import React, { Suspense } from "react";

const LazyApexChart = React.lazy(() => import("./LazyApexChart"));

const PieDonutChart = ({
  series = [],
  labels = [],
  colors = [],
  height = 220,
  isCountVisible = true,
}) => {
  // const totalAmount = countTotal
  //   ? countTotal
  //   : series.reduce((acc, val) => acc + val, 0);

  const options = {
    chart: {
      type: "donut",
    },
    labels: labels,
    colors: colors,
    legend: {
      position: "right",
      fontSize: "14px",
      markers: {
        width: 12,
        height: 12,
        radius: 12,
      },
      formatter: function (seriesName, opts) {
        const index = opts.seriesIndex;
        return isCountVisible
          ? `${seriesName} <span class="text-[12px] font-bold m-2 rounded-sm p-1 bg-[#FEEFC7] text-[#F79009] whitespace-nowrap">${series[index]}</span>`
          : seriesName;
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      enabled: false,
    },
    // plotOptions: {
    //   pie: {
    //     donut: {
    //       labels: {
    //         show: false,
    //       },
    //     },
    //   },
    // },
    plotOptions: {
      pie: {
        donut: {
          labels: {
            show: true,
            total: {
              show: true,
              label: "Total",
              fontSize: "16px",
              fontWeight: 600,
              color: "#333",
              formatter: function (w) {
                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
              },
            },
          },
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: {
            width: 200,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <Suspense fallback={<div>Loading chart...</div>}>
      <LazyApexChart
        options={options}
        series={series}
        type="donut"
        height={height}
      />
    </Suspense>
  );
};

export default PieDonutChart;
