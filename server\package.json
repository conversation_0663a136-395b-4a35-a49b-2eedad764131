{"name": "server", "version": "1.0.0", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node main.js", "dev": "nodemon main.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.804.0", "@aws-sdk/s3-request-presigner": "^3.804.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.2", "nodemon": "^3.1.10", "short-unique-id": "^5.2.2", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "xlsx": "^0.18.5"}}