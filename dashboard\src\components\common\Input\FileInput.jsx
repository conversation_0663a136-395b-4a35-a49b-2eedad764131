import React, { useRef } from "react";
import { X, Eye, FileText } from "lucide-react";

const FileInput = ({
  label,
  name,
  value,
  onChange,
  onRemove,
  onPreview,
  required = false,
  maxSizeMB = 10,
  errorMessage,
  initialFileName = "",
  hideFileDisplay = false,
  hasFile: hasFileProp, // Accept hasFile prop from parent
}) => {
  const fileInputRef = useRef(null);

  // Enhanced logic to handle both existing file objects and new File objects
  const isExistingFile = value && typeof value === "object" && value.isExisting;
  const isNewFile =
    value &&
    (value instanceof File || (typeof value === "object" && !value.isExisting));
  const hasFile =
    hasFileProp !== undefined
      ? hasFileProp
      : isExistingFile || isNewFile || (!value && initialFileName);

  const handleRemove = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    onRemove();
  };

  const handlePreview = () => {
    if (onPreview) {
      onPreview();
    }
  };

  // Helper function to clean timestamp prefixes from filenames
  const cleanFileName = (fileName) => {
    if (!fileName) return "";

    // Remove timestamp prefix pattern (e.g., "1750758616591-filename.pdf" -> "filename.pdf")
    const timestampPattern = /^\d{10,}-(.+)$/;
    const match = fileName.match(timestampPattern);

    if (match) {
      return match[1];
    }

    // Extract filename from URL/path if it contains slashes
    const pathParts = fileName.split("/");
    const baseFileName = pathParts[pathParts.length - 1];

    // Try to remove timestamp from the base filename as well
    const baseMatch = baseFileName.match(timestampPattern);
    return baseMatch ? baseMatch[1] : baseFileName;
  };

  const getFileInfo = () => {
    if (isExistingFile) {
      const cleanName = value.name ? cleanFileName(value.name) : "Unknown file";
      return {
        name: cleanName,
        type: "existing",
        badge: "Uploaded",
      };
    } else if (isNewFile) {
      const fileName =
        value instanceof File ? value.name : value.name || "Unknown file";
      const cleanName = cleanFileName(fileName);
      return {
        name: cleanName,
        type: "new",
        badge: "New",
      };
    } else if (initialFileName) {
      // Handle legacy initialFileName or use it as display name from parent
      const cleanName = cleanFileName(initialFileName);
      return {
        name: cleanName,
        type: "initial",
        badge: "Uploaded",
      };
    }
    return null;
  };

  const fileInfo = getFileInfo();

  const handleFileChange = (event) => {
    // Pass the event directly to parent's onChange handler
    if (onChange) {
      onChange(event);
    }
  };

  const getBadgeStyles = (type) => {
    const baseStyles =
      "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ml-2";
    switch (type) {
      case "existing":
      case "initial":
        return `${baseStyles} bg-green-100 text-green-800`;
      case "new":
        return `${baseStyles} bg-blue-100 text-blue-800`;
      default:
        return `${baseStyles} bg-gray-100 text-gray-800`;
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 block">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      <div className="relative">
        <input
          type="file"
          name={name}
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
          onChange={handleFileChange}
          ref={fileInputRef}
          className={`block w-full text-sm text-gray-700 border border-[#CBD4E1] rounded-md file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
          style={
            hasFile
              ? {
                  color: "transparent",
                  backgroundColor: "transparent",
                }
              : {}
          }
        />
        {hasFile && fileInfo && (
          <div className="absolute inset-0 flex items-center px-3 pointer-events-none bg-white rounded-md border border-[#CBD4E1]">
            <FileText className="text-gray-400 mr-2" size={16} />
            <span className="text-sm text-gray-600 truncate flex-1">
              {fileInfo.name}
            </span>
            <span
              className={`${getBadgeStyles(fileInfo.type)} pointer-events-none`}
            >
              {fileInfo.badge}
            </span>
          </div>
        )}
      </div>

      {/* Enhanced file display with badges - always show if file exists */}
      {hasFile && !hideFileDisplay && fileInfo && (
        <div className="mt-3 inline-flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 w-full">
          <div className="flex items-center flex-1 min-w-0">
            <FileText className="text-gray-500 mr-3 flex-shrink-0" size={18} />
            <div className="flex flex-col min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <span
                  className="text-sm text-gray-800 truncate font-medium"
                  title={fileInfo.name}
                >
                  {fileInfo.name}
                </span>
                <div className="flex items-center ml-2">
                  <span className={getBadgeStyles(fileInfo.type)}>
                    {fileInfo.badge}
                  </span>
                </div>
              </div>
              {isNewFile && value instanceof File && (
                <span className="text-xs text-gray-500 mt-1">
                  {(value.size / 1024).toFixed(1)} KB
                </span>
              )}
            </div>
          </div>

          <div className="ml-3 flex items-center space-x-2">
            {onPreview && (
              <button
                type="button"
                onClick={handlePreview}
                className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors duration-200"
                aria-label="Preview file"
                title="Preview file"
              >
                <Eye size={16} />
              </button>
            )}
            <button
              type="button"
              onClick={handleRemove}
              className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors duration-200"
              aria-label="Remove file"
              title="Remove file"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="flex items-center mt-2 text-red-600 text-sm">
          <X size={14} className="mr-1" />
          {errorMessage}
        </div>
      )}
    </div>
  );
};

export default FileInput;
