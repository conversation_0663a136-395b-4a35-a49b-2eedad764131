const mongoose = require("mongoose");
const UserCoinBalance = require("../models/UserCoinBalance");
const CoinTransaction = require("../models/CoinTransaction");

exports.getCoinHistory = async (req, res) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID not found in request",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const pipeline = [
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [
            { $skip: skip },
            { $limit: limit },
            {
              $lookup: {
                from: "jobs",
                localField: "relatedJobId",
                foreignField: "_id",
                as: "relatedJob",
              },
            },
            {
              $unwind: {
                path: "$relatedJob",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                amount: 1,
                balanceBefore: 1,
                balanceAfter: 1,
                transactionType: 1,
                earnedType: 1,
                spendType: 1,
                createdAt: 1,
                relatedSubmissionId: 1,
                relatedJobId: {
                  _id: "$relatedJob._id",
                  jobTitle: "$relatedJob.jobTitle",
                  jobId: "$relatedJob.jobId",
                },
              },
            },
          ],
        },
      },
    ];

    const [results] = await CoinTransaction.aggregate(pipeline);

    const transactions = results.data || [];
    const total = results.metadata[0]?.total || 0;
    const totalPages = Math.ceil(total / limit);

    const balanceDoc = await UserCoinBalance.findOne({ userId });

    return res.status(200).json({
      success: true,
      data: {
        transactions,
        balance: balanceDoc?.currentBalance || 0,
        totalEarned: balanceDoc?.totalEarned || 0,
        totalSpent: balanceDoc?.totalSpent || 0,
        total,
        page,
        totalPages,
      },
    });
  } catch (err) {
    console.error("Error fetching coin history:", err);
    return res.status(500).json({
      success: false,
      message: err.message || "Server error while fetching coin history",
    });
  }
};
