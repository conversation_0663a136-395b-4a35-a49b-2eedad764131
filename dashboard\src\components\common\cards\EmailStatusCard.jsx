import React from "react";
import AppButton from "../Button/AppButton";

const EmailStatusCard = ({
  image,
  title,
  message,
  buttonText,
  onClick,
  label,
  footer,
}) => {
  return (
    <div className="w-screen h-screen bg-gradient-to-br from-white to-gray-50 flex items-center justify-center relative overflow-hidden">
      {/* Background Image */}
      <img
        src="/assets/images/Background.svg"
        alt="background lines"
        className="absolute inset-0 w-full h-full object-cover opacity-80 pointer-events-none z-0"
        draggable={false}
      />

      {/* Card */}
      <div className="relative z-10 bg-white shadow-xl rounded-2xl px-8 py-10 max-w-md w-full text-center">
        <img
          src={image} // Assuming `image` is passed as a prop with a public path
          alt="status icon"
          className="mx-auto mb-6 w-24 h-24 object-contain"
        />
        <h4 className="text-0.5xl font-semibold text-gray-800 mb-3">{title}</h4>
        <p className="text-gray-600 mb-3 text-sm">{message}</p>
        {label && (
          <AppButton text={buttonText} onClick={onClick} label={label} />
        )}
      </div>
      {footer && (
        <footer className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-xs text-gray-500">
          Trouble signing in? Reach out to <NAME_EMAIL>.
        </footer>
      )}
    </div>
  );
};

export default EmailStatusCard;
