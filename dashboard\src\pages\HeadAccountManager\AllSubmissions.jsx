import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import PaginationFooter from "../../components/common/Table/TableFooter";
import TableHeader from "../../components/common/Table/TableHeader";
import ColorSelectionInput from "../../components/common/Input/ColorSelectionInput";
import { getAllSubmissions } from "../../services/operations/headAccountAPI";
import ThreeDot from "../../components/common/Button/ThreeDot";
import EmptyState from "../../components/common/EmptyState/EmptyState";
import { useSelector } from "react-redux";
import { submissionsStatusAndTimeline } from "../../services/operations/candidateAPI";
import { JobStatusOptions } from "../../utils/JobStatusOptions";
import ViewOptionsDropdown from "../../components/common/filter/ViewOptionsDropdown";
import FiltersPanel from "../../components/common/filter/FiltersPanel";
import SortingPanel from "../../components/common/filter/SortingPanel";
import { useURLState } from "../../components/common/filter/useURLState";
import SearchDropdown from "../../components/common/filter/SearchDropdown";

const AllSubmissions = () => {
  const [activeTab, setActiveTab] = useState(0);
  const tab = [{ name: "All Submissions" }];
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [submissions, setSubmissions] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();

  // Refs to track previous values and prevent unnecessary calls
  const prevFiltersRef = useRef("");
  const prevSortingRef = useRef("");
  const prevSearchRef = useRef("");
  const isInitialMount = useRef(true);
  const fetchTimeoutRef = useRef(null);

  // URL State Management
  const { parseURLParams, updateURLParams } = useURLState();
  const initialURLState = parseURLParams();

  // Search and Filter States
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const [searchData, setSearchData] = useState(
    initialURLState.search || {
      searchTerm: "",
      searchField: "all",
    }
  );
  const [active, setActive] = useState(initialURLState.activeTab || 0);

  // View Options and Filter States
  const [showDropdown, setShowDropdown] = useState(false);
  const [showFilters, setShowFilters] = useState(
    initialURLState.showFilters || false
  );
  const [showSorting, setShowSorting] = useState(
    initialURLState.showSorting || false
  );

  // Filter and Sorting States
  const [filters, setFilters] = useState(
    initialURLState.filters || {
      status: [],
      recruiterName: [],
      jobTitle: [],
    }
  );

  const [sorting, setSorting] = useState(() => {
    // Convert URL sorting back to UI format
    if (initialURLState.sorting.submissionDate) {
      return initialURLState.sorting.submissionDate === "recent"
        ? "Recent"
        : "Oldest";
    } else if (initialURLState.sorting.sortBy) {
      const { sortBy, sortOrder } = initialURLState.sorting;
      if (sortBy === "name") {
        return sortOrder === "asc" ? "A to Z" : "Z to A";
      } else if (sortBy === "candidateID") {
        return sortOrder === "asc" ? "Low to High" : "High to Low";
      } else {
        return `${sortBy}_${sortOrder}`;
      }
    }
    return "";
  });

  // REMOVED: refreshTrigger state - this was causing double calls

  // Available filters and sort options for submissions
  const availableFilters = ["status"];
  const availableSortOptions = ["submissionDate"];

  const columns = [
    { label: "Candidate ID", key: "candidateID" },
    { label: "Name", key: "name" },
    { label: "Recruiter Name", key: "recruiterName" },
    { label: "Job ID", key: "jobId" },
    { label: "Job Title", key: "jobTitle" },
    { label: "Status", key: "status" },
    { label: "", key: "actions" },
  ];

  // Clean filters to remove empty arrays
  const cleanFilters = (filtersObj) => {
    const cleaned = {};
    Object.keys(filtersObj).forEach((key) => {
      const value = filtersObj[key];
      if (Array.isArray(value) && value.length > 0) {
        cleaned[key] = value;
      } else if (typeof value === "string" && value.trim()) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  };

  // Check if there are any active filters or search
  const hasActiveFiltersOrSearch = () => {
    const hasFilters = Object.values(filters).some((filter) =>
      Array.isArray(filter) ? filter.length > 0 : filter
    );
    const hasSearch =
      searchData.searchTerm && searchData.searchTerm.trim() !== "";
    const hasSorting = sorting && sorting !== "";

    return hasFilters || hasSearch || hasSorting;
  };

  const updateSubmissionStatus = async (submissionId, newStatus) => {
    try {
      await submissionsStatusAndTimeline({
        newstatus: newStatus,
        submissionId: submissionId,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  // FIXED: Main API call function with debouncing
  const getData = async (
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) => {
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Debounce the API call
    fetchTimeoutRef.current = setTimeout(async () => {
      try {
        setLoading(true);

        // Convert sorting string to params object
        let sortingParams = {};
        if (appliedSorting) {
          switch (appliedSorting) {
            case "Recent":
              sortingParams = { submissionDate: "recent" };
              break;
            case "Oldest":
              sortingParams = { submissionDate: "oldest" };
              break;
            case "A to Z":
              sortingParams = { sortBy: "name", sortOrder: "asc" };
              break;
            case "Z to A":
              sortingParams = { sortBy: "name", sortOrder: "desc" };
              break;
            case "Low to High":
              sortingParams = { sortBy: "candidateID", sortOrder: "asc" };
              break;
            case "High to Low":
              sortingParams = { sortBy: "candidateID", sortOrder: "desc" };
              break;
            case "name_asc":
              sortingParams = { sortBy: "name", sortOrder: "asc" };
              break;
            case "name_desc":
              sortingParams = { sortBy: "name", sortOrder: "desc" };
              break;
            case "candidateID_asc":
              sortingParams = { sortBy: "candidateID", sortOrder: "asc" };
              break;
            case "candidateID_desc":
              sortingParams = { sortBy: "candidateID", sortOrder: "desc" };
              break;
            case "jobTitle_asc":
              sortingParams = { sortBy: "jobTitle", sortOrder: "asc" };
              break;
            case "jobTitle_desc":
              sortingParams = { sortBy: "jobTitle", sortOrder: "desc" };
              break;
            default:
              if (appliedSorting.includes("_")) {
                const [sortBy, sortOrder] = appliedSorting.split("_");
                sortingParams = { sortBy, sortOrder };
              }
          }
        }

        // Clean filters before sending
        const cleanedFilters = cleanFilters(appliedFilters);

        const response = await getAllSubmissions(
          page,
          limit,
          user.role,
          cleanedFilters,
          sortingParams,
          appliedSearch
        );
        const results = response?.results || [];

        const mapped = results.map((submission) => ({
          id: submission._id,
          submissionId: submission.submissionId,
          candidateID: submission.candidate?.candidateID || "—",
          name:
            `${submission.candidate?.personalDetails?.firstName || ""} ${
              submission.candidate?.personalDetails?.lastName || ""
            }`.trim() || "—",
          recruiterName:
            `${submission.recruiter?.name?.firstName || ""} ${
              submission.recruiter?.name?.lastName || ""
            }`.trim() || "—",
          jobId: submission.job?.jobId || "—",
          jobTitle: submission.job?.jobTitle || "—",
          status: submission.status || "submitted",
          actions: (
            <ThreeDot
              dropdownSize="w-32"
              buttonDropDown={
                <ul className="py-0" role="none">
                  <li>
                    <div
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(
                          `/submissions/submissionDetails?candidateId=${submission?.candidate?.candidateID}&submissionId=${submission?.submissionId}`
                        );
                      }}
                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      role="menuitem"
                    >
                      <img src="/assets/icons/view.svg" alt="View" />
                      View
                    </div>
                  </li>
                </ul>
              }
            />
          ),
        }));

        setSubmissions(mapped);
        setTotalResults(response?.totalSubmissions || 0);
        setTotalPages(response?.totalPages || 1);
      } catch (err) {
        console.error("Error fetching submissions:", err);
        setSubmissions([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms debounce
  };

  // Search handlers
  const handleSearchClick = () => {
    setShowSearchDropdown(!showSearchDropdown);
  };

  const handleSearchClose = () => {
    setShowSearchDropdown(false);
  };

  // FIXED: Remove refreshTrigger usage
  const handleSearch = (searchInputs) => {
    setSearchData(searchInputs);
    if (currentPage !== 1) {
      setCurrentPage(1); // This will trigger the useEffect
    } else {
      // If already on page 1, manually call getData
      getData(1, pageSize, filters, sorting, searchInputs);
    }
  };

  // Filter and Sorting handlers
  const handleFilterChange = (filterType, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [filterType]: value,
    }));
  };

  const handleSortingChange = (sortValue) => {
    setSorting(sortValue);
  };

  // FIXED: Clear filters without refreshTrigger
  const handleClearAllFilters = () => {
    const clearedFilters = { status: [], recruiterName: [], jobTitle: [] };
    const clearedSearch = { searchTerm: "", searchField: "all" };

    setFilters(clearedFilters);
    setSorting("");
    setSearchData(clearedSearch);
    
    if (currentPage !== 1) {
      setCurrentPage(1); // Will trigger useEffect
    } else {
      getData(1, pageSize, clearedFilters, "", clearedSearch);
    }
  };

  // FIXED: Apply filters without refreshTrigger
  const handleApplyFilters = () => {
    setShowDropdown(false);
    setShowSearchDropdown(false);
    
    if (currentPage !== 1) {
      setCurrentPage(1); // Will trigger useEffect with current state
    } else {
      getData(1, pageSize, filters, sorting, searchData);
    }
  };

  // FIXED: Single useEffect for pagination changes only
  useEffect(() => {
    getData(currentPage, pageSize, filters, sorting, searchData);
  }, [currentPage, pageSize]);

  // FIXED: Smart useEffect for filters, sorting, search changes
  useEffect(() => {
    const filtersStr = JSON.stringify(filters);
    const searchStr = JSON.stringify(searchData);

    // Check if values actually changed
    const filtersChanged = filtersStr !== prevFiltersRef.current;
    const sortingChanged = sorting !== prevSortingRef.current;
    const searchChanged = searchStr !== prevSearchRef.current;

    // Skip if nothing changed and not initial mount
    if (
      !isInitialMount.current &&
      !filtersChanged &&
      !sortingChanged &&
      !searchChanged
    ) {
      return;
    }

    // Update refs
    prevFiltersRef.current = filtersStr;
    prevSortingRef.current = sorting;
    prevSearchRef.current = searchStr;

    // Reset to page 1 when filters change (but not on initial mount)
    if (
      !isInitialMount.current &&
      (filtersChanged || sortingChanged || searchChanged)
    ) {
      if (currentPage !== 1) {
        setCurrentPage(1); // Will trigger pagination useEffect
      } else {
        getData(1, pageSize, filters, sorting, searchData);
      }
    } else if (isInitialMount.current) {
      // Initial fetch
      getData(currentPage, pageSize, filters, sorting, searchData);
      isInitialMount.current = false;
    }
  }, [filters, sorting, searchData, pageSize]);

  // Update URL when state changes (keep this separate)
  useEffect(() => {
    const sortingForURL = {};
    if (sorting) {
      if (sorting === "Recent") {
        sortingForURL.submissionDate = "recent";
      } else if (sorting === "Oldest") {
        sortingForURL.submissionDate = "oldest";
      } else if (sorting.includes("_")) {
        const [sortBy, sortOrder] = sorting.split("_");
        sortingForURL.sortBy = sortBy;
        sortingForURL.sortOrder = sortOrder;
      }
    }

    updateURLParams({
      activeTab: active,
      filters: filters,
      sorting: sortingForURL,
      search: searchData.searchTerm ? searchData : {},
      showFilters: showFilters,
      showSorting: showSorting,
    });
  }, [
    active,
    filters,
    sorting,
    searchData,
    showFilters,
    showSorting,
    updateURLParams,
  ]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  // Handle tab switching
  const handleTabChange = (newActiveTab) => {
    setActiveTab(newActiveTab);
    setActive(newActiveTab);
  };

  return (
    <div className="flex flex-col min-h-screen pb-12">
      <TabNav
        nav={tab}
        active={activeTab}
        setActive={handleTabChange}
        rightSidebar={
          <div className="flex gap-3">
            <div className="relative">
              <img
                src="/assets/icons/search.svg"
                alt="Search"
                className="w-6 h-8 cursor-pointer"
                onClick={handleSearchClick}
              />
              <SearchDropdown
                isOpen={showSearchDropdown}
                onClose={handleSearchClose}
                onSearch={handleSearch}
                activeTab="submissions"
              />
            </div>

            <ViewOptionsDropdown
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              setShowFilters={setShowFilters}
              setShowSorting={setShowSorting}
              showFilters={showFilters}
              showSorting={showSorting}
            />
          </div>
        }
      />

      <FiltersPanel
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        filters={filters}
        handleFilterChange={handleFilterChange}
        handleClearAllFilters={handleClearAllFilters}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableFilters={availableFilters}
        pageType="submissions"
      />

      <SortingPanel
        showSorting={showSorting}
        setShowSorting={setShowSorting}
        sorting={sorting}
        setSorting={handleSortingChange}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableSortOptions={availableSortOptions}
      />

      {/* Show loading state */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-2 text-gray-600">Loading submissions...</span>
        </div>
      )}

      {/* Show content based on data availability */}
      {!loading && (
        <>
          {submissions.length > 0 ? (
            <div className="flex flex-col mb-16">
              <div className="flex flex-col">
                <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
                  <table className="min-w-full bg-white">
                    <TableHeader columns={columns.map((col) => col.label)} />
                    <tbody>
                      {submissions?.map((sub, index) => (
                        <tr
                          key={sub.id || index}
                          className="cursor-pointer border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
                          onClick={(e) => {
                            e.preventDefault();
                            navigate(
                              `/submissions/submissionDetails?candidateId=${sub?.candidateID}&submissionId=${sub?.submissionId}`
                            );
                          }}
                        >
                          {columns.map((col) => {
                            if (col.key === "status") {
                              return (
                                <td className="px-4 py-3" key={col.key}>
                                  <ColorSelectionInput
                                    value={sub.status}
                                    onChange={(newStatus) => {
                                      const updated = [...submissions];
                                      updated[index] = {
                                        ...updated[index],
                                        status: newStatus,
                                      };
                                      setSubmissions(updated);
                                      updateSubmissionStatus(sub.id, newStatus);
                                    }}
                                    options={JobStatusOptions}
                                    disabled={true}
                                    width="w-44"
                                  />
                                </td>
                              );
                            } else {
                              return (
                                <td className="px-4 py-3" key={col.key}>
                                  {sub[col.key] || "—"}
                                </td>
                              );
                            }
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="bg-white">
                  <PaginationFooter
                    currentPage={currentPage}
                    totalPages={totalPages}
                    pageSize={pageSize}
                    setPageSize={setPageSize}
                    setCurrentPage={setCurrentPage}
                    totalResults={totalResults}
                  />
                </div>
              </div>
            </div>
          ) : (
            <EmptyState
              title={
                hasActiveFiltersOrSearch()
                  ? "No Submissions Match Your Criteria"
                  : "No Submissions Found"
              }
              description={
                hasActiveFiltersOrSearch()
                  ? "No submissions match your current search criteria or filters. Try adjusting your search or clearing filters to see all submissions."
                  : "There are no candidate submissions to display at the moment. Submissions will appear here once candidates are submitted to jobs."
              }
              showClearButton={hasActiveFiltersOrSearch()}
              onClearFilters={handleClearAllFilters}
            />
          )}
        </>
      )}
    </div>
  );
};

export default AllSubmissions;