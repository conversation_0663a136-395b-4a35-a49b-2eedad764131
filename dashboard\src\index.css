@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@import "tailwindcss";

body {
  font-family: "Inter", sans-serif;
}

.toast-container-top-center {
  top: 1rem !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 600px;
  z-index: 9999 !important;
}

.custom-toast {
  transition: all 0.6s ease-in-out !important; /* Fade and slide duration */
  opacity: 1;
}

.react-datepicker {
  display: flex !important;
  flex-direction: row !important;
  align-items: stretch !important;
}

.react-datepicker-wrapper {
  width: 100% !important;
}

.react-datepicker__month-container {
  height: 276px !important; /* consistent base height */
}

.react-datepicker__time-container {
  height: 276px !important; /* force match calendar */
  width: 90px !important;
  overflow: hidden !important;
}

.react-datepicker__time {
  height: 100% !important;
}

.react-datepicker__time-box {
  height: 100% !important;
  overflow-y: auto !important;
}

.react-datepicker__time-list {
  height: 100% !important;
  max-height: 100% !important;
  overflow-y: auto !important;
  padding-top: 0 !important;
}

.hide-number-arrows::-webkit-outer-spin-button,
.hide-number-arrows::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.hide-number-arrows {
  -moz-appearance: textfield;
}
