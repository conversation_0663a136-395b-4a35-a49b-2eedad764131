import React from "react";

const RecruitersStatsCard = ({ stats }) => {
  const statItems = [
    { label: "Jobs working On", value: stats.jobsWorkingOn },
    { label: "Total Submission", value: stats.totalSubmission },
    { label: "Job Covered", value: stats.jobCovered },
    { label: "Submission to Interview", value: stats.submissionToInterview },
    { label: "Interview to Offer", value: stats.interviewToOffer },
  ];

  return (
    <div className="bg-[#F7F7F7] rounded-b-md shadow p-4">
      <div className="text-sm font-bold text-gray-900 mb-2">Stats</div>
      <div className="flex justify-between items-stretch">
        {statItems.map((item, idx) => (
          <div
            key={idx}
            className={`flex-1 ${idx !== 0 ? "border-l border-gray-200" : ""} ${
              idx === 0 ? "pl-0" : "pl-4"
            } text-left`}
          >
            <div className="text-xl font-bold text-gray-900">{item.value}</div>
            <div className="text-sm text-gray-500">{item.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecruitersStatsCard;
