import { ChevronLeft, ChevronRight } from "lucide-react";

export default function PaginationFooter({
  currentPage,
  totalPages,
  pageSize,
  setPageSize,
  setCurrentPage,
  totalResults,
}) {
  const pages = Array.from({ length: totalPages }, (_, i) => i + 1);

  return (
    <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200 bg-white">
      {/* Page Size Dropdown */}
      <div className="text-sm text-gray-600 flex items-center gap-2">
        <span>Showing</span>
        <select
          className="border rounded px-2 py-1 text-sm"
          value={pageSize}
          onChange={(e) => setPageSize(Number(e.target.value))}
        >
          {[10, 25, 50].map((size) => (
            <option key={size}>{size}</option>
          ))}
        </select>
        <span>from {totalResults} results</span>
      </div>

      {/* Pagination Buttons */}
      <div className="flex items-center space-x-2">
        <button
          disabled={currentPage === 1}
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          className="p-1.5 disabled:opacity-50"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        {pages.map((page) => (
          <button
            key={page}
            onClick={() => setCurrentPage(page)}
            className={`w-6 h-6 text-sm  ${
              currentPage === page
                ? "bg-green-700 text-white rounded-full border"
                : "text-gray-700 "
            }`}
          >
            {page}
          </button>
        ))}

        <button
          disabled={currentPage === totalPages}
          onClick={() =>
            setCurrentPage((prev) => Math.min(prev + 1, totalPages))
          }
          className="p-1.5  disabled:opacity-50"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
