import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import {
  getCandidateTimeline,
  submissionsStatusAndTimeline,
} from "../../services/operations/candidateAPI";
import AppButton from "../../components/common/Button/AppButton";
import Modal from "../../components/common/Modal/Modal";
import ColorSelectionInput from "../../components/common/Input/ColorSelectionInput";
import { useFormik } from "formik";
import CalendarDropDownField from "../../components/common/Input/CalenderDropDownField";
import { toast } from "react-toastify";
import { JobStatusOptions } from "../../utils/JobStatusOptions";
import InputField from "../../components/common/Input/InputField";
import { createPayout } from "../../services/operations/payoutAPI";

const statusStyles = {
  hired: "bg-green-100 text-green-600",
  reviewing: "bg-yellow-100 text-yellow-600",
  interviewing: "bg-purple-100 text-purple-600",
  "invitation sent": "bg-orange-100 text-orange-600",
  "advanced to offers": "bg-yellow-100 text-yellow-700",
  "confirmed submission": "bg-blue-100 text-blue-600",
};

const columns = [
  { label: "Date", key: "eventDate" },
  { label: "Status", key: "status" },
  { label: "Note", key: "notes" },
  { label: "", key: "action" },
];

const Timeline = ({ submissionId, isSidebar = false, header }) => {
  const [timeline, setTimeline] = useState([]);
  const [searchParams] = useSearchParams();
  const submissionID = searchParams.get("submissionId");

  const [modalOpen, setModalOpen] = useState(false);

  const formik = useFormik({
    initialValues: {
      status: "",
      date: null,
      commission: "",
      payRateAmount: "",
      guaranteePeriod: "",
      notes: "",
    },
    validate: (data) => {
      let errors = {};

      if (!data.date) {
        errors.date = "Date is required.";
      }

      if (data.status === "hired-under guarantee period") {
        if (!data.commission) {
          errors.commission = "Commission is required when status is hired.";
        }
        if (!data.payRateAmount) {
          errors.payRate = "Pay rate is required when status is hired.";
        }
        if (!data.guaranteePeriod) {
          errors.guaranteePeriod =
            "Guarantee period is required when status is hired.";
        }
      }

      return errors;
    },
    onSubmit: (data) => {
      console.log(data);
      const submitData = {
        newstatus: data.status,
        submissionId: submissionID,
        notes: data.notes ? data.notes : "-",
        eventdate: data.date,
      };

      // Add hired-specific fields if status is hired
      if (data.status === "hired-under guarantee period") {
        submitData.commission = data.commission;
        submitData.payRateAmount = data.payRateAmount;
        submitData.guaranteePeriod = data.guaranteePeriod;
      }

      addTimeline(submitData);
    },
  });
  async function apiCall(ID) {
    try {
      const candidate = await getCandidateTimeline(ID);
      formik.setFieldValue("status", candidate.data[0]?.status);
      setTimeline(candidate.data);
    } catch (error) {
      console.log(error);
    }
  }

  async function addTimeline(data) {
    try {
      // 1. Create submission log (timeline)
      await submissionsStatusAndTimeline(data);

      // 2. If hired-under guarantee period, also create payout
      if (data.newstatus === "hired-under guarantee period") {
        console.log("Creating payout with data:", data);
        await createPayout({
          submissionId: data.submissionId,
          guaranteePeriod: data.guaranteePeriod,
          payRateAmount: data.payRateAmount,
          commission: data.commission,
        });
      }

      formik.resetForm();
      setModalOpen(false);
      apiCall(data.submissionId);
    } catch (err) {
      toast.error(err?.response?.data?.message || "Something went wrong");
      console.log(err);
    }
  }
  const isFormFieldValid = (name) =>
    !!(formik.touched[name] && formik.errors[name]);
  const getFormErrorMessage = (name) => {
    return (
      isFormFieldValid(name) && (
        <small className="text-red-500">{formik.errors[name]}</small>
      )
    );
  };

  useEffect(() => {
    if (submissionID) {
      apiCall(submissionID);
    }
    if (submissionId) {
      apiCall(submissionId);
    }
  }, [submissionID, submissionId]);

  return (
    <div className="mt-4 w-full mx-auto p-2 space-y-6">
      {/* Button */}
      {isSidebar && <div className="my-2">{header}</div>}
      {!isSidebar && (
        <div className="flex justify-center">
          <AppButton
            label={"+ Add New Timeline"}
            variant="secondary"
            css={
              "py-[5px] px-6 rounded-md border border-gray-400 text-gray-600 hover:bg-[#3E9900] hover:text-white cursor-pointer"
            }
            onClick={() => {
              setModalOpen(true);
            }}
          />
        </div>
      )}

      {/* Modal */}
      <Modal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Add New Timeline"
        className="w-[900px] max-w-full"
      >
        <form
          className="space-y-4 px-6 pb-4 pt-2"
          onSubmit={formik.handleSubmit}
        >
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-800 mb-1">
                Status
              </label>
              <ColorSelectionInput
                value={formik.values.status}
                onChange={(e) => {
                  formik.setFieldValue("status", e);
                }}
                options={JobStatusOptions}
                width="w-full p-1"
              />
              {getFormErrorMessage("status")}
            </div>
            <div>
              <CalendarDropDownField
                name="date"
                label="Date"
                required={true}
                placeholder="YYYY-MM-DD"
                errorMessage={getFormErrorMessage("date")}
                onChange={(date) => {
                  if (date) {
                    const formatted = date.toISOString();
                    formik.setFieldValue("date", formatted);
                  }
                }}
                values={formik.values.date}
                minDate={null}
                maxDate={null}
                showTimeSelect={false}
                width="w-[235px] "
              />
            </div>
          </div>

          {formik.values.status === "hired-under guarantee period" && (
            <div className="grid grid-cols-2 gap-4">
              <InputField
                name="commission"
                label="Commission"
                value={formik.values.commission}
                onChange={(e) => {
                  formik.setFieldValue("commission", e.target.value);
                }}
                placeholder="Enter Commission"
                errorMessage={getFormErrorMessage("commission")}
                required={true}
              />
              <InputField
                name="payRateAmount"
                label="Pay Rate"
                value={formik.values.payRateAmount}
                onChange={(e) => {
                  formik.setFieldValue("payRateAmount", e.target.value);
                }}
                placeholder="Enter Pay Rate"
                errorMessage={getFormErrorMessage("payRateAmount")}
                required={true}
              />
              <InputField
                name="guaranteePeriod"
                label="Guarantee Period"
                value={formik.values.guaranteePeriod}
                onChange={(e) => {
                  formik.setFieldValue("guaranteePeriod", e.target.value);
                }}
                placeholder="Enter Guarantee Period"
                errorMessage={getFormErrorMessage("guaranteePeriod")}
                required={true}
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-800 mb-1">
              Note
            </label>
            <textarea
              name="notes"
              id="notes"
              value={formik.values.notes}
              onChange={formik.handleChange}
              rows={4}
              className="w-full border rounded px-3 py-2.5 text-sm"
              placeholder="Enter Note"
            />
            {getFormErrorMessage("notes")}
          </div>

          <div className="flex justify-end gap-2 pt-2">
            <AppButton
              type="button"
              label="Cancel"
              variant="secondary"
              className="px-5 py-2 text-sm rounded-md"
              onClick={(e) => {
                e.preventDefault();
                setModalOpen(false);
              }}
            />
            <AppButton
              type="submit"
              label="Save"
              variant="primary"
              className="px-5 py-2 text-sm rounded-md"
            />
          </div>
        </form>
      </Modal>

      {/* Timeline with stepper and table */}
      <div className="flex gap-4 items-start">
        <div className="flex flex-col items-center pt-15">
          {timeline.map((_, index) => {
            const isTop = index === 0;
            const isLast = index === timeline.length - 1;
            return (
              <div key={index} className="flex flex-col items-center">
                {isTop ? (
                  <div className="w-5 h-5 rounded-full bg-[#419E6A] z-10" />
                ) : (
                  <img
                    src={"/assets/icons/Stepper.svg"}
                    alt="step"
                    className="w-5 h-5 z-10"
                  />
                )}
                {!isLast && (
                  <div
                    className="w-px bg-gray-300"
                    style={{ height: "23px" }}
                  />
                )}
              </div>
            );
          })}
        </div>

        <div className="flex-1 bg-white border border-gray-200 shadow-md rounded-lg overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50 text-left text-sm text-gray-700 font-bold">
              <tr>
                {columns.map((col) => (
                  <th key={col.key} className="px-6 py-3">
                    {col.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="text-sm text-gray-800">
              {timeline.map((item, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 hover:bg-gray-50"
                >
                  {columns.map((col) => {
                    if (col.key === "status") {
                      return (
                        <td className="px-6 py-3" key={col.key}>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${
                              statusStyles[item.status.toLowerCase()] ||
                              "bg-gray-100 text-gray-600"
                            }`}
                          >
                            {item.status}
                          </span>
                        </td>
                      );
                    }
                    if (col.key === "notes") {
                      return (
                        <td className="px-6 py-3" key={col.key}>
                          {item.notes || "—"}
                        </td>
                      );
                    }
                    if (col.key === "eventDate") {
                      return (
                        <td className="px-6 py-3" key={col.key}>
                          {`${new Date(item.eventDate).toLocaleString("en-US", {
                            month: "short",
                          })} ${new Date(item.eventDate).getDate()}, ${new Date(
                            item.eventDate
                          ).getFullYear()}` || "—"}
                        </td>
                      );
                    }
                    return (
                      <td className="px-6 py-3" key={col.key}>
                        {item[col.key]}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Timeline;
