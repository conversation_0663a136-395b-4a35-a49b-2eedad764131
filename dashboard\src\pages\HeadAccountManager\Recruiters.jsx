import { useState } from "react";
import EmptyState from "../../components/common/EmptyState";

const Recruiters = () => {
  // Real data state - starts empty, will be populated by API
  const [recruiters, setRecruiters] = useState([]);
  // not connected to api yet

  // You can add sample data for testing:
  // const [recruiters, setRecruiters] = useState([
  //   { id: 1, name: "<PERSON>", specialization: "Frontend" },
  //   { id: 2, name: "<PERSON>", specialization: "Backend" }
  // ]);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold text-gray-900 mb-6">Recruiters</h1>

      {/* Correct validation - check if data exists */}
      {recruiters.length > 0 ? (
        <div>
          {/* Show actual recruiters data */}
          {recruiters.map(recruiter => (
            <div key={recruiter.id} className="p-4 border rounded mb-2">
              {recruiter.name} - {recruiter.specialization}
            </div>
          ))}
        </div>
      ) : (
        <EmptyState
          title="No Recruiters Found"
          description="There are no recruiters to display at the moment. Recruiters will appear here once they are added to the system."
        />
      )}
    </div>
  );
};

export default Recruiters;
