import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";
import SelectField from "../../../../common/Input/SelectionField";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";

const yesNoOptions = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
];

const CertificationsForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
    setFieldValue,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  // Reset expiration fields if parent select is changed to "no"
  useEffect(() => {
    if (
      values.certification.blsCertification !== "yes" &&
      values.certification.blsExpiration
    ) {
      setFieldValue("certification.blsExpiration", "");
    }
  }, [
    values.certification.blsCertification,
    values.certification.blsExpiration,
    setFieldValue,
  ]);
  useEffect(() => {
    if (
      values.certification.aclsPalsNals !== "yes" &&
      values.certification.aclsPalsNalsExpiration
    ) {
      setFieldValue("certification.aclsPalsNalsExpiration", "");
    }
  }, [
    values.certification.aclsPalsNals,
    values.certification.aclsPalsNalsExpiration,
    setFieldValue,
  ]);

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <SelectField
        label="BLS Certification"
        name="certification.blsCertification"
        value={values.certification.blsCertification}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={yesNoOptions}
        errorMessage={
          touched.certification?.blsCertification &&
          errors.certification?.blsCertification
        }
      />
      {values.certification.blsCertification === "yes" && (
        <CalendarDropdownField
          label="BLS Expiration Date"
          name="certification.blsExpiration"
          // required={true}
          disabled={false}
          minDate={new Date()}
          // errorMessage={
          //   touched.certification?.blsExpiration &&
          //   errors.certification?.blsExpiration
          // }
        />
      )}
      <SelectField
        label="ACLS/PALS/NALS"
        name="certification.aclsPalsNals"
        value={values.certification.aclsPalsNals}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={yesNoOptions}
        errorMessage={
          touched.certification?.aclsPalsNals &&
          errors.certification?.aclsPalsNals
        }
      />

      {values.certification.aclsPalsNals === "yes" && (
        <CalendarDropdownField
          label="ACLS/PALS/NALS Expiration Date"
          name="certification.aclsPalsNalsExpiration"
          // required={true}
          disabled={false}
          minDate={new Date()}
          // errorMessage={
          //   touched.certification?.aclsPalsNalsExpiration &&
          //   errors.certification?.aclsPalsNalsExpiration
          // }
        />
      )}
      <InputField
        label="Other Relevant Certificates"
        name="certification.otherRelevantCertificate"
        value={values.certification.otherRelevantCertificate}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder="Enter other certificates"
      />
    </form>
  );
};

export default CertificationsForm;
