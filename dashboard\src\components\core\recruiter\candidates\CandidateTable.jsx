import React, { useEffect, useState } from "react";
import PaginationFooter from "../../../common/Table/TableFooter";
import TableHeader from "../../../common/Table/TableHeader";
import { useNavigate } from "react-router-dom";
import ColorSelectionInput from "../../../common/Input/ColorSelectionInput";
import ThreeDot from "../../../common/Button/ThreeDot";

const CandidateTable = ({
  getData,
  baseurl = "",
  columns = [],
  tableRow = "",
  dropdownItems = [],
  dropdownkey = "",
  dropdownwidth = "w-36",
  isDropDownDisable = false,
  updateData,
}) => {
  const navigate = useNavigate();
  const [tableData, setTableData] = useState([]);
  const safeTableData = Array.isArray(tableData) ? tableData : [];
  const paginatedData = safeTableData;

  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalResults, setTotalResults] = useState(0);
  const [allJobs, setAllJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  async function getAllData() {
    setIsLoading(true);
    try {
      const data = await getData(currentPage, pageSize);
      setTotalPages(data.totalPages);
      setTotalResults(data.totalResults);
      setTableData(data.resData || []); 
    } catch (error) {
      console.error("Error fetching data:", error);
      setTableData([]);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    getAllData();
  }, [currentPage, pageSize]); 

  return (
    <>
      <table className="min-w-full overflow-scroll bg-white">
        <TableHeader columns={columns} />
        <tbody>
          {paginatedData?.length < 1 && (
            <tr>
              <td
                colSpan={columns.length + 2}
                className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-center text-gray-800"
              >
                <div className="flex flex-col items-center justify-center py-8">
                  <img
                    src="/assets/images/No Record Found.svg"
                    alt="no record found"
                    className="mb-4"
                  />
                  <p className="text-gray-600">No Record Found</p>
                </div>
              </td>
            </tr>
          )}
          {paginatedData?.map((tableItem, key) => (
            <tr
              key={key}
              className="border-b border-[#E2E8F0] cursor-pointer hover:bg-gray-100 transition-colors duration-200 align-middle text-sm text-gray-800"
              onClick={() =>
                navigate(
                  `/candidate/candidatedetails?candidateId=${tableItem.id}${
                    tableItem?.submissionId
                      ? `&submissionId=${tableItem.submissionId}`
                      : ""
                  }`
                )
              }
            >

              {Object.keys(tableItem)
                .filter(
                  (filterItem) =>
                    !(filterItem == "id" || filterItem == "submissionId")
                )
                ?.map((item) => {
                  if (dropdownkey && dropdownkey == item) {
                    return (
                      <td>
                        <ColorSelectionInput
                          value={tableItem[dropdownkey]}
                          disabled={isDropDownDisable}
                          width={dropdownwidth}
                          onChange={(newStatus) => {
                            const updated = [...safeTableData];
                            updated[index] = {
                              ...updated[index],
                              [dropdownkey]: newStatus,
                            };
                            setAllJobs(updated);
                            updateData(tableItem.id, newStatus);
                          }}
                          options={dropdownItems}
                        />
                      </td>
                    );
                  }
                  return (
                    <>
                      <td className="px-4 py-3 text-start">
                        {tableItem[item]}
                      </td>
                    </>
                  );
                })}

              {tableRow && <td>{tableRow}</td>}

              {/* Three dots column - prevent row click */}
              <td
                className="px-2 py-2 text-start"
                onClick={(e) => e.stopPropagation()}
              >
                <ThreeDot
                  dropdownSize="w-32"
                  buttonDropDown={
                    <>
                      <ul className="py-0" role="none">
                        <li>
                          <div
                            onClick={(e) => {
                              e.preventDefault();
                              navigate(
                                `/candidate/candidatedetails?candidateId=${
                                  tableItem.id
                                }${
                                  tableItem?.submissionId
                                    ? `&submissionId=${tableItem.submissionId}`
                                    : ""
                                }`
                              );
                            }}
                            className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                            role="menuitem"
                          >
                            <img
                              src="/assets/icons/view.svg"
                              alt="View Submission"
                            />
                            View
                          </div>
                        </li>
                      </ul>
                    </>
                  }
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <PaginationFooter
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        totalResults={totalResults}
      />
    </>
  );
};

export default CandidateTable;
