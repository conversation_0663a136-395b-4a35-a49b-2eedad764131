import React, { useState, useRef, useEffect } from "react";

const FilterBar = ({ filters, onChange, onClear, onApply, rightAlignActions = true }) => {
  const [openIndex, setOpenIndex] = useState(null);
  const barRef = useRef(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClick(e) {
      if (barRef.current && !barRef.current.contains(e.target)) {
        setOpenIndex(null);
      }
    }
    if (openIndex !== null) {
      document.addEventListener("mousedown", handleClick);
    }
    return () => document.removeEventListener("mousedown", handleClick);
  }, [openIndex]);

  return (
    <div ref={barRef} className="flex flex-wrap items-center justify-between px-4 py-2 bg-white border-b border-gray-200">
      <div className="flex flex-wrap items-center gap-3">
        {filters.map((filter, idx) => (
          <div key={filter.label} className="relative">
            {/* The pill is the only clickable entry point for each filter. The dropdown should only show the selection UI, not another labeled field. */}
            <div
              className="border border-blue-200 bg-blue-50 rounded-full px-3 py-1 cursor-pointer flex items-center gap-1 text-blue-600 text-sm font-medium min-w-[180px]"
              onClick={() => setOpenIndex(openIndex === idx ? null : idx)}
            >
              <span>{filter.label}</span>
              {filter.selected && filter.selected.length > 0 ? (
                <span className="ml-1 truncate max-w-[120px]">: {
                  filter.label === "Experience Level"
                    ? filter.selected.join(" - ")
                    : filter.selected.join(", ")
                }</span>
              ) : (
                <span className="ml-1 text-blue-300">: {filter.placeholder}</span>
              )}
              <svg className="w-4 h-4 ml-1 text-blue-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
            </div>
            {openIndex === idx && (
              <div className="absolute left-0 mt-2 z-50 min-w-[220px]">
                {/* Clone the component and pass onClose prop */}
                {React.cloneElement(filter.component, {
                  onClose: () => setOpenIndex(null)
                })}
              </div>
            )}
          </div>
        ))}
      </div>
      {rightAlignActions && (
        <div className="flex items-center gap-2 ml-auto">
          <span className="text-blue-500 cursor-pointer text-xs" onClick={onClear}>
            Clear all
          </span>
          <button
            className="px-4 py-1 rounded bg-green-500 text-white text-sm font-medium hover:bg-green-600"
            onClick={onApply}
          >
            Apply
          </button>
        </div>
      )}
    </div>
  );
};

export default FilterBar;
