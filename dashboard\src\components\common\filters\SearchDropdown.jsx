import React, { useState, useEffect, useRef } from "react";
import TableLikeSearchSelector from "./TableLikeSearchSelector";

const SearchDropdown = ({ isOpen, onClose, onSearch, activeTab }) => {
  const [searchInputs, setSearchInputs] = useState(() => {
    const params = new URLSearchParams(window.location.search);
    const savedSearch = localStorage.getItem(`jobSearch_${activeTab}`);
    const savedData = savedSearch ? JSON.parse(savedSearch) : null;

    return {
      searchTerm: params.get("searchTerm") || savedData?.searchTerm || "",
      searchField: params.get("searchField") || savedData?.searchField || "all",
    };
  });

  // 🚫 Do NOT clear the search term when the dropdown closes.
  // The term should persist until the user explicitly clears it.
  // So this entire effect has been removed.
  //debounce

  // ✅ Save to URL and localStorage
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (searchInputs.searchTerm) {
      params.set("searchTerm", searchInputs.searchTerm);
      params.set("searchField", searchInputs.searchField);
    } else {
      params.delete("searchTerm");
      params.delete("searchField");
    }
    window.history.replaceState({}, "", `${window.location.pathname}?${params}`);
    localStorage.setItem(`jobSearch_${activeTab}`, JSON.stringify(searchInputs));
  }, [searchInputs, activeTab]);

  // ✅ Debounced onSearch
  // 1. Only fire when the user has typed something (non-empty term)
  // 2. ALSO fire when the user deletes everything (to clear search)
  // 3. Skip completely on dropdown mount/open to avoid the initial API call

  const lastTermRef = useRef("");

  useEffect(() => {
    const delay = 600;

    if (!isOpen) return; // Dropdown closed ⇒ ignore

    const trimmed = searchInputs.searchTerm.trim();

    // Skip if nothing has changed
    if (trimmed === lastTermRef.current) return;

    const handler = setTimeout(() => {
      // User cleared the input ⇒ clear search
      if (trimmed === "") {
        onSearch({ searchTerm: "", searchField: "all" });
      } else {
        onSearch(searchInputs);
      }

      lastTermRef.current = trimmed;
    }, delay);

    return () => clearTimeout(handler);
  }, [searchInputs.searchTerm, searchInputs.searchField, isOpen]);

  // Keep track of the current term so re-opening the dropdown does NOT trigger
  // a fresh search immediately (unless the user changes the text).
  useEffect(() => {
    if (isOpen) {
      // Sync the ref to whatever term exists when the dropdown is opened.
      // This makes the main debounced effect (below) see that nothing changed
      // and therefore skip firing onSearch right away.
      lastTermRef.current = searchInputs.searchTerm.trim();
    }
  }, [isOpen]);

  // ✅ Restore saved filters but DO NOT call API on open
  useEffect(() => {
    const saved = localStorage.getItem(`jobSearch_${activeTab}`);
    if (saved) {
      const parsed = JSON.parse(saved);
      setSearchInputs(parsed);
      // onSearch(parsed); // ❌ removed: this caused API call on open
    } else {
      handleClear();
    }
  }, [activeTab]);

  const searchFieldOptions = {
    0: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    1: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    2: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    3: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    4: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    5: [ { value: "all", label: "All Fields" }, { value: "jobTitle", label: "Job Title" }, { value: "jobId", label: "Job ID" }, { value: "jobType", label: "Job Type" }, { value: "location", label: "Location" }, ],
    submissions: [ { value: "all", label: "All Fields" }, { value: "candidateID", label: "Candidate ID" }, { value: "candidateName", label: "Name" }, { value: "recruiterName", label: "Recruiter Name" }, { value: "jobId", label: "Job ID" }, { value: "jobTitle", label: "Job Title" }, { value: "status", label: "Status" } ]
  };

  const currentSearchFields = searchFieldOptions[activeTab] || searchFieldOptions[0];

  const handleInputChange = (field, value) => {
    setSearchInputs((prev) => ({ ...prev, [field]: value }));
  };

  const handleSearch = () => {
    if (searchInputs.searchTerm.trim()) {
      onSearch(searchInputs);
      // Do NOT close automatically; user can close via outside-click, toggle icon, or Clear button.
    }
  };

  const handleClear = () => {
    localStorage.removeItem(`jobSearch_${activeTab}`);
    const params = new URLSearchParams(window.location.search);
    params.delete("searchTerm");
    params.delete("searchField");
    window.history.replaceState({}, "", `${window.location.pathname}?${params}`);
    setSearchInputs({ searchTerm: "", searchField: "all" });
    onSearch({ searchTerm: "", searchField: "all" });
    onClose();
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") handleSearch();
  };

  if (!isOpen) return null;

  return (
    <div className="absolute top-full right-0 mt-2 w-88 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
      <div className="p-4 pb-0 flex">
        {/* Search Field Selector */}
        <div className="cursor-pointer">
          <TableLikeSearchSelector
            searchInputs={searchInputs}
            handleInputChange={handleInputChange}
            currentSearchFields={currentSearchFields}
          />
        </div>

        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchInputs.searchTerm}
              onChange={(e) => handleInputChange("searchTerm", e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter search term..."
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer">
              <svg
                className={`w-4 h-4 ${searchInputs.searchTerm.trim() ? 'text-blue-600' : 'text-gray-400'}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchDropdown;