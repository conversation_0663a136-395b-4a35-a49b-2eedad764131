const express = require("express");
const router = express.Router();
const {
  registerUser,
  login,
  userInfo,
  refreshToken,
  verifyEmail,
  forgotPassword,
  resetPassword,
  FixingRecruiterDetails,
} = require("../controllers/Auth");
const { auth, authorize } = require("../middlewares/auth");

router.post("/register", registerUser);
router.post(
  "/register/accountmanager",
  auth,
  authorize("headAccountManager"),
  registerUser
);
router.post("/login", login);
router.get("/user", auth, userInfo);
router.post("/refresh-token", refreshToken);
router.get("/verify-email", verifyEmail);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password", resetPassword);
router.post(
  "/fixing-recruiter-details",
  auth,
  authorize("headAccountManager"),
  FixingRecruiterDetails
);
module.exports = router;
