const mongoose = require("mongoose");

const userSchema = new mongoose.Schema(
  {
    name: {
      firstName: {
        type: String,
        required: true,
      },
      middleName: {
        type: String,
      },
      lastName: {
        type: String,
      },
    },
    password: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: true,
      unique: true,
    },
    email: {
      type: String,
      required: true,
    },
    phone: {
      countryCode: {
        type: String,
        required: true,
      },
      number: {
        type: String,
        required: true,
      },
    },
    userType: {
      type: String,
      enum: ["recruiter", "accountManager", "headAccountManager"],
      required: true,
    },
    emailVerified: {
      type: <PERSON>ole<PERSON>,
      default: false,
    },
    source: {
      name: {
        type: String,
        required: function () {
          return this.userType === "recruiter";
        },
      },
      campaign: {
        type: String,
        required: function () {
          return this.source?.name !== "website";
        },
      },
    },
    signupDevice: {
      ipAddress: {
        type: String,
        required: function () {
          return this.userType === "recruiter";
        },
      },
      browser: {
        type: String,
      },
      os: {
        type: String,
      },
    },
    tokenVersion: {
      type: Number,
      default: 0, // Used for forced logout functionality
    },
    lastLogin: {
      type: Date,
      default: null,
    },
    deviceInfo: {
      userAgent: String,
      ipAddress: String,
      deviceName: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: function () {
        return this.userType === "accountManager";
      },
    },
  },

  {
    timestamps: true,
  }
);

const User = mongoose.model("User", userSchema);

module.exports = User;
