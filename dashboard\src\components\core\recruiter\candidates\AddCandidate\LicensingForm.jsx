import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";
import SelectField from "../../../../common/Input/SelectionField";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";

const LicensingForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
      autoComplete="off"
    >
      <InputField
        label="License Number"
        name="licensing.stateLicenses"
        value={values.licensing.stateLicenses || ""}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter license number"
        errorMessage={
          touched.licensing?.stateLicenses && errors.licensing?.stateLicenses
        }
      />

      {/* <InputField
        label="Expiry Date"
        name="licensing.licenseExpireDate"
        value={values.licensing.licenseExpireDate || ""}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        type="date"
        errorMessage={
          touched.licensing?.licenseExpireDate &&
          errors.licensing?.licenseExpireDate
        }
      /> */}
      <CalendarDropdownField
        label="License Expire Date"
        name="licensing.licenseExpireDate"
        // required
        // errorMessage={
        //   touched.licensing?.licenseExpireDate &&
        //   errors.licensing?.licenseExpireDate
        // }
        minDate={new Date()}
      />

      <SelectField
        label="Compact License"
        name="licensing.compactLicense"
        value={
          values.licensing.compactLicense === true
            ? "yes"
            : values.licensing.compactLicense === false
            ? "no"
            : values.licensing.compactLicense || ""
        }
        onChange={(e) => {
          const val =
            e.target.value === "yes"
              ? true
              : e.target.value === "no"
              ? false
              : "";
          handleChange({
            target: {
              name: "licensing.compactLicense",
              value: val,
            },
          });
        }}
        onBlur={handleBlur}
        placeholder="Select"
        options={[
          { value: "yes", label: "Yes" },
          { value: "no", label: "No" },
        ]}
        required
        errorMessage={
          touched.licensing?.compactLicense && errors.licensing?.compactLicense
        }
      />
    </form>
  );
};

export default LicensingForm;
