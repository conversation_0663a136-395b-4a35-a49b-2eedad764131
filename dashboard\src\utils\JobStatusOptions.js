export const JobStatusOptions = [
  {
    label: "Submitted",
    value: "submitted",
    color: "text-[#F79009] bg-[#FEEFC7]",
  },
  {
    label: "Reviewing",
    value: "reviewing",
    color: "text-[#F79009] bg-[#FEEFC7]",
  },
  {
    label: "Submitted to client",
    value: "submitted to client",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Interviewing",
    value: "interviewing",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Selected",
    value: "selected",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Rejected",
    value: "rejected",
    color: "text-[#D92D20] bg-[#FECDCA]",
  },
  {
    label: "Awaiting Offer",
    value: "awaiting offer",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Offer Released",
    value: "offer released",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Offer Accepted",
    value: "offer accepted",
    color: "text-[#039855] bg-[#D1FADF]",
  },
  {
    label: "Offer Rejected",
    value: "offer rejected",
    color: "text-[#D92D20] bg-[#FECDCA]",
  },
  {
    label: "Hired-Under guarantee period",
    value: "hired-under guarantee period",
    color: "text-[#1570EF] bg-[#D1E9FF]",
  },
  {
    label: "Guarantee period not completed",
    value: "guarantee period not completed",
    color: "text-[#D92D20] bg-[#FECDCA]",
  },
  {
    label: "Guarantee period completed",
    value: "guarantee period completed",
    color: "text-[#039855] bg-[#D1FADF]",
  },
];
