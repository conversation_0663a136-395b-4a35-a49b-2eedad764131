const Job = require("../models/Job.models");
const mongoose = require("mongoose");
const User = require("../models/User.models");
const recruiterProfile = require("../models/RecruiterProfile.models");
const { accountManager } = require("../models/AccountManager.models");
const CoinTransaction = require("../models/CoinTransaction");
const userCoinBalance = require("../models/UserCoinBalance");
const { workOnRequest } = require("../models/WorkOnRequest.models");
const job = require("../models/Job.models");
const { bookMark } = require("../models/bookmark.models");
const { candidateSubmission } = require("../models/CandidateSubmission.models");
const { Candidates } = require("../models/Candidate.models");
const { auditLog } = require("../models/AuditLog.models");
const { submissionLog } = require("../models/submissionLog.models");
const {
  buildRecruiterSortConditions,
  buildRecruiterFilterConditions,
  buildRecruiterResponseMetadata,
} = require("../utils/recruiterJobUtils");
const { createNotification } = require("../services/notificationService");

//* @Desc remove or unmap recruiter from job in aspect of not-working on it
//* @Route POST /api/v1/job/recruiter/unmap-job
//* @Access private, recruiter
const unMappedJob = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    const recruiter = await recruiterProfile.findOne({
      "user._id": user?._id,
    });
    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    let isjobID = false;
    // Deactivate previous active log for the same jobId
    recruiter.jobsWorkingOn = recruiter.jobsWorkingOn?.map((entry) => {
      if (
        entry?.jobId?.toString() === jobId?.toString() &&
        entry.isActive &&
        entry.status == "assigned" &&
        entry.assignedBy == "manager"
      ) {
        isjobID = true;
      }

      if (
        entry?.jobId?.toString() === jobId?.toString() &&
        entry.isActive &&
        entry.status == "assigned"
      ) {
        return { ...entry, isActive: false };
      }

      return entry;
    });

    if (isjobID) {
      await workOnRequest.findOneAndUpdate(
        {
          "job._id": new mongoose.Types.ObjectId(jobId),
          "recruiter.userId": user.userId,
        },
        { status: "rejected" }
      );
    }

    // Push new assignment log
    recruiter.jobsWorkingOn.push({
      jobId,
      status: "removed",
      isActive: true,
    });
    await recruiter.save();

    return res.status(200).json({
      success: true,
      message: "Job up-mapped successfully",
    });
  } catch (err) {
    console.error("Error in unMappedJob:", err);
    return res.status(500).json({ message: err.message, status: false });
  }
};

//* @Desc get all recruiters
//* @Route POST /api/v1/recruiter/get-all-recruiters
//* @Access private, head manager
const getAllRecruiters = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const recruiter = await User.aggregate([
      {
        $match: {
          userType: "recruiter",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          localField: "userId",
          foreignField: "recruiter.userId",
          as: "workonrequests",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "userId",
          foreignField: "submittedBy.userID",
          as: "submissions",
        },
      },
      {
        $addFields: {
          submissionsCount: { $size: "$submissions" },
        },
      },
      {
        $addFields: {
          workonrequestsCount: {
            $size: "$workonrequests",
          },
          // Count only active jobs (work requests with status accepted)
          activeJobsCount: {
            $size: {
              $filter: {
                input: "$workonrequests",
                cond: { $eq: ["$this.status", "accepted"] },
              },
            },
          },
          // Count all jobs (total work requests)
          totalJobsCount: {
            $size: "$workonrequests",
          },
        },
      },

      {
        $project: {
          submissionsCount: 1,
          workonrequestsCount: 1,
          workonrequests: 1,
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
          status: 1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = recruiter[0]?.metadata[0]?.total || 0;
    const recruiters = recruiter[0]?.data || [];
    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({
      message: "get all recruiters",
      success: true,
      data: recruiters,
      total,
      totalPages,
      page,
    });
  } catch (error) {
    console.error("Error in getting recruiters:", error);
    return res.status(400).json({ message: error.message, success: false });
  }
};

//* @Desc get recruiter
//* @Route POST /api/v1/recruiter/recruiter-details
//* @Access private, head manager
const getRecruiterDetails = async (req, res) => {
  try {
    const { recruiterID } = req.query;

    if (!recruiterID) {
      return res.status(400).json({
        success: false,
        message: "Recruiter ID is required.",
      });
    }

    const recruiter = await User.aggregate([
      {
        $match: {
          userType: "recruiter",
          userId: recruiterID,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$recruiter.userId", "$$userID"] },
                    { $eq: ["$status", "accepted"] },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job.jobId",
                foreignField: "jobId",
                as: "job",
              },
            },
            { $unwind: "$job" },
            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "job.jobId",
                foreignField: "job.jobID",
                as: "submission",
              },
            },
            {
              $addFields: {
                submissionsCount: { $size: "$submission" },
              },
            },
          ],
          as: "workonrequests",
        },
      },
      {
        $lookup: {
          from: "cointransactions",
          let: { userId: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$transactionType", "spent"] },
              },
            },
            {
              $lookup: {
                from: "workrequests",
                let: { userId: "$userId", jobId: "$relatedJobId" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $eq: ["$recruiter._id", "$$userId"] },
                          { $eq: ["$job._id", "$$jobId"] },
                          { $eq: ["$status", "accepted"] },
                        ],
                      },
                    },
                  },
                ],
                as: "workRequest",
              },
            },
            {
              $match: {
                "workRequest.0": { $exists: true },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "relatedJobId",
                foreignField: "_id",
                as: "job",
              },
            },
            { $unwind: { path: "$job", preserveNullAndEmptyArrays: true } },
          ],
          as: "acceptedSpentTransactions",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$submittedBy.userID", "$$userID"],
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job.jobID",
                foreignField: "jobId",
                as: "job",
              },
            },
            { $unwind: "$job" },
            {
              $lookup: {
                from: "candidates",
                localField: "candidate.candidateID",
                foreignField: "candidateID",
                as: "candidate",
              },
            },
            { $unwind: "$candidate" },
          ],
          as: "submissions",
        },
      },
      {
        $addFields: {
          submissionsCount: { $size: "$submissions" },
          workonrequestsCount: {
            $size: "$workonrequests",
          },
          totalJobCount: {
            $add: [
              { $size: "$workonrequests" },
              { $size: "$acceptedSpentTransactions" },
            ],
          },
          totalJob: {
            $concatArrays: [
              { $ifNull: ["$workonrequests", []] },
              {
                $ifNull: ["$acceptedSpentTransactions", []],
              },
            ],
          },
        },
      },
      {
        $project: {
          submissionsCount: 1,
          workonrequestsCount: 1,
          totalJob: 1,
          totalJobCount: 1,
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
          submissions: 1,
          workonrequests: 1,
          acceptedSpentTransactions: 1,
        },
      },
    ]);

    return res.status(200).json({
      message: "get recruiter",
      success: true,
      data: recruiter,
    });
  } catch (err) {
    console.error("Error in getting recruiter:", err);
    return res.status(400).json({ message: err.message, success: false });
  }
};

const getRecruitersByJobId = async (req, res) => {
  try {
    const { jobId } = req.params;
    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "Job ID is required.",
      });
    }

    const job = await Job.findOne({ jobId });
    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Job not found.",
      });
    }

    const recruiters = await recruiterProfile.aggregate([
      {
        $match: {
          "jobsWorkingOn.jobId": new mongoose.Types.ObjectId(job._id),
          "jobsWorkingOn.isActive": true,
          "jobsWorkingOn.status": "assigned",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "user._id",
          foreignField: "_id",
          as: "user",
        },
      },
      {
        $unwind: "$user",
      },
      {
        $project: {
          name: "$user.name",
          userId: "$user.userId",
          _id: "$user._id",
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      message: "Recruiters fetched successfully",
      data: recruiters,
    });
  } catch (error) {
    console.error("Error fetching recruiters by job ID:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc Get matched jobs for recruiter
//* @Route GET /api/v1/recruiter/matched-jobs
//* @Access private - Recruiter
const getMatchedJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?.userId;
    //* Verify recruiter exists
    const recruiter = await recruiterProfile.findOne({
      "user.userId": recruiterId,
    });

    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter and sort conditions using centralized utilities
    const filterConditions = buildRecruiterFilterConditions(req.query);
    const sortConditions = buildRecruiterSortConditions(
      req.query.sortBy,
      req.query.sortOrder,
      req.query.postedDate
    );

    const { candidateRole, domain } = recruiter;

    //* Building match query with recruiter preferences
    const matchQuery = {
      jobProfile: { $in: candidateRole },
      industry: { $in: domain },
      visibility: true,
      ...filterConditions, // Add filter conditions
    };

    //* Use centralized sorting (already built above)
    const sort = sortConditions;

    const pipeline = [
      { $match: matchQuery },
      ...(Object.keys(sort).length ? [{ $sort: sort }] : []),

      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter.userId", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork", "accepted"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workrequest",
        },
      },

      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user.userId", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiter",
        },
      },
      {
        $addFields: {
          recruitercount: { $size: "$recruiter" },
          workrequestcount: { $size: "$workrequest" },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $eq: ["$workrequestcount", 0] },
              { $eq: ["$recruitercount", 0] },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "users",
          let: {
            userId: "$accountManager._id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$_id", "$$userId"] }],
                },
              },
            },

            {
              $project: {
                name: 1,
                email: 1,
                userId: 1,
                phone: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: false,
        },
      },

      {
        $lookup: {
          from: "jobbookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user?._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ];

    const results = await Job.aggregate(pipeline);

    const total = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    // Get response metadata using centralized utility
    const responseMetadata = buildRecruiterResponseMetadata(
      req.query,
      sortConditions
    );

    res.status(200).json({
      success: true,
      message: "Matched jobs fetched successfully",
      total,
      page,
      totalPages: Math.ceil(total / limit),
      results: jobs,
      ...responseMetadata,
    });
  } catch (error) {
    console.error("Error fetching matched jobs:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get work upon jobs
//* @Route GET /api/v1/recruiter/work-upon-jobs
//* @Access Private - Recruiter
const getWorkJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter and sort conditions using centralized utilities
    const filterConditions = buildRecruiterFilterConditions(req.query);
    const sortConditions = buildRecruiterSortConditions(
      req.query.sortBy,
      req.query.sortOrder,
      req.query.postedDate
    );

    const results = await recruiterProfile.aggregate([
      {
        $match: {
          "user.userId": user.userId,
        },
      },

      {
        $unwind: {
          path: "$jobsWorkingOn",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: "$jobsWorkingOn.jobId",
          status: "$jobsWorkingOn.status",
          isActive: "$jobsWorkingOn.isActive",
        },
      },
      {
        $match: {
          status: "assigned",
          isActive: true,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "_id",
          foreignField: "_id",
          as: "_id",
        },
      },
      {
        $unwind: {
          path: "$_id",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $replaceRoot: {
          newRoot: "$_id",
        },
      },
      // Apply filters after job data is available
      ...(Object.keys(filterConditions).length > 0
        ? [{ $match: filterConditions }]
        : []),
      // Apply sorting immediately after filters, before any lookups
      { $sort: sortConditions },
      {
        $lookup: {
          from: "jobbookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    { $eq: ["$recruiterId", "$$userId"] },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: { $gt: [{ $size: "$bookmark" }, 0] },
        },
      },
      {
        $lookup: {
          from: "users",
          let: {
            userId: "$accountManager._id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ["$_id", "$$userId"] }],
                },
              },
            },

            {
              $project: {
                name: 1,
                email: 1,
                userId: 1,
                phone: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                candidateStatus: [
                  {
                    $match: {
                      "submittedBy.userID": user.userId,
                    },
                  },
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    // Get response metadata using centralized utility
    const responseMetadata = buildRecruiterResponseMetadata(
      req.query,
      sortConditions
    );

    res.status(200).json({
      success: true,
      message: "workon jobs fetched successfully",
      total,
      page,
      totalPages: Math.ceil(total / limit),
      results: jobs,
      ...responseMetadata,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get work on request jobs
//* @Route GET /api/v1/recruiter/work-on-request
//* @Access Private - Recruiter
const getWorkOnRequestJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter and sort conditions using centralized utilities
    const filterConditions = buildRecruiterFilterConditions(req.query);
    const sortConditions = buildRecruiterSortConditions(
      req.query.sortBy,
      req.query.sortOrder,
      req.query.postedDate
    );

    const results = await workOnRequest.aggregate([
      {
        $match: {
          "recruiter.userId": user.userId,
          status: "requestToWork",
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "job._id",
          foreignField: "_id",
          as: "job",
        },
      },
      {
        $unwind: {
          path: "$job",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $replaceRoot: {
          newRoot: "$job",
        },
      },
      // Apply sorting immediately after job data is available
      { $sort: sortConditions },
      // Apply filters after job data is available
      ...(Object.keys(filterConditions).length > 0
        ? [{ $match: filterConditions }]
        : []),
      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "jobbookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          bookmark: 0,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    // Get response metadata using centralized utility
    const responseMetadata = buildRecruiterResponseMetadata(
      req.query,
      sortConditions
    );

    res.status(200).json({
      success: true,
      message: "workonrequest jobs fetched successfully",
      total,
      page,
      totalPages: Math.ceil(total / limit),
      results: jobs,
      ...responseMetadata,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get all jobs which is not workingon and not on workon request by manages
//* @Route GET /api/v1/recruiter/get-all-jobs
//* @Access Private - Recruiter
const getAllJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter and sort conditions using centralized utilities
    const filterConditions = buildRecruiterFilterConditions(req.query);
    const sortConditions = buildRecruiterSortConditions(
      req.query.sortBy,
      req.query.sortOrder,
      req.query.postedDate
    );

    const results = await job.aggregate([
      {
        $match: {
          isDeleted: false,
          visibility: true,
          accountManager: { $exists: true },
          ...filterConditions, // Add filter conditions
        },
      },
      // Apply sorting immediately after initial match and filters
      { $sort: sortConditions },
      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter.userId", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork", "accepted"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workrequest",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user.userId", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiter",
        },
      },
      {
        $addFields: {
          recruitercount: { $size: "$recruiter" },
          workrequestcount: { $size: "$workrequest" },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $eq: ["$recruitercount", 0] },
              { $eq: ["$workrequestcount", 0] },
            ],
          },
        },
      },

      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "jobbookmarks",
          let: {
            userId: new mongoose.Types.ObjectId(user._id),
            jobID: "$_id",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jobId", "$$jobID"] },
                    {
                      $eq: ["$recruiterId", "$$userId"],
                    },
                  ],
                },
              },
            },
          ],
          as: "bookmark",
        },
      },
      {
        $addFields: {
          isBookmark: {
            $gt: [{ $size: "$bookmark" }, 0],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          recruitercount: 0,
          workrequestcount: 0,
          recruiter: 0,
          workrequest: 0,
          bookmark: 0,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    // Get response metadata using centralized utility
    const responseMetadata = buildRecruiterResponseMetadata(
      req.query,
      sortConditions
    );

    res.status(200).json({
      success: true,
      message: "all jobs fetched successfully",
      total,
      page,
      totalPages: Math.ceil(total / limit),
      results: jobs,
      ...responseMetadata,
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc get all save jobs
//* @Route GET /api/v1/recruiter/get-all-save-jobs
//* @Access Private - Recruiter
const getAllSaveJobs = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter and sort conditions using centralized utilities
    const filterConditions = buildRecruiterFilterConditions(req.query);
    const sortConditions = buildRecruiterSortConditions(
      req.query.sortBy,
      req.query.sortOrder,
      req.query.postedDate
    );

    const results = await bookMark.aggregate([
      {
        $match: {
          recruiterId: new mongoose.Types.ObjectId(user._id),
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobId",
          foreignField: "_id",
          as: "jobs",
        },
      },
      {
        $unwind: "$jobs",
      },
      {
        $replaceRoot: {
          newRoot: "$jobs",
        },
      },
      // Apply filters after job data is available
      ...(Object.keys(filterConditions).length > 0
        ? [{ $match: filterConditions }]
        : []),
      // Apply sorting early in the pipeline
      { $sort: sortConditions },
      {
        $lookup: {
          from: "users",
          let: { userId: "$accountManager._id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$_id", "$$userId"],
                },
              },
            },
            {
              $project: {
                name: 1,
                email: 1,
                phone: 1,
                userId: 1,
              },
            },
          ],
          as: "accountManager",
        },
      },
      {
        $unwind: {
          path: "$accountManager",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: new mongoose.Types.ObjectId(user._id),
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter._id", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workonrequest",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: new mongoose.Types.ObjectId(user._id),
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$user._id", "$$recruiterId"],
                    },
                  ],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$jobsWorkingOn.jobId", "$$jobId"],
                    },
                    {
                      $eq: ["$jobsWorkingOn.isActive", true],
                    },
                    {
                      $eq: ["$jobsWorkingOn.status", "assigned"],
                    },
                  ],
                },
              },
            },
          ],
          as: "workingon",
        },
      },
      {
        $addFields: {
          isBookmark: true,
          jobType: {
            $cond: [
              { $gt: [{ $size: "$workingon" }, 0] },
              "workingon",
              {
                $cond: [
                  {
                    $gt: [{ $size: "$workonrequest" }, 0],
                  },
                  "workonrequest",
                  "$$REMOVE",
                ],
              },
            ],
          },
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$job._id", "$$jobID"],
                },
              },
            },
            {
              $facet: {
                candidate: [
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
                candidateStatus: [
                  {
                    $match: {
                      "submittedBy.userID": user.userId,
                    },
                  },
                  {
                    $group: {
                      _id: "$status",
                      count: {
                        $sum: 1,
                      },
                    },
                  },
                  {
                    $project: {
                      status: "$_id",
                      count: "$count",
                      _id: false,
                    },
                  },
                ],
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: { jobID: "$_id" },
          pipeline: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: false,
              },
            },

            {
              $match: {
                "jobsWorkingOn.status": "assigned",
                "jobsWorkingOn.isActive": true,
              },
            },
            {
              $group: {
                _id: "$jobsWorkingOn.jobId",
                count: {
                  $sum: 1,
                },
              },
            },

            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$_id", "$$jobID"],
                    },
                  ],
                },
              },
            },
          ],
          as: "recruiterCount",
        },
      },
      {
        $unwind: {
          path: "$recruiterCount",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          workonrequest: 0,
          workingon: 0,
        },
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = results[0].metadata[0]?.total || 0;
    const jobs = results[0].data;

    // Get response metadata using centralized utility
    const responseMetadata = buildRecruiterResponseMetadata(
      req.query,
      sortConditions
    );

    res.status(200).json({
      success: true,
      message: "all save jobs fetched successfully",
      total,
      page,
      totalPages: Math.ceil(total / limit),
      results: jobs,
      ...responseMetadata,
    });
  } catch (error) {
    console.error("Error save job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc add to bookmark
//* @Route POST /api/v1/recruiter/add-to-bookmark
//* @Access Private - Recruiter
const addToBookMark = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }
    // check job exist
    const jobData = await job.findById(jobId);
    if (!jobData) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    //is bookmark available
    const isJobBookmark = await bookMark.findOne({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    if (isJobBookmark) {
      return res.status(400).json({
        success: false,
        message: "This job already been bookmarked.",
      });
    }

    // add to bookmark
    const isBookMark = new bookMark({
      jobId: jobId,
      recruiterId: user._id,
    });

    await isBookMark.save();

    res.status(200).json({
      success: true,
      message: "add bookmark successfully",
    });
  } catch (error) {
    console.error("Error add bookmark job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc add to bookmark
//* @Route DELETE /api/v1/recruiter/remove-to-bookmark
//* @Access Private - Recruiter
const removeFromBookMark = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.body;

    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    //is bookmark available
    const isJobBookmark = await bookMark.findOne({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    if (!isJobBookmark) {
      return res.status(400).json({
        success: false,
        message: "This job not in bookmarked.",
      });
    }

    // find and update
    await bookMark.findOneAndDelete({
      recruiterId: new mongoose.Types.ObjectId(user._id),
      jobId: new mongoose.Types.ObjectId(jobId),
    });

    res.status(200).json({
      success: true,
      message: "remove bookmark successfully",
    });
  } catch (error) {
    console.error("Error removing bookmark job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc Select Job to work on
//* @Route POST /api/v1/recruiter/select-job-to-work-upon
//* @Access Private - Recruiter
const selectJobToWorkUpon = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?._id;

    const { jobId, workOnType } = req.body;

    if (!["workOn"].includes(workOnType)) {
      return res.status(400).json({
        success: false,
        message: "workonType are required.",
      });
    }

    const amountDeducted = workOnType == "instantSubmit" ? 2 : 1;

    if (!jobId || !recruiterId) {
      return res.status(400).json({
        success: false,
        message: "Job ID and Recruiter ID are required",
      });
    }

    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(400).json({
        success: false,
        message: "Job not found",
      });
    }

    // check the user not work more then 10 job at one time
    const checkWorkon = await recruiterProfile.aggregate([
      {
        $match: {
          "user._id": new mongoose.Types.ObjectId(recruiterId),
        },
      },
      {
        $facet: {
          count: [
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                "jobsWorkingOn.isActive": true,
                "jobsWorkingOn.status": "assigned",
                $or: [
                  {
                    "jobsWorkingOn.isSlotEmpty": false,
                  },
                  {
                    "jobsWorkingOn.isSlotEmpty": {
                      $exists: false,
                    },
                  },
                ],
              },
            },
            { $count: "count" },
          ],
          data: [],
        },
      },
    ]);

    if (checkWorkon[0]?.count[0]?.count >= 10) {
      return res.status(400).json({
        success: false,
        message:
          "The maximum number of slots to work on this job is 10. You cannot assign more job beyond this limit.",
      });
    }
    const recruiter = await recruiterProfile.findOne({
      "user._id": new mongoose.Types.ObjectId(recruiterId),
    });

    if (!recruiter) {
      return res.status(400).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item?.jobId?.toString() == jobId?.toString() &&
          item?.status == "assigned" &&
          item?.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    const userCoinBalances = await userCoinBalance.findOne({
      userId: recruiter.user._id,
    });

    if (!userCoinBalances) {
      return res.status(400).json({
        success: false,
        message: "User coin balance not found",
      });
    }

    await CoinTransaction.create({
      userId: recruiter.user._id,
      relatedJobId: job._id,
      transactionType: "spent",
      quantity: 1, // Assuming a fixed amount for job selection
      balanceBefore: userCoinBalances.currentBalance,
      balanceAfter: userCoinBalances.currentBalance - amountDeducted,
      status: "completed",
      spendType: workOnType,
      amount: amountDeducted,
      createdAt: Date.now(),
    });

    // Update the recruiter to include the selected job
    recruiter.jobsWorkingOn.push({
      jobId,
      assignedBy: "self",
      assignedAt: Date.now(),
    });
    await recruiter.save();

    // Update the coin balance
    userCoinBalances.currentBalance -= amountDeducted; // Deduct 1 coin for job selection
    userCoinBalances.totalSpent += amountDeducted; // Update total spent
    userCoinBalances.lastTransactionAt = Date.now();
    await userCoinBalances.save();

    res.status(200).json({
      success: true,
      message: "Job selected successfully",
    });
  } catch (error) {
    console.error("Error selecting job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc work on request accept & reject
//* @Route POST /api/v1/recruiter/work-on-request-status-update
//* @Access Private - Recruiter
const updateWorkOnRequest = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId, status } = req.body;

    if (!(jobId && status && ["accepted", "rejected"].includes(status))) {
      return res.status(400).json({
        success: false,
        message: "jobid and status not found.",
      });
    }

    // check recruiter exist
    const recruiter = await recruiterProfile.findOne({
      "user._id": user?._id,
    });
    if (!recruiter) {
      return res.status(404).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item?.jobId?.toString() == jobId?.toString() &&
          item.status == "assigned" &&
          item.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    // update work on request
    await workOnRequest.findOneAndUpdate(
      {
        "job._id": new mongoose.Types.ObjectId(jobId),
        "recruiter.userId": user.userId,
      },
      {
        $set: {
          status: status,
        },
        $push: {
          statusLogs: {
            to: status,
            from: "requestToWork",
            changedBy: user?._id,
          },
        },
      },
      { new: true }
    );

    if (status === "rejected") {
      // Update the recruiter to include the selected job for accept jobs
      recruiter.jobsWorkingOn.push({
        jobId,
        status: "removed",
        isActive: true,
      });
      await recruiter.save();
    } else {
      // Update the recruiter to include the selected job for accept jobs
      recruiter.jobsWorkingOn.push({
        jobId,
        assignedBy: "manager",
        isSlotEmpty: true,
        assignedAt: Date.now(),
      });
      await recruiter.save();
    }

    // for work on request accept and reject notification
    const jobDetails = await job.findOne({ jobId });
    await createNotification({
      recipients: [jobDetails?.accountManager?._id],
      type: "work-on-request",
      message: `Recruiter ${status} work on request.`,
      relatedJobId: [jobDetails?.jobId],
    });

    res.status(200).json({
      success: true,
      message: "work on request update successfully",
    });
  } catch (error) {
    console.error("Error work on request update job:", error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

//* @Desc dashboard stats
//* @Route POST /api/v1/recruiter/dashboard-stats
//* @Access Private - Recruiter
const dashboardStats = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const recruiter = await recruiterProfile.aggregate([
      {
        $match: {
          "user.userId": user.userId,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobsWorkingOn.jobId",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      {
        $addFields: {
          enrichedJobs: {
            $map: {
              input: "$jobsWorkingOn",
              as: "job",
              in: {
                $mergeObjects: [
                  "$$job",
                  {
                    jobInfo: {
                      $arrayElemAt: [
                        {
                          $filter: {
                            input: "$jobDetails",
                            as: "jobDetail",
                            cond: {
                              $eq: ["$$jobDetail._id", "$$job.jobId"],
                            },
                          },
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          domain: 1,
          candidateRole: 1,

          workingOn: {
            $size: {
              $filter: {
                input: { $ifNull: ["$jobsWorkingOn", []] },
                as: "job",
                cond: {
                  $and: [
                    { $eq: ["$$job.status", "assigned"] },
                    { $eq: ["$$job.isActive", true] },
                  ],
                },
              },
            },
          },
          recentAssignedJobs: {
            $filter: {
              input: "$enrichedJobs",
              as: "job",
              cond: {
                $and: [
                  { $eq: ["$$job.status", "assigned"] },
                  { $eq: ["$$job.isActive", true] },
                  {
                    $gte: [
                      "$$job.assignedAt",
                      {
                        $dateSubtract: {
                          startDate: "$$NOW",
                          unit: "hour",
                          amount: 84,
                        },
                      },
                    ],
                  },
                ],
              },
            },
          },
        },
      },
    ]);

    const job = await Job.aggregate([
      {
        $match: {
          isDeleted: false,
          visibility: true,
          accountManager: { $exists: true },
        },
      },
      {
        $lookup: {
          from: "workrequests",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$job._id", "$$jobId"] },
                    {
                      $eq: ["$recruiter.userId", "$$recruiterId"],
                    },
                    {
                      $in: ["$status", ["requestToWork", "accepted"]],
                    },
                  ],
                },
              },
            },
          ],
          as: "workrequest",
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            jobId: "$_id",
            recruiterId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$user.userId", "$$recruiterId"],
                },
              },
            },
            {
              $project: {
                jobs: {
                  $filter: {
                    input: "$jobsWorkingOn",
                    as: "job",
                    cond: {
                      $and: [
                        {
                          $eq: ["$$job.jobId", "$$jobId"],
                        },
                        {
                          $eq: ["$$job.status", "assigned"],
                        },
                        {
                          $eq: ["$$job.isActive", true],
                        },
                      ],
                    },
                  },
                },
              },
            },
            {
              $match: {
                $expr: {
                  $gt: [{ $size: "$jobs" }, 0],
                },
              },
            },
          ],
          as: "recruiter",
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $eq: [{ $size: "$workrequest" }, 0] },
              { $eq: [{ $size: "$recruiter" }, 0] },
            ],
          },
        },
      },
      {
        $facet: {
          matchJob: [
            {
              $match: {
                jobProfile: {
                  $in: recruiter[0]?.candidateRole,
                },
                industry: { $in: recruiter[0]?.domain },
              },
            },

            {
              $count: "total",
            },
          ],
          alljob: [
            {
              $count: "total",
            },
          ],
        },
      },
    ]);

    const workonrequest = await workOnRequest.aggregate([
      {
        $match: {
          "recruiter.userId": user.userId,
          status: "requestToWork",
        },
      },
      {
        $count: "requestToWork",
      },
    ]);

    const candidate = await candidateSubmission.aggregate([
      {
        $match: {
          "submittedBy.userID": user.userId,
        },
      },
      {
        $addFields: {
          candidateSubmitted: {
            $cond: [
              {
                $in: [
                  "$status",
                  [
                    "submitted",
                    "reviewing",
                    "submitted to client",
                    "selected",
                    "interviewing",
                    "reject after interview",
                    "awaiting offer",
                    "rejected",
                    "offer released",
                    "offer accepted",
                    "offer rejected",
                    "hired-under guarantee period",
                    "guarantee period not completed",
                    "guarantee period completed",
                  ],
                ],
              },
              1,
              0,
            ],
          },
          candidateSelected: {
            $cond: [
              {
                $in: [
                  "$status",
                  [
                    "submitted to client",
                    "selected",
                    "interviewing",
                    "reject after interview",
                    "awaiting offer",
                    "rejected",
                    "offer released",
                    "offer accepted",
                    "offer rejected",
                    "hired-under guarantee period",
                    "guarantee period not completed",
                    "guarantee period completed",
                  ],
                ],
              },
              1,
              0,
            ],
          },
          offerReleased: {
            $cond: [
              {
                $in: [
                  "$status",
                  [
                    "offer released",
                    "offer accepted",
                    "offer rejected",
                    "hired-under guarantee period",
                    "guarantee period not completed",
                    "guarantee period completed",
                  ],
                ],
              },
              1,
              0,
            ],
          },
          offerAccepted: {
            $cond: [
              {
                $in: [
                  "$status",
                  [
                    "offer accepted",
                    "offer rejected",
                    "hired-under guarantee period",
                    "guarantee period not completed",
                    "guarantee period completed",
                  ],
                ],
              },
              1,
              0,
            ],
          },
          offerRejected: {
            $cond: [
              {
                $in: [
                  "$status",
                  [
                    "offer rejected",
                    "reject after interview",
                    "guarantee period not completed",
                  ],
                ],
              },
              1,
              0,
            ],
          },
          backout: {
            $cond: [
              {
                $in: ["$status", ["guarantee period not completed"]],
              },
              1,
              0,
            ],
          },
        },
      },
      {
        $group: {
          _id: "$submittedBy.userID",
          candidateSubmitted: {
            $sum: "$candidateSubmitted",
          },
          candidateSelected: {
            $sum: "$candidateSelected",
          },
          offerReleased: { $sum: "$offerReleased" },
          offerAccepted: { $sum: "$offerAccepted" },
          offerRejected: { $sum: "$offerRejected" },
          backout: { $sum: "$backout" },
        },
      },
    ]);

    const jobUpdate = await auditLog.aggregate([
      {
        $match: {
          visibleTo: "external",
          createdAt: {
            $gte: new Date(Date.now() - 3.5 * 24 * 60 * 60 * 1000),
          },
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $lookup: {
          from: "jobs",
          localField: "JobId",
          foreignField: "_id",
          as: "JobId",
        },
      },
      {
        $unwind: {
          path: "$JobId",
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: "recruiters",
          let: {
            id: "$JobId._id",
            userId: user.userId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$user.userId", "$$userId"],
                },
              },
            },
            {
              $unwind: {
                path: "$jobsWorkingOn",
                preserveNullAndEmptyArrays: true,
              },
            },

            {
              $match: {
                "jobsWorkingOn.isActive": true,
                "jobsWorkingOn.status": "assigned",
              },
            },
            {
              $match: {
                $expr: {
                  $eq: ["$jobsWorkingOn.jobId", "$$id"],
                },
              },
            },
          ],
          as: "recruiters",
        },
      },
      {
        $addFields: {
          isRecruiter: { $size: "$recruiters" },
        },
      },
      {
        $match: {
          $expr: {
            $gt: ["$isRecruiter", 0],
          },
        },
      },
      {
        $project: {
          createdAt: "$createdAt",
          title: "$JobId.jobTitle",
          jobID: "$JobId.jobId",
          update: "$updateFields",
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      message: "dashboard stats find",
      data: {
        workingOn: recruiter[0]?.workingOn,
        matchJob: job[0]?.matchJob[0]?.total,
        alljob: job[0]?.alljob[0]?.total,
        workonrequest: workonrequest[0]?.requestToWork,
        recentworkingon: recruiter[0]?.recentAssignedJobs,
        totalCandidiate: candidate[0]?.candidateSubmitted,
        candidateSubmitted: candidate[0]?.candidateSubmitted,
        candidateSelected: candidate[0]?.candidateSelected,
        offerReleased: candidate[0]?.offerReleased,
        offerAccepted: candidate[0]?.offerAccepted,
        offerRejected: candidate[0]?.offerRejected,
        backout: candidate[0]?.backout,
        jobUpdate,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

module.exports = {
  unMappedJob,
  getAllRecruiters,
  getRecruiterDetails,
  getRecruitersByJobId,
  getMatchedJobs,
  selectJobToWorkUpon,
  getWorkJobs,
  getWorkOnRequestJobs,
  getAllJobs,
  addToBookMark,
  removeFromBookMark,
  getAllSaveJobs,
  updateWorkOnRequest,
  dashboardStats,
};
