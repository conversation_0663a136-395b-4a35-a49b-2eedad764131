import React, { useEffect, useState } from "react";
import Modal from "../../../common/Modal/Modal";
import TableHeader from "../../../common/Table/TableHeader";
import { getallManagerWorkHistory } from "../../../../services/operations/headAccountAPI";
import { bulkAssignJobToManager } from "../../../../services/operations/jobAPI";
import Pills from "../../../common/Job/Pills";

const AccountManagerAssignment = ({
  setIsModalOpen,
  isModalOpen,
  getAllJob,
  setSelectedJobs,
  selectedJobs,
}) => {
  const columns = [
    "MANAGER",
    "TOTAL ASSIGNED JOBS",
    "SPECIALIZATION",
    "ASSIGN",
  ];
  const [loading, setLoading] = useState(false);
  const [allManagers, setAllManagers] = useState([]);

  async function getAllManger() {
    try {
      setLoading(true);
      const getaccountManager = await getallManagerWorkHistory();

      const managerDetails = getaccountManager?.data?.map((item) => {
        return {
          id: { userId: item.userId, _id: item?._id },
          manager: item?.name?.firstName + " " + item?.name?.lastName,
          workonjob: item?.workonJobCount,
          domain: item?.profile?.domain,
        };
      });
      setAllManagers(managerDetails);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }

  useEffect(() => {
    if (isModalOpen) {
      getAllManger();
    }
  }, [isModalOpen]);

  async function assignJobToManager(manager, jobs) {
    try {
      const postData = {
        mapper: Object.keys(jobs)
          ?.filter((item) => jobs[item])
          ?.map((item) => {
            return {
              jobID: item,
              managerID: manager?._id,
              systemGenratedID: manager?.userId,
              directPublish: false,
            };
          }),
      };

      await bulkAssignJobToManager(postData);
      setSelectedJobs([]);
      getAllJob();
      setAllManagers([]);
      setIsModalOpen(false);
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <>
      <Modal
        title={"Assign Manager"}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
        css="relative p-4 w-3xl max-w-3xl max-h-full "
      >
        <div className="px-2 pt-1 pb-10 ">
          <table className="min-w-full bg-white ">
            <TableHeader
              columns={columns}
              css="px-4 py-3 text-center font-semibold sticky top-0 bg-gray-50 border-b border-[#E2E8F0]"
            />
            <tbody>
              {allManagers?.map((manager, index) => (
                <tr className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800">
                  {Object.keys(manager)?.map((item) => {
                    if (item == "id") {
                      return;
                    }
                    return (
                      <td className="px-4 py-3 text-center">
                        {Array.isArray(manager[item])
                          ? manager[item].join(",")
                          : manager[item]}
                        {/* <div className="flex items-center gap-1 flex-wrap">
                          {Array.isArray(manager[item])
                            ? manager[item].map((subItem, index) => (
                                <Pills title={subItem} key={index} />
                              ))
                            : manager[item]}
                        </div> */}
                      </td>
                    );
                  })}
                  <td
                    className="px-4 py-3 text-center underline text-[#4D82F3] cursor-pointer"
                    onClick={(e) => {
                      e.preventDefault();
                      assignJobToManager(manager.id, selectedJobs);
                    }}
                  >
                    Assign
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Modal>
    </>
  );
};

export default AccountManagerAssignment;
