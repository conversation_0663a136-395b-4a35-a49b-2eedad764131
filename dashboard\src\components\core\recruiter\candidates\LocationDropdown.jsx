import React, { useState } from "react";
import countriesData from "../../../../data/countries.json";

const LocationDropdown = ({ selected, setSelected }) => {
  const [search, setSearch] = useState("");

  // Remove duplicate country names
  const allCountries = Array.from(new Set(countriesData.map((c) => c.name)));
  const filtered = allCountries.filter((name) =>
    name.toLowerCase().includes(search.toLowerCase())
  );
  const showList = search.trim().length > 0 && filtered.length > 0;
  const half = Math.ceil(filtered.length / 2);
  const left = filtered.slice(0, half);
  const right = filtered.slice(half);

  function toggleCountry(name) {
    setSelected((prev) =>
      prev.includes(name)
        ? prev.filter((n) => n !== name)
        : [...prev, name]
    );
  }

  function removeCountry(name) {
    setSelected((prev) => prev.filter((n) => n !== name));
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border p-4 w-[360px] max-w-full box-border overflow-visible">
      <div className="mb-2 relative">
        <input
          className="w-full border rounded pl-9 pr-2 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-200"
          placeholder="Type to search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          autoFocus
        />
        <svg
          className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <circle cx="11" cy="11" r="8" />
          <path d="M21 21l-4.35-4.35" />
        </svg>
      </div>
      {/* Selected chips */}
      {selected.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selected.map((name) => (
            <span
              key={name}
              className="flex items-center bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-medium max-w-[140px] truncate"
              title={name}
            >
              {name}
              <button
                className="ml-1 text-blue-400 hover:text-blue-700 focus:outline-none"
                onClick={() => removeCountry(name)}
                tabIndex={-1}
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}
      <div className="border-b mb-2" />
      {showList ? (
        <div className="flex gap-6 max-h-72 overflow-y-auto">
          <div className="flex flex-col gap-2 flex-1 min-w-0">
            {left.map((name) => (
              <label
                key={name}
                className="flex items-center gap-2 text-sm cursor-pointer min-w-0"
              >
                <input
                  type="checkbox"
                  checked={selected.includes(name)}
                  onChange={() => toggleCountry(name)}
                  className="accent-blue-500 w-4 h-4 rounded border-gray-300"
                />
                <span className="truncate max-w-[140px]" title={name}>{name}</span>
              </label>
            ))}
          </div>
          <div className="flex flex-col gap-2 flex-1 min-w-0">
            {right.map((name) => (
              <label
                key={name}
                className="flex items-center gap-2 text-sm cursor-pointer min-w-0"
              >
                <input
                  type="checkbox"
                  checked={selected.includes(name)}
                  onChange={() => toggleCountry(name)}
                  className="accent-blue-500 w-4 h-4 rounded border-gray-300"
                />
                <span className="truncate max-w-[140px]" title={name}>{name}</span>
              </label>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-gray-400 text-xs text-center py-4">
          Type to search for a location
        </div>
      )}
    </div>
  );
};

export default LocationDropdown;
