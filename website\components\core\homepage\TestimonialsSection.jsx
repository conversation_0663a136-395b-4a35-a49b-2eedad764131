"use client";
import TestimonialsCard from "./TestimonialsCard";
import sneha from "@/public/assets/images/homepage/sneha.webp";
import elena from "@/public/assets/images/homepage/elena.webp";
import john from "@/public/assets/images/homepage/john.webp";
import emma from "@/public/assets/images/homepage/emma.webp";
import raj from "@/public/assets/images/homepage/raj.webp";
import soumya from "@/public/assets/images/homepage/shreya.webp";
import TestimonialCarousel from "./TestimonialCarousel";

const TestimonialsSection = () => {
  return (
    <div className="flex flex-col py-24 items-center gap-7">
      <h2 className="text-5xl font-bold">Success Stories</h2>
      <p className="text-center text-lg font-light w-[75%]">
        Hear from our top performers and see how HIRRING.COM has helped them
        build thriving businesses
      </p>
      <div className=" gap-8 hidden lg:flex">
        <div className="flex flex-col gap-10">
          <TestimonialsCard
            name="<PERSON>"
            testimonial="I doubled my commissions within the first 3 months of joining HIRRING.COM. The tools and support provided helped me streamline my recruitment process and focus on what matters – building relationships with my clients."
            image={john}
            height={15.5}
          />
          <TestimonialsCard
            name="Soumya M."
            testimonial="Hirring.com is amazing. I don’t worry about invoicing, interviews, or coordination - they handle everything."
            image={soumya}
            height={15.5}
          />
        </div>
        <div className="flex flex-col gap-10">
          <TestimonialsCard
            name="Emma S."
            testimonial="HIRRING.COM has given me the freedom to work on my own terms while earning a consistent income. The platform's resources are a game-changer for someone like me, who's always on the go."
            image={emma}
            height={18.32}
          />
          <TestimonialsCard
            name="Sneha P."
            testimonial="I joined with zero client connections, and within 60 days I’d closed 3 roles. The account managers make it so easy to focus only on sourcing."
            image={sneha}
            height={16.87}
          />
        </div>
        <div className="flex flex-col gap-10">
          <TestimonialsCard
            name="Raj K."
            testimonial="The onboarding process was so smooth, and the commissions are the best I’ve seen in the industry. HIRRING.COM truly stands out in how they support their recruiters."
            image={raj}
            height={15.5}
          />
          <TestimonialsCard
            name="Elena V."
            testimonial="Hirring.com is the reason I quit my 9–5. I earn more, work less, and submit candidates globally from my laptop."
            image={elena}
            height={15.5}
          />
        </div>
      </div>
      <div className="lg:hidden flex w-full  px-6 max-w-[450px] mx-auto ">
        <TestimonialCarousel />
      </div>
    </div>
  );
};

export default TestimonialsSection;
