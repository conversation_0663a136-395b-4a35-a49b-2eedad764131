import React, { useState } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "./Sidebar";
import Navbar from "./Navbar";
import { useSelector } from "react-redux";

const Layout = () => {
  const [isHide, setIsHide] = useState(false);
  const userInfo = useSelector((state) => state.auth?.user)?.role || "";

  return (
    <>
      <Sidebar
        userType={userInfo}
        isHide={isHide}
        setIsHide={setIsHide}
      />
      <div className={isHide ? "sm:ml-24 p-0" : "sm:ml-64 p-0"}>
        <Navbar />
        <div className="p-2">
          <Outlet />
        </div>
      </div>
    </>
  );
};

export default Layout;
