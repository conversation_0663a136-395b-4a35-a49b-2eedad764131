const Contact = require("../models/Contact");
const {
  recruiterDetailTemplate,
} = require("../templates/email/SendContactDetails");
const mailSender = require("../utils/mailSender");
const UAParser = require("ua-parser-js");

//* @Desc Save contact form details
//* @Route POST /api/v1/contact/save-contact
//* @Access Public
exports.saveContact = async (req, res) => {
  try {
    const {
      name,
      email,
      country,
      countryCode,
      phone,
      message,
      contactPersonType,
      source,
    } = req.body;

    if (!name || !email || !country || !message || !source || !phone) {
      return res
        .status(400)
        .json({ message: "Please fill all the mandatory fields" });
    }

    // Device details
    const parser = new UAParser(req.headers["user-agent"]);
    const { browser, os } = parser.getResult();
    const ipAddress = req.headers["x-forwarded-for"] || req.ip;
    const meta = {
      browser: browser?.name || "",
      os: os?.name || "",
      ipAddress,
    };

    const removeCountryCode = phone?.slice(countryCode?.length || 0);

    const contact = await Contact.create({
      name,
      email,
      country,
      countryCode,
      phone: removeCountryCode,
      message,
      contactPersonType,
      source,
      meta,
    });

    if (!contact) {
      return res.status(500).json({ message: "Failed to save contact" });
    }

    const sendAdminEmail = await mailSender(
      "<EMAIL>",
      "New Contact Form Submission",
      recruiterDetailTemplate(contact)
    );

    if (!sendAdminEmail) {
      console.error("Unable to send admin email");
      return res.status(201).json({
        message: "Contact saved, but email failed to send.",
        contact,
      });
    }

    return res.status(201).json({
      message: "Contact saved successfully",
      contact,
    });
  } catch (error) {
    console.error("Error saving contact:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

//* @Desc Get all contact Form Submissions
//* @Route GET /api/v1/contact/get-all-contact-form-submissions
//* @Access Private - Head Account Manager
exports.getAllContactFormSubmissions = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    // Convert to numbers and validate
    const pageNumber = Math.max(1, parseInt(page));
    const limitNumber = Math.max(1, parseInt(limit));

    // Calculate skip value for pagination
    const skip = (pageNumber - 1) * limitNumber;

    // Get total count for pagination info
    const totalContacts = await Contact.countDocuments();

    // Get paginated contacts
    const contacts = await Contact.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNumber);

    if (!contacts || contacts.length === 0) {
      return res.status(404).json({ message: "No contact submissions found" });
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalContacts / limitNumber);
    const hasNextPage = pageNumber < totalPages;
    const hasPrevPage = pageNumber > 1;

    return res.status(200).json({
      message: "Contact submissions retrieved successfully",
      contacts,
      pagination: {
        currentPage: pageNumber,
        totalPages,
        totalContacts,
        limit: limitNumber,
        hasNextPage,
        hasPrevPage,
      },
    });
  } catch (error) {
    console.error("Error retrieving contact submissions:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
};
