import React from "react";

const JOB_TYPE_OPTIONS = [
  { label: "Full Time", value: "full-time" },
  { label: "Contract Base", value: "contract" },
];

const JobTypeDropdown = ({ selected, setSelected, onClose }) => {
  function toggleOption(value) {
    setSelected((prev) =>
      prev.includes(value)
        ? prev.filter((v) => v !== value)
        : [...prev, value]
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border p-4 w-[220px] max-w-full box-border overflow-visible">
      <div className="flex items-center justify-between mb-2">
        <span className="font-semibold text-gray-800 text-base">Job Type</span>
        <button
          className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
          onClick={onClose}
          aria-label="Close"
          type="button"
        >
          ×
        </button>
      </div>
      <div className="border-b mb-2" />
      <div className="flex flex-col gap-2">
        {JOB_TYPE_OPTIONS.map((opt) => (
          <label key={opt.value} className="flex items-center gap-2 text-sm cursor-pointer">
            <input
              type="checkbox"
              checked={selected.includes(opt.value)}
              onChange={() => toggleOption(opt.value)}
              className="accent-blue-500 w-4 h-4 rounded border-gray-300"
            />
            <span>{opt.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default JobTypeDropdown;
