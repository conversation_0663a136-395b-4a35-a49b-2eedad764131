import React from "react";

// Status options matching the exact submissionEnums schema
const statusOptions = [
  { value: "submitted", label: "Submitted" },
  { value: "Talent Pool", label: "Talent Pool" },
  { value: "reviewing", label: "Reviewing" },
  { value: "submitted to client", label: "Submitted to Client" },
  { value: "selected", label: "Selected" },
  { value: "interviewing", label: "Interviewing" },
  { value: "awaiting offer", label: "Awaiting Offer" },
  { value: "rejected", label: "Rejected" },
  { value: "offer released", label: "Offer Released" },
  { value: "offer accepted", label: "Offer Accepted" },
  { value: "offer rejected", label: "Offer Rejected" },
  { value: "hired-under guarantee period", label: "Hired (Under Guarantee)" },
  {
    value: "guarantee period not completed",
    label: "Guarantee Period Not Completed",
  },
  { value: "guarantee period completed", label: "Guarantee Period Completed" },
];

const StatusDropdown = ({ selected, setSelected, onClose }) => {
  function toggleStatus(statusValue) {
    // Allow multiple selection - toggle status in/out of array
    setSelected(
      (prev) =>
        prev.includes(statusValue)
          ? prev.filter((s) => s !== statusValue) // Remove if exists
          : [...prev, statusValue] // Add to existing array
    );
  }

  function removeStatus(statusValue) {
    setSelected((prev) => prev.filter((s) => s !== statusValue));
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border p-4 w-72 max-w-full relative">
      <div className="flex items-center justify-between mb-2">
        <span className="font-semibold text-gray-800 text-base">Status</span>
        <button
          className="text-gray-400 hover:text-gray-700 text-lg"
          onClick={onClose}
          aria-label="Close"
        >
          ×
        </button>
      </div>

      {/* Selected status chips */}
      {selected.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selected.map((statusValue) => {
            const statusOption = statusOptions.find(
              (opt) => opt.value === statusValue
            );
            return (
              <span
                key={statusValue}
                className="flex items-center bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-medium max-w-[140px] truncate"
                title={statusOption?.label || statusValue}
              >
                {statusOption?.label || statusValue}
                <button
                  className="ml-1 text-blue-400 hover:text-blue-700 focus:outline-none"
                  onClick={() => removeStatus(statusValue)}
                  tabIndex={-1}
                >
                  ×
                </button>
              </span>
            );
          })}
        </div>
      )}

      <div className="border-b mb-2" />
      <div className="flex flex-col gap-2 max-h-60 overflow-y-auto">
        {statusOptions.map((status) => (
          <label
            key={status.value}
            className={`flex items-center gap-2 text-sm cursor-pointer px-2 py-1 rounded ${
              selected.includes(status.value)
                ? "bg-blue-50 text-blue-700 font-medium"
                : "hover:bg-gray-50"
            }`}
          >
            <input
              type="checkbox"
              checked={selected.includes(status.value)}
              onChange={() => toggleStatus(status.value)}
              className="accent-blue-600 w-4 h-4 rounded border-gray-300"
            />
            <span className="truncate">{status.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default StatusDropdown;
