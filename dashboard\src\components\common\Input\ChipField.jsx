import { useState } from "react";

const ChipField = ({
  label,
  required = false,
  placeholder = "",
  value = [],
  onChange,
  errorMessage,
  css = "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm placeholder-gray-400",
}) => {
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (e) => {
    if ((e.key === "Enter" || e.key === ",") && inputValue.trim() !== "") {
      e.preventDefault();
      if (!value.includes(inputValue.trim())) {
        onChange([...value, inputValue.trim()]);
        setInputValue("");
      }
    } else if (e.key === "Backspace" && inputValue === "" && value.length) {
      onChange(value.slice(0, -1));
    }
  };

  const removeChip = (chip) => {
    onChange(value.filter((item) => item !== chip));
  };

  return (
    <div className="flex flex-col space-y-1">
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-800">*</span>}
        </label>
      )}

      <div className={`flex flex-wrap gap-1 items-center ${css}`}>
        {value.map((chip, index) => (
          <span
            key={index}
            className="flex items-center bg-blue-100 text-blue-800 text-sm rounded-full px-2 py-1"
          >
            {chip}
            <button
              type="button"
              onClick={() => removeChip(chip)}
              className="ml-1 text-blue-600 hover:text-blue-800 cursor-pointer"
            >
              ×
            </button>
          </span>
        ))}
        <input
          type="text"
          className="flex-grow min-w-[100px] outline-none text-sm placeholder-gray-400 bg-transparent"
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
        />
      </div>

      {errorMessage && (
        <span className="text-sm text-red-600">{errorMessage}</span>
      )}
    </div>
  );
};

export default ChipField;
