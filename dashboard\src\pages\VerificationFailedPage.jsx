import EmailStatusCard from "../components/common/cards/EmailStatusCard";
import { useSearchParams } from "react-router-dom";
import { resendVerification } from "../services/operations/userAPI";

const VerificationFailedPage = () => {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("userId");

  const handleResend = async () => {
    try {
      await resendVerification({ userId: userId });
    } catch (err) {
      console.error("Error resending verification email:", err);
    }
  };
  return (
    <EmailStatusCard
      image="/assets/images/verification-failed.svg"
      title={
        <span className="text-red-600">Oops! Email Verification Failed</span>
      }
      message={
        "Please check your email and verify first. If didn't recieved the email, click the button below to resend the verification link and complete the process."
      }
      label="Resend Link"
      onClick={handleResend}
      footer={true}
    />
  );
};

export default VerificationFailedPage;
