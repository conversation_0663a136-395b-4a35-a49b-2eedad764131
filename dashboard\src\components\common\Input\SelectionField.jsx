import React from "react";
import Select from "react-select";

const SelectField = ({
  label,
  name,
  id,
  placeholder,
  value,
  required = false,
  onChange,
  errorMessage,
  options = [],
  onBlur,
  disable = false,
  css = "focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm text-gray-700",
}) => {
  function Change(selectedOption) {
    const event = {
      target: {
        name,
        value: selectedOption ? selectedOption.value : "",
      },
    };
    onChange(event);
  }

  const normalizedOptions = options?.map((opt) =>
    typeof opt === "string" ? { value: opt, label: opt } : opt
  );

  // normalized options
  const selectedValue =
    normalizedOptions.find((opt) => opt.value === value) || null;

  return (
    <div className="flex flex-col space-y-1">
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-800">*</span>}
        </label>
      )}
      <Select
        id={id}
        placeholder={placeholder}
        name={name}
        options={normalizedOptions}
        onChange={Change}
        onBlur={onBlur}
        className={css}
        isDisabled={disable}
        value={selectedValue}
      />
      {/* <select
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        disabled={disable}
        onBlur={onBlur}
        className={css}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((opt, index) => (
          <option key={index} value={opt.value || opt}>
            {opt.label || opt}
          </option>
        ))}
      </select> */}
      {errorMessage && (
        <div className="text-red-500 text-xs mt-1">{errorMessage}</div>
      )}
    </div>
  );
};

export default SelectField;
