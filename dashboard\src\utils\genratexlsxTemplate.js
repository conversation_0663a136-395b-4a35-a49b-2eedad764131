export const handleDownload = async (headerName = [], templateName) => {
  try {
    const XLSX = await import("xlsx");

    const header = [headerName];
    const worksheet = XLSX.utils.aoa_to_sheet(header);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Template");
    XLSX.writeFile(workbook, `${templateName}.xlsx`);
  } catch (error) {
    console.log(error);
  }
};
