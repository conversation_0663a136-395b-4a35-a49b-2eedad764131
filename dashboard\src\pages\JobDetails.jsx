import { useEffect, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import TabNav from "../components/common/TabNav/TabNav";
import JobCard from "../components/common/cards/JobCard";
import JobOverviewCard from "../components/common/Job/JobOverviewCard";
import { getJobByID } from "../services/operations/jobAPI";
import { useDispatch, useSelector } from "react-redux";
import DropDownButton from "../components/common/Button/DropDownButton";
import InputField from "../components/common/Input/InputField";
import {
  getAllCandidate,
  getcandidateWithoutSubmission,
} from "../services/operations/candidateAPI";
import { useNavigate, useLocation } from "react-router-dom";
import { getRelativeTime } from "../utils/RelativeTimeFormatter";
import ColorSelectionInput from "../components/common/Input/ColorSelectionInput";
import TableHeader from "../components/common/Table/TableHeader";
import Chat from "../components/common/chats/Chat";
import ChatSectionAccountManager from "../components/common/chats/ChatSectionAccountManager";
import { setCandidateID } from "../redux/reducer/candidate.slice";
import ThreeDot from "../components/common/Button/ThreeDot";
import Modal from "../components/common/Modal";
import ChatOverlay from "../components/common/chats/ChatOverlay";
import { Download } from "lucide-react";
import { JobStatusOptions } from "../utils/JobStatusOptions";
import Timeline from "./HeadAccountManager/Timeline";
import CandidateCard from "../components/common/cards/CandidateCard";
const tabKeys = ["submissions", "job-details", "updates", "chat"];
const defaultTab = "job-details";

const JobDetails = () => {
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const [job, setJob] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTimeLineModalOpen, setIsTimeLineModalOpen] = useState(false);
  const [modalUser, setModalUser] = useState(null);

  const jobId = searchParams.get("jobId");
  const currentTab = searchParams.get("tab");
  const istimeline = searchParams.get("istimeline");
  const submissionIDs = searchParams.get("submissionId");
  const userInfo = useSelector((item) => item.auth.user);
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);

  const modalHandleClose = () => {
    setIsModalOpen(false);
  };

  // Always ensure tab param is present in URL
  useEffect(() => {
    if (!currentTab) {
      setSearchParams(
        {
          tab: defaultTab,
          jobId,
          istimeline: istimeline == "true" ? true : false,
          submissionId: submissionIDs,
        },
        { replace: true }
      );
    }
  }, [currentTab, setSearchParams, jobId, istimeline, submissionIDs]);

  const [activeTab, setActiveTab] = useState(() =>
    tabKeys.indexOf(currentTab || defaultTab)
  );

  // submission url page opening
  useEffect(() => {
    if (istimeline && submissionIDs && job) {
      setIsTimeLineModalOpen(istimeline == "true" ? true : false);
      const submissionsDetails = job?.candidatesubmissions
        ?.map((submission) => {
          return {
            _id: submission?._id,
            Candidate: submission?.candidate?.candidateID,
            name: `${submission?.candidate?.personalDetails?.firstName} ${submission?.candidate?.personalDetails?.lastName}`,
            submissionId: submission?.submissionId,
            status: submission?.status,
            resume: submission?.candidate?.documentAttachments?.resume,
            candidateID: submission?.candidate?.candidateID,
            email: submission?.candidate?.personalDetails?.emailAddress,
            recruiter: `${submission?.submittedBy?.name?.firstName} ${submission?.submittedBy?.name?.lastName}`,
          };
        })
        ?.filter((item) => submissionIDs == item?.submissionId)[0];
      setModalUser(submissionsDetails);
    }
  }, [istimeline, submissionIDs, job]);

  useEffect(() => {
    const tab = searchParams.get("tab") || defaultTab;
    const idx = tabKeys.indexOf(tab);
    setActiveTab(idx === -1 ? 1 : idx);
  }, [searchParams]);

  const handleTabChange = (index) => {
    setSearchParams({ tab: tabKeys[index], jobId });
  };

  useEffect(() => {
    const fetchJobDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getJobByID(jobId);
        if (response?.success) {
          setJob(
            Array.isArray(response.data) ? response.data[0] : response.data
          );
        } else {
          setError(response?.message || "Failed to fetch job details.");
        }
      } catch (error) {
        setError("Error fetching job details.");
      } finally {
        setLoading(false);
      }
    };
    if (jobId) fetchJobDetails();
    else setLoading(false);
  }, [jobId]);

  const tabs = [
    { name: <span>Submissions</span>, css: "" },
    { name: <span>Job Details</span>, css: "" },
    { name: <span>Updates</span>, css: "" },
    { name: <span>Chat</span>, css: "" },
  ];

  function formatCustomDate(dateInput) {
    const date = new Date(dateInput);

    if (isNaN(date)) return "Invalid date";

    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dayName = days[date.getDay()];
    const dayOfMonth = date.getDate();
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";

    hours = hours % 12 || 12;

    return `${dayName} ${dayOfMonth}, ${year} | ${hours}:${minutes} ${ampm}`;
  }

  function getUpdatedFieldsList(
    data,
    renameMap = {},
    ignoreFields = ["updatedAt"]
  ) {
    const updatedFields = [];

    for (const key in data) {
      if (ignoreFields.includes(key)) continue;

      const mappedKey = renameMap[key] || key;
      updatedFields.push(mappedKey);
    }

    return updatedFields;
  }

  const [isActiveSearchCandidate, setIsActiveSearchCandidate] = useState(false);
  const [searchCandidateInput, setSearchCandidateInput] = useState("");
  const [userSearch, setUserSearch] = useState([]);

  const filterData = (item) => {
    if (!searchCandidateInput) return true;

    const lowerSearch = searchCandidateInput.toLowerCase();
    return Object.values(item).some((val) =>
      String(val).toLowerCase().includes(lowerSearch)
    );
  };

  const filteredUsers = userSearch.filter(filterData).slice(0, 10);

  async function getCandidate(jobid) {
    try {
      const response = await getcandidateWithoutSubmission(jobid);
      setUserSearch(
        response?.data?.map((item) => {
          return {
            candidateID: item?.candidateID,
            name: `${item?.personalDetails.firstName} ${item?.personalDetails.lastName}`,
          };
        })
      );
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <div className="w-full mx-auto p-2 space-y-6 ">
      <JobOverviewCard job={job} />
      <TabNav
        nav={tabs}
        active={activeTab}
        rightSidebar={
          userInfo?.role == "recruiter" &&
          job?.isWorkingOn && (
            <DropDownButton
              buttonSize="w-[168px]"
              button={
                <>
                  <span className="text-sm font-medium">Submit Candidate</span>
                  <svg
                    className="w-4 h-4 "
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </>
              }
              buttonDropDown={
                <>
                  <ul className="py-0" role="none">
                    <li>
                      <div
                        className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        role="menuitem"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsActiveSearchCandidate(false);
                          navigate(
                            `/candidate/addcandidate?step=0&jobId=${jobId}`
                          );
                        }}
                      >
                        <img
                          src="/assets/icons/add_candidate.svg"
                          alt="Download Full-time Template"
                        />
                        New Candidate
                      </div>
                    </li>
                    <li>
                      <div
                        className={`flex  items-center justify-start  gap-2 px-4 py-2 text-sm ${
                          isActiveSearchCandidate
                            ? "text-[#296600] cursor-not-allowed"
                            : "text-gray-700 hover:bg-gray-100 cursor-pointer"
                        } `}
                        role="menuitem"
                        onClick={(e) => {
                          e.preventDefault();
                          getCandidate(jobId);
                          setIsActiveSearchCandidate(true);
                        }}
                      >
                        <img
                          src="/assets/icons/edit_candidate.svg"
                          alt="Download Full-time Template"
                        />
                        Existing Candidate
                      </div>
                    </li>
                    {isActiveSearchCandidate && (
                      <li>
                        <div className="m-2">
                          <InputField
                            placeholder={"Search by name"}
                            onChange={(e) => {
                              setSearchCandidateInput(e.target.value);
                            }}
                            value={searchCandidateInput}
                          />
                        </div>
                        {filteredUsers &&
                          filteredUsers?.map((item) => {
                            return (
                              <div
                                className="text-sm text-start mx-3 my-2 cursor-pointer hover:text-[#296600]"
                                onClick={(e) => {
                                  e.preventDefault();
                                  dispatch(setCandidateID(item?.candidateID));
                                  navigate(
                                    `/candidate/addcandidate?step=0&jobId=${jobId}&candidateID=${item?.candidateID}`
                                  );
                                }}
                              >
                                {item?.name}
                              </div>
                            );
                          })}
                      </li>
                    )}
                  </ul>
                </>
              }
            />
          )
        }
        setActive={handleTabChange}
      />
      {activeTab === 0 && (
        <div className="p-0">
          <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
            <table className="min-w-full divide-y divide-gray-200 text-sm text-left">
              <TableHeader
                columns={
                  userInfo.role == "recruiter"
                    ? [
                        "Candidate ID",
                        "Name",
                        "submission ID",
                        "Status",
                        "Resume",
                        "Action",
                      ]
                    : [
                        "Candidate ID",
                        "Name",
                        "submission ID",
                        "Status",
                        "Resume",
                        "",
                      ]
                }
              />
              <tbody>
                {job?.candidatesubmissions
                  ?.map((submission) => {
                    console.log("Submission Item:", submission);
                    return {
                      Candidate: submission?.candidate?.candidateID,
                      name: `${submission?.candidate?.personalDetails?.firstName} 
                ${submission?.candidate?.personalDetails?.lastName}`,
                      submissionId: submission?.submissionId,
                      status: submission?.status,
                      resume:
                        submission?.candidate?.documentAttachments?.resume,
                      candidateID: submission?.candidate?.candidateID,
                      email:
                        submission?.candidate?.personalDetails?.emailAddress,
                      recruiter: `${submission?.submittedBy?.name?.firstName} 
                ${submission?.submittedBy?.name?.lastName}`,
                      _id: submission?._id,
                    };
                  })
                  ?.map((submissionItem, index, items) => (
                    <tr
                      className=" cursor-pointer border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800"
                      key={index}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        navigate(
                          `/submissions/submissionDetails?candidateId=${submissionItem?.candidateID}&submissionId=${submissionItem.submissionId}`
                        );
                      }}
                    >
                      {Object.keys(submissionItem)
                        .filter(
                          (item) =>
                            item !== "resume" &&
                            item !== "candidateID" &&
                            item !== "email" &&
                            item !== "recruiter" &&
                            item !== "_id"
                        )
                        .map((item) => (
                          <td className="px-4 py-3 text-start" key={item}>
                            {item === "status" ? (
                              <ColorSelectionInput
                                value={submissionItem[item]}
                                width={"w-40"}
                                disabled={true}
                                options={JobStatusOptions}
                              />
                            ) : (
                              submissionItem[item]
                            )}
                          </td>
                        ))}
                      {/* Resume column */}
                      <td className="px-4 py-3 text-start">
                        {submissionItem.resume ? (
                          <a
                            href={submissionItem?.resume}
                            target="_blank"
                            rel="noopener noreferrer"
                            title="Download Resume"
                            className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                            download
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        ) : (
                          <span className="text-gray-400">—</span>
                        )}
                      </td>
                      {/* Action column */}
                      {userInfo?.role == "recruiter" && (
                        <td className="flex items-center h-full gap-4 px-3 py-4 w-full">
                          <div className="cursor-pointer">
                            <img
                              src="/assets/icons/chat.svg"
                              alt="Chat"
                              onClick={(e) => {
                                e.stopPropagation();

                                setModalUser(submissionItem);
                                setIsModalOpen(true);
                              }}
                            />
                          </div>
                          <div className="cursor-pointer">
                            <img
                              src="/assets/icons/history.svg"
                              alt="History"
                              onClick={(e) => {
                                // setModalUser(submissionItem);
                                e.stopPropagation();

                                setSearchParams(
                                  {
                                    tab: "submissions",
                                    jobId,
                                    istimeline: true,
                                    submissionId: submissionItem?.submissionId,
                                  },
                                  { replace: true }
                                );
                                // setIsTimeLineModalOpen(true);
                              }}
                            />
                          </div>
                        </td>
                      )}

                      {/* ThreeDot at the end */}
                      {userInfo.role !== "recruiter" && (
                        <td className="px-4 py-3 text-start">
                          <ThreeDot
                            dropdownSize="w-32"
                            buttonDropDown={
                              <>
                                <ul className="py-0" role="none">
                                  <li>
                                    <div
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        navigate(
                                          `/submissions/submissionDetails?candidateId=${submissionItem?.candidateID}&submissionId=${submissionItem?.submissionId}`
                                        );
                                      }}
                                      className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                                      role="menuitem"
                                    >
                                      <img
                                        src="/assets/icons/view.svg"
                                        alt="View"
                                      />
                                      View
                                    </div>
                                  </li>
                                </ul>
                              </>
                            }
                          />
                        </td>
                      )}
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {activeTab === 1 && job && (
        <>
          <JobCard title="Details" defaultOpen={true}>
            <div className="grid grid-cols-5 gap-x-6 gap-y-4 text-sm text-gray-800 mb-4 text-left">
              <div>
                <div className="font-medium">Country:</div>
                <div>{job.location?.country || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">State:</div>
                <div>{job.location?.state || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Location:</div>
                <div>{job.location?.city || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Zip Code:</div>
                <div>{job.location?.zipCode || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Job Status:</div>
                <div>{job.jobStatus || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Hours/Week:</div>
                <div>{job.requiredHoursPerWeek || "N/A"}</div>
              </div>

              {userInfo.role !== "recruiter" && job.payRate && (
                <div>
                  <div className="font-medium">Client Bill Rate:</div>
                  <div>{job.payRate || "N/A"}</div>
                </div>
              )}

              {userInfo.role !== "recruiter" && job.clientname && (
                <div>
                  <div className="font-medium">Client Name:</div>
                  <div>{job?.clientname || "N/A"}</div>
                </div>
              )}
              {userInfo.role !== "recruiter" && job?.externalJobId && (
                <div>
                  <div className="font-medium">External JobID:</div>
                  <div>{job?.externalJobId || "N/A"}</div>
                </div>
              )}

              <div>
                <div className="font-medium">Pay Rate</div>
                {job.salary?.min && job.salary?.max
                  ? `${job.salary.min} - ${job.salary.max} ${
                      job.salary.currency || ""
                    }`
                  : "N/A"}
              </div>
              <div>
                <div className="font-medium">Remote Job:</div>
                <div>{job.remote ? "Yes" : "No"}</div>
              </div>
            </div>
          </JobCard>

          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <JobCard title="Benefits" defaultOpen={true}>
                <ul className="list-disc pl-5 space-y-1">
                  {(job.benefits || []).map((benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </JobCard>
            </div>
            <div className="flex-1">
              <JobCard title="Skills" defaultOpen={true}>
                <ul className="list-disc pl-5 space-y-1">
                  {(job.primarySkills || []).map((skill, i) => (
                    <li key={i}>{skill}</li>
                  ))}
                </ul>
              </JobCard>
            </div>
          </div>

          <JobCard title="Job Description" defaultOpen={true}>
            <p className="leading-relaxed">
              <span
                dangerouslySetInnerHTML={{
                  __html: job?.jobDescription || "N/A",
                }}
              />
            </p>
          </JobCard>
        </>
      )}
      {activeTab === 2 && job && (
        <div className="space-y-4">
          <JobCard title="Details" defaultOpen={true}>
            <ul className="space-y-4 text-sm text-gray-700">
              {job?.updates?.map((item, idx) => (
                <li key={idx} className="flex justify-between items-start">
                  <div className="flex items-start gap-2">
                    <div className="mt-1 w-2 h-2 bg-gray-400 rounded-full" />
                    <div>
                      <div className="font-medium text-gray-800">
                        Job details update {getRelativeTime(item?.createdAt)}
                      </div>
                      <p className="text-gray-600">
                        The following fields have been updated in the job
                        record:{" "}
                        {getUpdatedFieldsList(
                          item.updateFields,
                          {
                            jobStatus: "Status",
                            jobDescription: "Description",
                          },
                          ["updatedAt"]
                        ).join(", ")}
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 whitespace-nowrap mt-1">
                    {formatCustomDate(item?.createdAt)}
                  </div>
                </li>
              ))}
            </ul>
          </JobCard>
        </div>
      )}
      {/* {activeTab === 3 && (
        <div className="p-4 text-gray-600">
          <p>Chat feature coming soon.</p>
        </div>
      )} */}
      {activeTab === 3 && userInfo?.role === "recruiter" && <Chat job={job} />}{" "}
      {((activeTab === 3 && userInfo?.role === "accountManager") ||
        (activeTab === 3 && userInfo?.role === "headAccountManager")) && (
        <ChatSectionAccountManager job={job} />
      )}
      <Modal
        children={
          <ChatOverlay
            job={job}
            submissionId={modalUser?.submissionId}
            submission={modalUser}
          />
        }
        isOpen={isModalOpen}
        onClose={modalHandleClose}
      />
      <Modal
        width={"p-1 w-full max-w-2xl"}
        children={
          isTimeLineModalOpen && (
            <Timeline
              submissionId={modalUser?.submissionId}
              header={
                <>
                  <CandidateCard
                    candidateStatus={modalUser?.status}
                    name={modalUser?.name}
                    email={modalUser?.email}
                    recruiter={modalUser?.recruiter}
                  />
                </>
              }
              isSidebar={true}
            />
          )
        }
        isOpen={isTimeLineModalOpen}
        onClose={() => {
          setModalUser(null);
          setIsTimeLineModalOpen(false);
          setSearchParams(
            { tab: "submissions", jobId, istimeline: false },
            { replace: true }
          );
        }}
      />
    </div>
  );
};

export default JobDetails;
