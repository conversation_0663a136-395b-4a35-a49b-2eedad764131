import React, { useState } from "react";
import SignUpRightSection from "../../../common/SignUpRightSection";
import { forgotPassword } from "../../../../services/operations/authAPI";
import { toast } from "react-toastify";
import { Link } from "react-router-dom";

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await forgotPassword(email);
      if (response?.success) {
        setSent(true);
      } else {
        toast.error(response?.message || "Failed to send reset link.");
      }
    } catch (error) {
      console.error("Error sending reset link:", error);
      toast.error(
        error?.message || "An error occurred while sending the reset link."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <section className="w-[100%] md:w-[60%] lg:w-[50%] py-15 flex items-center justify-center">
      <div className="w-[80%] md:w-[60%] lg:w-[45%] mx-auto flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <img
            src={"/assets/images/icon.png"}
            alt="icon"
            width={50}
            height={100}
          />
          <h2 className="text-4xl font-bold">Forgot Password?</h2>
          <p className="text-gray-600 text-sm leading-5">
            Enter your email address and we will send you a reset link.
          </p>
        </div>
        {sent ? (
          <>
            <div className="text-green-600 text-center">
              A reset link has been sent to your email. Please check your inbox
              and follow the instructions to reset your password.
            </div>
            <div className="w-full flex justify-center items-center gap-1">
              Go to
              <Link
                to={"/login"}
                className="text-sm text-blue-600 underline hover:text-blue-800 "
              >
                Sign In
              </Link>
            </div>
          </>
        ) : (
          <form
            onSubmit={handleSubmit}
            className="flex flex-col items-center justify-center gap-6"
          >
            <div className="flex flex-col w-full gap-2">
              <label
                htmlFor="email"
                className="text-sm font-medium text-[#374151]"
              >
                Email Address
              </label>
              <input
                type="email"
                name="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="border border-[#D1D5DB] py-2 px-3 rounded-md focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <button
              type="submit"
              className={`w-full py-2 px-3 rounded-md ${
                email
                  ? "bg-[#65FF00] text-[#102108] font-medium cursor-pointer"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
              disabled={!email || loading}
            >
              {loading ? "Sending Link..." : "Send Link"}
            </button>
          </form>
        )}
      </div>
      <SignUpRightSection />
    </section>
  );
};

export default ForgotPassword;
