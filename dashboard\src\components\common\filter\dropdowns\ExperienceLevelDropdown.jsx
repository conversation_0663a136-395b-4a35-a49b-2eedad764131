import React, { useState, useEffect, useRef } from "react";

const ExperienceLevelDropdown = ({
  label,
  selectedValues,
  filterKey,
  onLocalChange,
  min = 1,
  max = 12,
  step = 1,
  prefix = "",
  suffix = "",
  showInputFields = false,
}) => {
  // Convert selectedValues array to range format [min, max]
  const parseSelectedValues = (values) => {
    if (!values || values.length === 0) return [min, max];

    // If it's already in range format like ["1-12"]
    if (values.length === 1 && values[0].includes('-')) {
      const [minVal, maxVal] = values[0].split('-').map(Number);
      return [minVal || min, maxVal || max];
    }

    // Default range
    return [min, max];
  };

  const [range, setRange] = useState(parseSelectedValues(selectedValues));
  const [isOpen, setIsOpen] = useState(false);
  const [minInput, setMinInput] = useState(range[0].toString());
  const [maxInput, setMaxInput] = useState(range[1].toString());
  const wrapperRef = useRef(null);
  const sliderRef = useRef(null);

  // Sync with parent when selectedValues change
  useEffect(() => {
    const newRange = parseSelectedValues(selectedValues);
    setRange(newRange);
    setMinInput(newRange[0].toString());
    setMaxInput(newRange[1].toString());
  }, [selectedValues, min, max]);

  // Scroll the slider so both handles are always visible (centered if possible)
  useEffect(() => {
    if (sliderRef.current) {
      const percent = ((range[0] + range[1]) / 2 - min) / (max - min);
      const scrollWidth = sliderRef.current.scrollWidth - sliderRef.current.clientWidth;
      sliderRef.current.scrollLeft = percent * scrollWidth;
    }
  }, [range, min, max]);

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleMinChange = (e) => {
    const newMin = Number(e.target.value);
    if (newMin <= range[1]) {
      const newRange = [newMin, range[1]];
      setRange(newRange);
      setMinInput(newMin.toString());
      setMaxInput(range[1].toString());
      // Convert back to string format for consistency with other filters
      onLocalChange(filterKey, [`${newRange[0]}-${newRange[1]}`]);
    }
  };

  const handleMaxChange = (e) => {
    const newMax = Number(e.target.value);
    if (newMax >= range[0]) {
      const newRange = [range[0], newMax];
      setRange(newRange);
      setMinInput(range[0].toString());
      setMaxInput(newMax.toString());
      // Convert back to string format for consistency with other filters
      onLocalChange(filterKey, [`${newRange[0]}-${newRange[1]}`]);
    }
  };

  const handleMinInputChange = (e) => {
    const value = e.target.value;
    setMinInput(value);

    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue >= min && numValue <= range[1]) {
      const newRange = [numValue, range[1]];
      setRange(newRange);
      const rangeString = `${newRange[0]}-${newRange[1]}`;
      onLocalChange(filterKey, [rangeString]);
    }
  };

  const handleMaxInputChange = (e) => {
    const value = e.target.value;
    setMaxInput(value);

    const numValue = parseInt(value);
    if (!isNaN(numValue) && numValue <= max && numValue >= range[0]) {
      const newRange = [range[0], numValue];
      setRange(newRange);
      const rangeString = `${newRange[0]}-${newRange[1]}`;
      onLocalChange(filterKey, [rangeString]);
    }
  };

  const closeDropdown = () => {
    setIsOpen(false);
  };

  const isDefaultRange = range[0] === min && range[1] === max;

  return (
    <div className="relative" ref={wrapperRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer min-w-[180px]"
      >
        <div className="flex items-center gap-2">
          <span className="text-[#2E90FA] font-medium">{label}:</span>
          <span className="text-gray-700">
            {!isDefaultRange ? `${prefix}${range[0]}${suffix}-${prefix}${range[1]}${suffix}` : "None"}
          </span>
        </div>
        <svg
          className={`w-4 h-4 text-[#94A3B8] transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg border p-4 w-[400px] max-w-full box-border z-50">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium text-gray-700 text-sm">{label}</span>
            <button
              className="text-gray-400 hover:text-black text-lg focus:outline-none"
              onClick={closeDropdown}
              aria-label="Close"
              type="button"
            >
              ×
            </button>
          </div>
          <div className="border-b mb-2" />
          <div className="flex flex-col gap-4 px-2 py-4">
            <div
              ref={sliderRef}
              className="relative flex items-center w-full"
              style={{ minHeight: 64, minWidth: 0, overflow: 'visible' }}
            >
              {/* Track */}
              <div className="absolute left-0 right-0 top-1/2 h-2 bg-gray-100 rounded-full" style={{ zIndex: 0, transform: 'translateY(-50%)' }} />
              {/* Selected range highlight */}
              <div
                className="absolute top-1/2 h-2 bg-blue-500 rounded-full"
                style={{
                  left: `${((range[0] - min) / (max - min)) * 100}%`,
                  width: `${((range[1] - range[0]) / (max - min)) * 100}%`,
                  zIndex: 1,
                  transform: 'translateY(-50%)',
                }}
              />
              {/* Min handle */}
              <input
                type="range"
                min={min}
                max={max}
                value={range[0]}
                onChange={handleMinChange}
                className="w-full accent-blue-500 bg-transparent"
                style={{ zIndex: 3, pointerEvents: 'auto', position: 'absolute', left: 0, top: 0 }}
              />
              {/* Max handle */}
              <input
                type="range"
                min={min}
                max={max}
                value={range[1]}
                onChange={handleMaxChange}
                className="w-full accent-blue-500 bg-transparent"
                style={{ zIndex: 2, pointerEvents: 'auto', position: 'absolute', left: 0, top: 24 }}
              />
              {/* Value bubbles */}
              <div
                className="absolute"
                style={{ left: `calc(${((range[0] - min) / (max - min)) * 100}% - 12px)`, top: -32, zIndex: 10 }}
              >
                <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-sm">{prefix}{range[0]}{suffix}</div>
              </div>
              <div
                className="absolute"
                style={{ left: `calc(${((range[1] - min) / (max - min)) * 100}% - 12px)`, top: 32, zIndex: 10 }}
              >
                <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-sm">{prefix}{range[1]}{suffix}</div>
              </div>
            </div>
          </div>

          {/* Min/Max Input Fields - Only show if showInputFields is true */}
          {showInputFields && (
            <div className="flex gap-4 mt-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  min={min}
                  max={max}
                  value={minInput}
                  onChange={handleMinInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={min.toString()}
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  min={min}
                  max={max}
                  value={maxInput}
                  onChange={handleMaxInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={max.toString()}
                />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ExperienceLevelDropdown;
