import React, { useState, useEffect, useRef } from "react";

const SortingPanel = ({
  showSorting,
  setShowSorting, // Add this prop to control the panel state
  sorting,
  setSorting,
  handleApplyFilters,
  availableSortOptions,
}) => {
  const [openDropdown, setOpenDropdown] = useState(null);

  // Local state to hold pending sorting changes
  const [pendingSorting, setPendingSorting] = useState(sorting);

  // State for the sorting selection dropdown
  const [showSortingDropdown, setShowSortingDropdown] = useState(false);
  const sortingDropdownRef = useRef(null);

  // Sync pending sorting with actual sorting when it changes (for clear all)
  useEffect(() => {
    setPendingSorting(sorting);
  }, [sorting]);

  // Handle clicks outside the sorting dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sortingDropdownRef.current &&
        !sortingDropdownRef.current.contains(event.target)
      ) {
        setShowSortingDropdown(false);
      }
    };

    if (showSortingDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSortingDropdown]);

  // Handle sorting toggle - clears sorting when unchecked
  const handleSortingToggle = (sortKey) => {
    const hasSortingValue = pendingSorting && pendingSorting !== "";

    if (hasSortingValue) {
      // If sorting has value, clear it
      setPendingSorting("");
    }
    // If no sorting value, do nothing (field stays available but empty)
  };

  // Sort option configurations with their display data
  const sortOptionConfigs = {
    jobTitle: {
      label: "Job Title",
      icon: null,
      options: ["A to Z", "Z to A"],
      mapToBackend: (option) => ({
        sortBy: "jobTitle",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    location: {
      label: "Location",
      icon: null,
      options: ["A to Z", "Z to A"],
      mapToBackend: (option) => ({
        sortBy: "location",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    jobType: {
      label: "Job Type",
      icon: null,
      options: ["Full Time First", "Contract First", "Part Time First"],
      mapToBackend: (option) => ({
        sortBy: "jobType",
        sortOrder: "asc", // Custom logic could be added here
      }),
    },
    specialization: {
      label: "Specialization",
      icon: null,
      options: ["A to Z", "Z to A"],
      mapToBackend: (option) => ({
        sortBy: "specialization",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    priority: {
      label: "Priority",
      icon: null,
      options: ["High to Low", "Low to High"],
      mapToBackend: (option) => ({
        sortBy: "priority",
        sortOrder: option === "High to Low" ? "desc" : "asc",
      }),
    },
    status: {
      label: "Status",
      icon: null,
      options: [
        "Active First",
        "Inactive First",
        "Pending First",
        "Closed First",
      ],
      mapToBackend: (option) => ({
        sortBy: "status",
        sortOrder: "asc", // Custom logic could be added here
      }),
    },
    visibility: {
      label: "Visibility",
      icon: null,
      options: ["Public First", "Private First", "Internal First"],
      mapToBackend: (option) => ({
        sortBy: "visibility",
        sortOrder: "asc", // Custom logic could be added here
      }),
    },
    submissionsCount: {
      label: "Submissions",
      icon: null,
      options: ["High to Low", "Low to High"],
      mapToBackend: (option) => ({
        sortBy: "submissionsCount",
        sortOrder: option === "High to Low" ? "desc" : "asc",
      }),
    },
    recruiterCount: {
      label: "Recruiters",
      icon: null,
      options: ["High to Low", "Low to High"],
      mapToBackend: (option) => ({
        sortBy: "recruiterCount",
        sortOrder: option === "High to Low" ? "desc" : "asc",
      }),
    },
    workOnRequestCount: {
      label: "Work Requests",
      icon: null,
      options: ["High to Low", "Low to High"],
      mapToBackend: (option) => ({
        sortBy: "workOnRequestCount",
        sortOrder: option === "High to Low" ? "desc" : "asc",
      }),
    },
    postedDate: {
      label: "Posted Date",
      icon: "/assets/images/post.png",
      options: ["Recent", "Oldest"],
      mapToBackend: (option) => ({
        postedDate: option.toLowerCase(), // "recent" or "oldest"
      }),
    },
    submissionDate: {
      // ✅ ADDED: New submission date configuration
      label: "Submission Date",
      icon: "/assets/images/post.png",
      options: ["Recent", "Oldest"],
      mapToBackend: (option) => ({
        submissionDate: option.toLowerCase(), // "recent" or "oldest"
      }),
    },
    name: {
      label: "Name",
      icon: null,
      options: ["A to Z", "Z to A"],
      mapToBackend: (option) => ({
        sortBy: "name",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    candidateID: {
      label: "Candidate ID",
      icon: null,
      options: ["Low to High", "High to Low"],
      mapToBackend: (option) => ({
        sortBy: "candidateID",
        sortOrder: option === "Low to High" ? "asc" : "desc",
      }),
    },
  };

  // Handle local sorting changes (before Apply is clicked)
  const handleLocalSortingChange = (option) => {
    setPendingSorting(option);
  };

  // Handle Apply button click - Modified to NOT close the panel
  const handleApplyClick = () => {
    // Apply the pending sorting
    setSorting(pendingSorting);
    // Pass the pending sorting value to handleApplyFilters to avoid state timing issues
    handleApplyFilters(pendingSorting);
    // Remove the panel closing logic - panel will stay open
  };

  // Handle Clear All button click - Only clear sorting, not filters
  const handleClearSorting = () => {
    setPendingSorting("");
    setSorting("");

    // Apply the changes with empty sorting
    handleApplyFilters("");

    // Close the panel after clearing
    setShowSorting(false);
  };

  const SortDropdown = ({ img, label, value, options, sortKey }) => (
    <div className="relative">
      <button
        onClick={() =>
          setOpenDropdown(openDropdown === sortKey ? null : sortKey)
        }
        className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
      >
        {img && <img src={img} alt="icon" className="w-4 h-4" />}
        <span className="text-[#2E90FA] font-medium">{label}:</span>
        <span>{value || "None"}</span>
        <svg
          className="w-4 h-4 text-[#94A3B8]"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {openDropdown === sortKey && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
          <div className="p-1">
            {sortKey === "submissionDate" ? (
              <>
                {options.map((option) => (
                  <div
                    key={option}
                    className={`px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer rounded flex items-center ${
                      value === option ? "bg-blue-50 text-blue-700" : ""
                    }`}
                    onClick={() => {
                      handleLocalSortingChange(option);
                      setOpenDropdown(null);
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={value === option}
                      className="mr-2 cursor-pointer"
                      readOnly
                    />
                    {option}
                  </div>
                ))}
              </>
            ) : (
              options.map((option) => (
                <div
                  key={option}
                  className={`px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer rounded ${
                    value === option ? "bg-blue-50 text-blue-700" : ""
                  }`}
                  onClick={() => {
                    handleLocalSortingChange(option);
                    setOpenDropdown(null);
                  }}
                >
                  {option}
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );

  if (!showSorting) return null;

  // Render only available sort options for the current tab
  const renderAvailableSortOptions = () => {
    // Always show all available sorting options
    return availableSortOptions?.map((sortKey) => {
      const config = sortOptionConfigs[sortKey];
      if (!config) return null;

      return (
        <SortDropdown
          key={sortKey}
          img={config.icon}
          label={config.label}
          value={pendingSorting} // Use pending sorting for display
          options={config.options}
          sortKey={sortKey}
        />
      );
    });
  };

  // Check if any sorting is active (based on pending sorting)
  const isSortingActive = pendingSorting && pendingSorting !== "";

  // Check if there are pending changes that haven't been applied
  const hasPendingChanges = pendingSorting !== sorting;

  return (
    <div className="bg-white border-b border-[#E5E7EB] py-3">
      <div className="flex items-center justify-between gap-4 mb-2 px-4">
        <div className="flex items-center gap-3 flex-wrap">
          {renderAvailableSortOptions()}

          <div className="relative" ref={sortingDropdownRef}>
            <button
              onClick={() => setShowSortingDropdown(!showSortingDropdown)}
              className="text-gray-400 text-xs font-medium px-2 py-1 rounded hover:bg-gray-100 flex items-center gap-1 border border-gray-200"
              type="button"
            >
              {isSortingActive ? (
                <span className="flex items-center gap-1">
                  Sort
                  <span className="bg-blue-100 text-blue-600 rounded-full px-1.5 py-0.5 text-[11px] font-semibold">
                    1
                  </span>
                </span>
              ) : (
                <span className="flex items-center gap-1">+ Sort</span>
              )}
              <svg
                width="14"
                height="14"
                fill="none"
                viewBox="0 0 24 24"
                className={`transition-transform ${
                  showSortingDropdown ? "rotate-180" : ""
                }`}
              >
                <path
                  d="M6 9l6 6 6-6"
                  stroke="#888"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </button>

            {/* Sorting Selection Dropdown */}
            {showSortingDropdown && (
              <div className="absolute top-full left-0 mt-1 w-[250px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-700 mb-3">
                    Select Sorting
                  </p>
                  <div className="space-y-2">
                    {availableSortOptions?.map((sortKey) => {
                      const config = sortOptionConfigs[sortKey];
                      if (!config) return null;

                      // Check if this sort option is currently active
                      const hasSortingValue =
                        pendingSorting && pendingSorting !== "";

                      return (
                        <label
                          key={sortKey}
                          className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded"
                        >
                          <input
                            type="checkbox"
                            checked={hasSortingValue}
                            onChange={() => handleSortingToggle(sortKey)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                          />
                          <span className="flex items-center gap-2">
                            {config.label}
                            {hasSortingValue && (
                              <span className="bg-blue-100 text-blue-600 rounded-full px-1.5 py-0.5 text-[10px] font-semibold">
                                Active
                              </span>
                            )}
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sorting Action Buttons */}
      <div className="flex justify-end space-x-3 px-4">
        <button
          onClick={handleClearSorting}
          className="px-4 py-2 text-[#A4A7AE] text-sm font-inter font-medium disabled:opacity-50 cursor-pointer"
        >
          Clear All
        </button>
        <button
          onClick={handleApplyClick}
          className={`px-4 py-2 text-white rounded-md text-sm font-medium ${
            hasPendingChanges || isSortingActive
              ? "bg-[#3E9900] hover:bg-green-700"
              : "bg-gray-400 cursor-not-allowed"
          }`}
          disabled={!hasPendingChanges && !isSortingActive}
        >
          Apply
        </button>
      </div>
    </div>
  );
};

export default SortingPanel;

// Helper function to convert UI sorting to backend format
export const convertSortingToBackendFormat = (sortingOption, sortKey) => {
  const sortOptionConfigs = {
    postedDate: {
      mapToBackend: (option) => ({
        postedDate: option.toLowerCase(), // "recent" or "oldest"
      }),
    },
    submissionDate: {
      // ✅ ADDED: Support for submission date
      mapToBackend: (option) => ({
        submissionDate: option.toLowerCase(), // "recent" or "oldest"
      }),
    },
    jobTitle: {
      mapToBackend: (option) => ({
        sortBy: "jobTitle",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    priority: {
      mapToBackend: (option) => ({
        sortBy: "priority",
        sortOrder: option === "High to Low" ? "desc" : "asc",
      }),
    },
    name: {
      mapToBackend: (option) => ({
        sortBy: "name",
        sortOrder: option === "A to Z" ? "asc" : "desc",
      }),
    },
    candidateID: {
      mapToBackend: (option) => ({
        sortBy: "candidateID",
        sortOrder: option === "Low to High" ? "asc" : "desc",
      }),
    },
    // Add more as needed
  };

  const config = sortOptionConfigs[sortKey];
  if (config && config.mapToBackend) {
    return config.mapToBackend(sortingOption);
  }

  // Default fallback
  return {
    sortBy: sortKey,
    sortOrder: "asc",
  };
};
