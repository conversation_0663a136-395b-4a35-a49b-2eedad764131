import React, { useEffect, useRef, useState } from "react";

const DropDownButton = ({ button = "" ,buttonDropDown="",dropdownSize="w-56", buttonSize="w-[122px]", position="right-0"}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <>
      <div ref={dropdownRef}>
        <button
          aria-expanded={isOpen}
          onClick={() => setIsOpen(!isOpen)}
          className={`flex items-center cursor-pointer justify-center gap-2 ${buttonSize} px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700`}
        >
          {button}
        </button>

        {isOpen && (
          <div className={`absolute ${position} z-50 my-3 me-3 ${dropdownSize} text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-md`}>
            {buttonDropDown}
          </div>
        )}
      </div>
    </>
  );
};

export default DropDownButton;
