"use client";

import testimonials from "@/data/testimonials";
import TestimonialsCard from "./TestimonialsCard";
import { useState, useEffect } from "react";

const TestimonialCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToNextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const goToPreviousSlide = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + testim.length) % testim.length
    );
  };

  // Set up automatic slide change every 3 seconds
  useEffect(() => {
    const interval = setInterval(goToNextSlide, 3000); // Change every 3 seconds

    // Clear interval when component unmounts
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col gap-4 w-full overflow-hidden">
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {testimonials.map((testimonial, index) => (
            <div key={index} className="flex-shrink-0 w-full">
              <TestimonialsCard
                name={testimonial.name}
                image={testimonial.image}
                testimonial={testimonial.text}
                height={15.5}
              />
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-center items-center gap-2 mt-4">
        {testimonials.map((_, index) => (
          <div
            key={index}
            className={` rounded-full  ${
              currentIndex === index
                ? "bg-[#31269E] h-2 w-2"
                : "h-1 w-1 bg-[#B4B1FF]"
            }`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default TestimonialCarousel;
