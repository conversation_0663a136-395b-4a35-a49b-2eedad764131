import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export const useURLState = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Parse URL parameters
  const parseURLParams = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
        
    const filters = {};
    const sorting = {};
    const search = {};
    const pagination = {};

    // Parse filters (arrays)
    const filterKeys = ['specialization', 'jobType', 'jobStatus', 'status', 'priority', 'visibility', 'location', 'recruiterName', 'jobTitle'];
    filterKeys.forEach(key => {
      const value = searchParams.get(key);
      if (value) {
        filters[key] = value.split(',').filter(item => item.trim());
      } else {
        filters[key] = [];
      }
    });

    // Parse sorting
    sorting.sortBy = searchParams.get('sortBy') || '';
    sorting.sortOrder = searchParams.get('sortOrder') || '';
    sorting.postedDate = searchParams.get('postedDate') || '';
    sorting.submissionDate = searchParams.get('submissionDate') || ''; // ✅ ADDED

    // Parse search
    search.searchTerm = searchParams.get('search') || '';
    search.searchField = searchParams.get('searchField') || 'all';

    // Parse pagination
    pagination.page = parseInt(searchParams.get('page')) || 1;
    pagination.limit = parseInt(searchParams.get('limit')) || 10;

    // Parse active tab
    const activeTab = parseInt(searchParams.get('tabs')) || 0;

    // Parse panel states - ADD THIS
    const showFilters = searchParams.get('showFilters') === 'true';
    const showSorting = searchParams.get('showSorting') === 'true';

    return { 
      filters, 
      sorting, 
      search, 
      pagination, 
      activeTab, 
      showFilters, 
      showSorting 
    };
  }, [location.search]);

  // Update URL with new state
  const updateURLParams = useCallback((newState) => {
    const searchParams = new URLSearchParams();
        
    // Add tab
    if (newState.activeTab !== undefined) {
      searchParams.set('tabs', newState.activeTab.toString());
    }

    // Add filters
    if (newState.filters) {
      Object.entries(newState.filters).forEach(([key, value]) => {
        if (Array.isArray(value) && value.length > 0) {
          searchParams.set(key, value.join(','));
        }
      });
    }

    // Add sorting
    if (newState.sorting) {
      if (newState.sorting.sortBy) {
        searchParams.set('sortBy', newState.sorting.sortBy);
      }
      if (newState.sorting.sortOrder) {
        searchParams.set('sortOrder', newState.sorting.sortOrder);
      }
      if (newState.sorting.postedDate) {
        searchParams.set('postedDate', newState.sorting.postedDate);
      }
      if (newState.sorting.submissionDate) { // ✅ ADDED
        searchParams.set('submissionDate', newState.sorting.submissionDate);
      }
    }

    // Add search
    if (newState.search) {
      if (newState.search.searchTerm) {
        searchParams.set('search', newState.search.searchTerm);
      }
      if (newState.search.searchField && newState.search.searchField !== 'all') {
        searchParams.set('searchField', newState.search.searchField);
      }
    }

    // Add pagination
    if (newState.pagination) {
      if (newState.pagination.page && newState.pagination.page !== 1) {
        searchParams.set('page', newState.pagination.page.toString());
      }
      if (newState.pagination.limit && newState.pagination.limit !== 10) {
        searchParams.set('limit', newState.pagination.limit.toString());
      }
    }

    // Add panel states - ADD THIS
    if (newState.showFilters !== undefined) {
      if (newState.showFilters) {
        searchParams.set('showFilters', 'true');
      }
      // Don't add false values to keep URL clean
    }

    if (newState.showSorting !== undefined) {
      if (newState.showSorting) {
        searchParams.set('showSorting', 'true');
      }
      // Don't add false values to keep URL clean
    }

    // Update URL without page reload
    const newURL = `${location.pathname}?${searchParams.toString()}`;
    navigate(newURL, { replace: true });
  }, [location.pathname, navigate]);

  return { parseURLParams, updateURLParams };
};