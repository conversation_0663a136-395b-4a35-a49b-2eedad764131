import React from "react";

const StatsCard = ({
  image = "",
  lable = "",
  value = "",
  width = "w-64",
  onClick,
  imageBackground = "bg-[#FEEFC7]",
}) => {
  return (
    <>
      <div
        onClick={onClick}
        className={`bg-white rounded-2xl shadow-sm p-4 cursor-pointer  border border-white  hover:border-[#3E9900]  ${width}`}
      >
        <div className="flex items-start gap-3">
          <div className={`${imageBackground} p-2 rounded-full`}>
            <img
              alt={lable}
              className="w-5 h-5 object-contain block flex-none"
              src={image}
            />
          </div>
        </div>

        <div className="mt-2">
          <p className="text-gray-500 text-sm font-medium">{lable}</p>
          <p className="text-gray-900 text-2xl font-bold">{value}</p>
        </div>
      </div>
    </>
  );
};

export default StatsCard;
