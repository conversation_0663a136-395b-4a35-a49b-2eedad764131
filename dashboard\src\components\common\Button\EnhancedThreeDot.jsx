import React, { useEffect, useRef, useState, useMemo } from "react";

const EnhancedThreeDot = ({
  buttonDropDown = "",
  dropdownSize = "w-56",
  iconSrc = null,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);

    // Enhanced: Listen for custom event to close the dropdown from inside
    function handleCustomClose() {
      setIsOpen(false);
    }
    window.addEventListener("threedot-close", handleCustomClose);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("threedot-close", handleCustomClose);
    };
  }, []);

  // Enhanced: Handle both function and JSX patterns for buttonDropDown (with performance optimization)
  const dropdownContent = useMemo(() => {
    // Handle function pattern (enhanced functionality)
    if (typeof buttonDropDown === "function") {
      return buttonDropDown(() => setIsOpen(false));
    }

    // Handle all other cases: JSX elements, React Fragments, strings, null, undefined
    return buttonDropDown;
  }, [buttonDropDown]);

  return (
    <>
      {/* Enhanced: Added relative positioning */}
      <div ref={dropdownRef} className="relative">
        <div className="flex items-center gap-2">
          <img
            aria-expanded={isOpen}
            onClick={() => setIsOpen((prev) => !prev)}
            src={"/assets/icons/threedot.svg"}
            className="w-7 h-5 cursor-pointer object-contain block flex-none"
          />
        </div>

        {isOpen && (
          <div
            className={`absolute right-0 z-50 my-1 me-3 ${dropdownSize} text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-md`}
            style={{ minWidth: "150px" }}
          >
            {dropdownContent}
          </div>
        )}
      </div>
    </>
  );
};

export default EnhancedThreeDot;
