const { default: mongoose } = require("mongoose");

const RefreshTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: "7d", 
  },
});


const RefreshToken = mongoose.model("RefreshToken", RefreshTokenSchema);
module.exports = {RefreshToken}
