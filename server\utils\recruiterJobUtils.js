/**
 * Utility functions for recruiter job operations
 * Similar to jobQueryUtils.js but specific to recruiter job requirements
 */

/**
 * Build sort conditions for recruiter jobs
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order (asc/desc)
 * @param {string} postedDate - Date sorting preference (recent/oldest)
 * @returns {object} - MongoDB sort conditions
 */
const buildRecruiterSortConditions = (sortBy, sortOrder, postedDate) => {
  // Default to latest first
  const defaultSort = { createdAt: -1 };

  // If no parameters provided, return default sort
  if (!postedDate && !sortBy && !sortOrder) {
    return defaultSort;
  }

  // Handle sortBy first (highest priority)
  if (sortBy) {
    const sortField = sortBy.toLowerCase();
    // For date-based sorting, use explicit -1/1
    if (sortField === 'newest' || sortField === 'recent' || sortField === 'latest') {
      return { createdAt: -1 };
    }
    if (sortField === 'oldest') {
      return { createdAt: 1 };
    }
    // For other fields, use the provided sort order
    const order = sortOrder && sortOrder.toLowerCase() === 'asc' ? 1 : -1;

    // Direct date sorting
    if (sortField === 'newest' || sortField === 'recent' || sortField === 'latest') {
      return { createdAt: -1 };
    }
    if (sortField === 'oldest') {
      return { createdAt: 1 };
    }
  }

  // Fall back to postedDate if sortBy not specified
  if (postedDate) {
    const dateSort = postedDate.toLowerCase();
    if (['recent', 'newest', 'latest'].includes(dateSort)) {
      return { createdAt: -1 };
    } else if (['oldest', 'earliest'].includes(dateSort)) {
      return { createdAt: 1 };
    }
  }

  if (!sortBy) return defaultSort;

  const sortField = sortBy.toLowerCase();
  const order = sortOrder && sortOrder.toLowerCase() === 'asc' ? 1 : -1;

  const sortMappings = {
    'posteddate': { createdAt: order },
    'posted_date': { createdAt: order },
    'createdat': { createdAt: order },
    'created_at': { createdAt: order },
    'recent': { createdAt: -1 },
    'newest': { createdAt: -1 },
    'latest': { createdAt: -1 },
    'oldest': { createdAt: 1 },
    'jobtitle': { jobTitle: order },
    'job_title': { jobTitle: order },
    'jobtype': { jobType: order },
    'job_type': { jobType: order },
    'experience': { 'experience.min': order },
    'industry': { industry: order },
    'specialization': { industry: order },
    'priority': { priority: order },
    'updatedat': { updatedAt: order },
    'updated_at': { updatedAt: order },
    'location': { 'location.city': order }
  };

  return sortMappings[sortField] || defaultSort;
};

/**
 * Build filter conditions for recruiter jobs
 * @param {object} query - Request query parameters
 * @returns {object} - MongoDB filter conditions
 */
const buildRecruiterFilterConditions = (query) => {
  const filterConditions = {};
  
  // Specialization filter (maps to industry field)
  if (query.specialization) {
    const specializations = query.specialization.split(',').map(s => s.trim()).filter(s => s);
    if (specializations.length > 0) {
      filterConditions.industry = {
        $in: specializations.map(spec => new RegExp(spec.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // Location filter
  if (query.location) {
    const locations = query.location.split(',').map(l => l.trim()).filter(l => l);
    if (locations.length > 0) {
      filterConditions["location.country"] = {
        $in: locations.map(loc => new RegExp(loc.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i'))
      };
    }
  }

  // Job Type filter
  if (query.jobType) {
    const jobTypes = query.jobType.split(',').map(jt => jt.trim()).filter(jt => jt);
    if (jobTypes.length > 0) {
      filterConditions.jobType = {
        $in: jobTypes.map(type => new RegExp(`^${type.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i'))
      };
    }
  }

  // Experience Level filter
  if (query.experienceLevel) {
    const expLevels = query.experienceLevel.split(',').map(el => el.trim()).filter(el => el);
    if (expLevels.length > 0) {
      // Handle range format like "3-7"
      const expConditions = expLevels.map(range => {
        if (range.includes('-')) {
          const [min, max] = range.split('-').map(Number);
          return {
            $and: [
              { "experience.min": { $lte: max } },
              { "experience.max": { $gte: min } }
            ]
          };
        }
        return {};
      }).filter(cond => Object.keys(cond).length > 0);
      
      if (expConditions.length > 0) {
        filterConditions.$or = expConditions;
      }
    }
  }

  // Search functionality
  if (query.search && query.search.trim()) {
    const searchTerm = query.search.trim();
    const searchField = query.searchField || 'all';

    const searchConditions = [];

    if (searchField === 'all' || searchField === 'jobTitle') {
      searchConditions.push({ jobTitle: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'jobId') {
      searchConditions.push({ jobId: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'location') {
      searchConditions.push({ "location.country": new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'jobType') {
      searchConditions.push({ jobType: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'industry') {
      searchConditions.push({ industry: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }
    if (searchField === 'all' || searchField === 'priority') {
      searchConditions.push({ priority: new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i') });
    }

    if (searchConditions.length > 0) {
      // If there's already an $or condition (from experience level), combine them properly
      if (filterConditions.$or) {
        // Combine existing $or with search $or using $and
        const existingOr = filterConditions.$or;
        delete filterConditions.$or;
        filterConditions.$and = [
          { $or: existingOr },
          { $or: searchConditions }
        ];
      } else {
        filterConditions.$or = searchConditions;
      }
    }
  }

  return filterConditions;
};

/**
 * Build response with applied filters and sorting
 * @param {object} query - Request query parameters
 * @param {object} sortConditions - Applied sort conditions
 * @returns {object} - Response metadata
 */
const buildRecruiterResponseMetadata = (query, sortConditions) => {
  return {
    appliedFilters: {
      specialization: query.specialization ? query.specialization.split(',').map(s => s.trim()).filter(s => s) : [],
      location: query.location ? query.location.split(',').map(l => l.trim()).filter(l => l) : [],
      jobType: query.jobType ? query.jobType.split(',').map(jt => jt.trim()).filter(jt => jt) : [],
      experienceLevel: query.experienceLevel ? query.experienceLevel.split(',').map(el => el.trim()).filter(el => el) : [],
      search: query.search || null,
      searchField: query.searchField || null,
    },
    appliedSorting: {
      postedDate: query.postedDate || null,
      sortBy: query.sortBy || null,
      sortOrder: query.sortOrder || null,
      sortObject: sortConditions
    }
  };
};

module.exports = {
  buildRecruiterSortConditions,
  buildRecruiterFilterConditions,
  buildRecruiterResponseMetadata
};
