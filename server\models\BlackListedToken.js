const mongoose = require("mongoose");

const BlacklistedTokenSchema = new mongoose.Schema({
  token: {
    type: String,
    required: true,
    unique: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  reason: {
    type: String,
    enum: ["logout", "forced_logout", "security_breach"],
    default: "logout",
  },
  
}, {
    timestamps: true,
  });

module.exports = mongoose.model("BlacklistedToken", BlacklistedTokenSchema);
