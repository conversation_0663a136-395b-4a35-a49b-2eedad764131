import React, { useEffect, useState } from "react";
import <PERSON>bNav from "../../components/common/TabNav/TabNav";
import DropDownButton from "../../components/common/Button/DropDownButton";
import { useLocation, useNavigate } from "react-router-dom";
import { handleDownload } from "../../utils/genratexlsxTemplate";
import AppButton from "../../components/common/Button/AppButton";
import BulkUpload from "../../components/core/HeadAccountManager/Job/BulkUpload";
import AccountManagerAssignment from "../../components/core/HeadAccountManager/Job/AccountManagerAssignment";

import {
  gethighperiorityJobs,
  getActiveJobs,
  getAllJobs,
  getUnEngagedJobs,
  getCloseJobs,
  updateJobStatus,
  getAllUnassignedJobs,
} from "../../services/operations/jobAPI";

import UnAssignedJobs from "../../components/common/Job/ManagerJobTables/UnAssignedJobs";
import HighPriorityJobs from "../../components/common/Job/ManagerJobTables/HighPriorityJobs";
import ActiveJobs from "../../components/common/Job/ManagerJobTables/ActiveJobs";
import UnEngagedJobs from "../../components/common/Job/ManagerJobTables/UnEngagedJobs";
import AllJobs from "../../components/common/Job/ManagerJobTables/AllJobs";
import CloseJobs from "../../components/common/Job/ManagerJobTables/CloseJobs";
import FiltersPanel from "../../components/common/filter/FiltersPanel";
import SortingPanel from "../../components/common/filter/SortingPanel";
import ViewOptionsDropdown from "../../components/common/filter/ViewOptionsDropdown";
import SearchDropdown from "../../components/common/filter/SearchDropdown";
import { useURLState } from "../../components/common/filter/useURLState";
import { useRef } from "react";
import { toast } from "react-toastify";

const JobsPage = () => {
  const { search, pathname } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentTabs = queryParams.get("tabs");
  const navigate = useNavigate();
  const { parseURLParams, updateURLParams } = useURLState();
  const [isInitialized, setIsInitialized] = useState(false);
  const initialURLState = parseURLParams();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  // Filter and Sorting States
  const [showFilters, setShowFilters] = useState(
    initialURLState.showFilters || false
  );
  const [showSorting, setShowSorting] = useState(
    initialURLState.showSorting || false
  );

  const [searchData, setSearchData] = useState(initialURLState.search);

  // Track if we're initializing from URL to prevent loops
  const isInitializing = useRef(false);
  const hasInitialized = useRef(false);

  const [active, setActive] = useState(initialURLState.activeTab);
  // Define available filters for each tab
  const tabFilterConfig = {
    0: {
      // Un-assigned Jobs
      filters: ["specialization", "jobType"],
      sortOptions: ["postedDate"],
    },
    1: {
      // High Priority Jobs
      filters: ["location", "jobType", "status"],
      sortOptions: ["postedDate"],
    },
    2: {
      // Active Jobs
      filters: ["location", "jobType", "visibility"],
      sortOptions: ["postedDate"],
    },
    3: {
      // Un-engaged Jobs
      filters: ["location", "jobType"],
      sortOptions: ["postedDate"],
    },
    4: {
      // All Jobs
      filters: ["location", "visibility", "status"],
      sortOptions: ["postedDate"],
    },
    5: {
      // Closed Jobs
      filters: ["location", "jobType", "status"],
      sortOptions: ["postedDate"],
    },
  };

  const currentConfig = tabFilterConfig[active] || tabFilterConfig[0];
  const availableFilters = currentConfig.filters;
  const availableSortOptions = currentConfig.sortOptions;

  // Tab-specific filter states
  const [filters, setFilters] = useState(() => {
    const initialFilters = {
      0: { jobType: [], specialization: [] },
      1: { location: [], jobType: [], status: [] },
      2: { location: [], jobType: [], visibility: [] },
      3: { location: [], jobType: [] },
      4: { location: [], visibility: [], status: [] },
      5: { location: [], jobType: [], status: [] },
    };

    // Override with URL data for the active tab
    if (
      initialURLState.filters &&
      Object.keys(initialURLState.filters).some(
        (key) => initialURLState.filters[key].length > 0
      )
    ) {
      initialFilters[initialURLState.activeTab] = {
        ...initialFilters[initialURLState.activeTab],
        ...initialURLState.filters,
      };
    }

    return initialFilters;
  });

  // Tab-specific sorting states
  const [sorting, setSorting] = useState(() => {
    const initialSorting = { 0: "", 1: "", 2: "", 3: "", 4: "", 5: "" };

    // Convert URL sorting to UI format
    if (initialURLState.sorting.postedDate) {
      initialSorting[initialURLState.activeTab] =
        initialURLState.sorting.postedDate === "recent" ? "Recent" : "Oldest";
    } else if (initialURLState.sorting.sortBy) {
      initialSorting[
        initialURLState.activeTab
      ] = `${initialURLState.sorting.sortBy}_${initialURLState.sorting.sortOrder}`;
    }

    return initialSorting;
  });
  // Un-assigned jobs states
  const [selectedJobs, setSelectedJobs] = useState({});
  const [assignManagerModel, setAssignManagerModel] = useState(false);
  // Add this state variable with your other states
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);

  // Add these handler functions
  const handleSearchClick = () => {
    setShowSearchDropdown(!showSearchDropdown);
  };

  const handleSearchClose = () => {
    setShowSearchDropdown(false);
  };

  const handleSearch = (searchInputs) => {
    setSearchData(searchInputs);

    // Trigger immediate search (or you can wait for Apply button)
    // This depends on whether you want instant search or apply-based search
  };

  const tabs = [
    { name: <span>Un-assigned Job</span>, css: "" },
    { name: <span>High Priority Job</span>, css: "" },
    { name: <span>Active Job</span>, css: "" },
    { name: <span>Un-engaged Job</span>, css: "" },
    { name: <span>All Job</span>, css: "" },
    { name: <span>Closed Job</span>, css: "" },
  ];

  useEffect(() => {
    const urlState = parseURLParams();

    // Set active tab
    setActive(urlState.activeTab);

    // Set filters for current tab
    setFilters((prev) => ({
      ...prev,
      [urlState.activeTab]: urlState.filters,
    }));

    // Set sorting for current tab
    const sortingValue =
      urlState.sorting.postedDate ||
      (urlState.sorting.sortBy
        ? `${urlState.sorting.sortBy}_${urlState.sorting.sortOrder}`
        : "") ||
      "";
    setSorting((prev) => ({
      ...prev,
      [urlState.activeTab]: sortingValue,
    }));

    // Set search
    setSearchData(urlState.search);

    setIsInitialized(true);
  }, []);

  // Update URL when state changes (but not during initialization)
  useEffect(() => {
    if (isInitializing.current || !hasInitialized.current) {
      hasInitialized.current = true;
      return;
    }

    // Convert sorting back to URL format
    const currentSorting = sorting[active];
    let sortingForURL = {};

    if (currentSorting) {
      if (currentSorting === "Recent") {
        sortingForURL.postedDate = "recent";
      } else if (currentSorting === "Oldest") {
        sortingForURL.postedDate = "oldest";
      } else if (currentSorting.includes("_")) {
        const [sortBy, sortOrder] = currentSorting.split("_");
        sortingForURL.sortBy = sortBy;
        sortingForURL.sortOrder = sortOrder;
      }
    }

    updateURLParams({
      activeTab: active,
      filters: filters[active],
      sorting: sortingForURL,
      search: searchData.searchTerm ? searchData : {},
      showFilters: showFilters,
      showSorting: showSorting,
    });
  }, [
    active,
    filters,
    sorting,
    searchData,
    isInitialized,
    showFilters,
    showSorting,
    updateURLParams,
  ]);

  useEffect(() => {
    navigate(`${pathname}?${currentTabs ? `tabs=${currentTabs}` : "tabs=0"}`, {
      replace: true,
    });
  }, []);

  useEffect(() => {
    if (currentTabs) {
      setActive(currentTabs);
    } else {
      setActive(0);
    }
  }, [currentTabs]);

  // Filter and Sorting Handler Functions
  const handleFilterChange = (filterType, value) => {
    setFilters((prev) => ({
      ...prev,
      [active]: {
        ...prev[active],
        [filterType]: value,
      },
    }));
  };

  const handleApplyFilters = () => {
    // Apply filters and sorting logic here
    setShowDropdown(false);
    setShowSearchDropdown(false);

    // Here you would typically refetch data with filters
    // You can modify your existing API calls to accept filter parameters
    setFilters((prev) => ({ ...prev })); // Force re-render to trigger useEffect
  };

  const handleSortingChange = (sortValue) => {
    setSorting((prev) => ({
      ...prev,
      [active]: sortValue,
    }));
  };

  function updateTabs(tabs) {
    setSelectedJobs([]);
    navigate(`${pathname}?${`tabs=${tabs}`}`, { replace: true });
    setActive(tabs);
    // Reset pagination when changing tabs
  }

  // API calls (keeping the same as original)
  async function getHighPriorityJob(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      // Build filters object for API
      const filters = {};

      // Handle locations
      if (appliedFilters.location && appliedFilters.location.length > 0) {
        filters.locations = appliedFilters.location.join(",");
      }

      // Handle job types
      if (appliedFilters.jobType && appliedFilters.jobType.length > 0) {
        filters.jobTypes = appliedFilters.jobType
          .map((type) => type.toLowerCase())
          .join(",");
      }

      // Handle status
      if (appliedFilters.status && appliedFilters.status.length > 0) {
        filters.status = appliedFilters.status.join(",");
      }

      // Handle sorting
      if (appliedSorting) {
        switch (appliedSorting) {
          case "Recent":
            filters.sortOrder = "recent";
            break;
          case "Oldest":
            filters.sortOrder = "oldest";
            break;
          default:
            if (appliedSorting.includes("_")) {
              const [sortBy, sortOrder] = appliedSorting.split("_");
              filters.sortBy = sortBy;
              filters.sortOrder = sortOrder;
            }
        }
      }

      // Handle search
      if (appliedSearch.searchTerm && appliedSearch.searchTerm.trim()) {
        filters.search = appliedSearch.searchTerm.trim();
        if (appliedSearch.searchField && appliedSearch.searchField !== "all") {
          filters.searchField = appliedSearch.searchField;
        }
      }

      // Call the API with filters
      const response = await gethighperiorityJobs(page, limit, filters);

      if (!response?.success) {
        throw new Error(
          response?.message || "Failed to fetch high priority jobs"
        );
      }

      // Transform the data for the frontend
      const jobs =
        response?.results?.map((item) => ({
          jobid: item?.jobId,
          jobtitle: item?.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item?.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          submissionsCount: item?.submissionsCount || 0,
          recruiterCount: item?.recruiterCount || 0,
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.error("[getHighPriorityJob] Error:", error);
      return {
        results: [],
        total: 0,
        totalPages: 1,
        page: 1,
      };
    }
  }

  async function getActiveJob(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      let sortingParams = {};

      if (appliedSorting) {
        // Handle different sorting options
        switch (appliedSorting) {
          case "Recent":
            sortingParams = { postedDate: "recent" };
            break;
          case "Oldest":
            sortingParams = { postedDate: "oldest" };
            break;
          default:
            sortingParams = {};
        }
      }
      const response = await getActiveJobs(
        page,
        limit,
        appliedFilters,
        sortingParams,
        appliedSearch
      );
      const jobs =
        response?.results?.map((item) => ({
          jobid: item?.jobId,
          visibility: item?.visibility || false,
          jobtitle: item.jobTitle,
          location:
            item?.location?.country?.split("-")[0] +
            ", " +
            item?.location?.state,
          jobType: item?.jobType,
          submissionsCount: item?.submissionsCount,
          recruiterCount: item?.recruiterCount,
        })) || [];

      return {
        results: jobs || [],
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.log(error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getUnEngagedJob(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      let sortingParams = {};

      if (appliedSorting) {
        switch (appliedSorting) {
          case "Recent":
            sortingParams = { postedDate: "recent" };
            break;
          case "Oldest":
            sortingParams = { postedDate: "oldest" };
            break;
          case "A to Z":
            sortingParams = { sortBy: "jobTitle", sortOrder: "asc" };
            break;
          case "Z to A":
            sortingParams = { sortBy: "jobTitle", sortOrder: "desc" };
            break;
          default:
            if (appliedSorting.includes("_")) {
              const [sortBy, sortOrder] = appliedSorting.split("_");
              sortingParams = { sortBy, sortOrder };
            }
        }
      }
      const response = await getUnEngagedJobs(
        page,
        limit,
        appliedFilters,
        sortingParams,
        appliedSearch
      );
      const jobs =
        response?.results?.map((item) => ({
          jobid: item?.jobId,
          jobtitle: item.jobTitle,
          location:
            item?.location?.country?.split("-")[0] +
            ", " +
            item?.location?.state,
          jobType: item?.jobType,
          workOnRequestCount: item?.workOnRequestCount,
        })) || [];

      return {
        results: jobs || [],
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.log(error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getUnAssignedJobs(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      let sortingParams = {};

      if (appliedSorting) {
        // Handle different sorting options
        switch (appliedSorting) {
          case "Recent":
            sortingParams = { postedDate: "recent" };
            break;
          case "Oldest":
            sortingParams = { postedDate: "oldest" };
            break;
          default:
            sortingParams = {};
        }
      }
      const response = await getAllUnassignedJobs(
        page,
        limit,
        appliedFilters,
        sortingParams,
        appliedSearch
      );

      const jobs =
        response?.results?.map((item) => ({
          jobId: item?.jobId,
          jobTitle: item.jobTitle,
          jobType: item?.jobType,
          specialization: item?.industry,
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.log(error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getAllJob(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      // Build filters object for API
      const filters = {};

      // Handle locations
      if (appliedFilters.location && appliedFilters.location.length > 0) {
        filters.locations = appliedFilters.location.join(",");
      }

      // Handle visibility (specific to All Jobs)
      if (appliedFilters.visibility && appliedFilters.visibility.length > 0) {
        filters.visibility = appliedFilters.visibility
          .map((v) => v.toLowerCase())
          .join(",");
      }

      // Handle status (specific to All Jobs) - map frontend 'status' to API 'jobStatus' for All Jobs tab
      if (appliedFilters.status && appliedFilters.status.length > 0) {
        filters.jobStatus = appliedFilters.status.join(","); // Use jobStatus for All Jobs
      }

      // Handle sorting
      if (appliedSorting) {
        switch (appliedSorting) {
          case "Recent":
            filters.sortOrder = "recent";
            break;
          case "Oldest":
            filters.sortOrder = "oldest";
            break;
          default:
            if (appliedSorting.includes("_")) {
              const [sortBy, sortOrder] = appliedSorting.split("_");
              filters.sortBy = sortBy;
              filters.sortOrder = sortOrder;
            }
        }
      }

      // Handle search
      if (appliedSearch.searchTerm && appliedSearch.searchTerm.trim()) {
        filters.search = appliedSearch.searchTerm.trim();
        if (appliedSearch.searchField && appliedSearch.searchField !== "all") {
          filters.searchField = appliedSearch.searchField;
        }
      }

      // Call the API with filters
      const response = await getAllJobs(page, limit, filters);

      if (!response?.success) {
        throw new Error(response?.message || "Failed to fetch all jobs");
      }

      // Transform the data for the frontend
      const jobs =
        response?.results?.map((item) => ({
          _id: item?.jobId, // Keep both for compatibility
          visibility: item?.visibility,
          jobtitle: item?.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item?.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          status: item?.jobStatus,
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.error("[getAllJob] Error:", error);
      return {
        results: [],
        total: 0,
        totalPages: 1,
        page: 1,
      };
    }
  }

  async function getCloseJob(
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) {
    try {
      // Build filters object for API
      const filters = {};

      // Handle locations
      if (appliedFilters.location && appliedFilters.location.length > 0) {
        filters.locations = appliedFilters.location.join(",");
      }

      // Handle job types
      if (appliedFilters.jobType && appliedFilters.jobType.length > 0) {
        filters.jobTypes = appliedFilters.jobType
          .map((type) => type.toLowerCase())
          .join(",");
      }

      // Handle status (specific to Closed Jobs) - map frontend 'status' to API 'jobStatus' for Closed Jobs tab
      if (appliedFilters.status && appliedFilters.status.length > 0) {
        filters.jobStatus = appliedFilters.status.join(","); // Use jobStatus for Closed Jobs
      }

      // Handle sorting
      if (appliedSorting) {
        switch (appliedSorting) {
          case "Recent":
            filters.sortOrder = "recent";
            break;
          case "Oldest":
            filters.sortOrder = "oldest";
            break;
          default:
            if (appliedSorting.includes("_")) {
              const [sortBy, sortOrder] = appliedSorting.split("_");
              filters.sortBy = sortBy;
              filters.sortOrder = sortOrder;
            }
        }
      }

      // Handle search
      if (appliedSearch.searchTerm && appliedSearch.searchTerm.trim()) {
        filters.search = appliedSearch.searchTerm.trim();
        if (appliedSearch.searchField && appliedSearch.searchField !== "all") {
          filters.searchField = appliedSearch.searchField;
        }
      }

      // Call the API with filters
      const response = await getCloseJobs(page, limit, filters);

      if (!response?.success) {
        throw new Error(response?.message || "Failed to fetch closed jobs");
      }

      // Transform the data for the frontend
      const jobs =
        response?.results?.map((item) => ({
          jobid: item?.jobId,
          jobtitle: item?.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item?.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          submissionsCount: item?.submissionsCount || 0,
          recruiterCount: item?.recruiterCount || 0,
          status: item?.jobStatus || item?.status, // Map jobStatus from API to status for frontend
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.error("[getCloseJob] Error:", error);
      return {
        results: [],
        total: 0,
        totalPages: 1,
        page: 1,
      };
    }
  }

  async function updateStatus(jobID, status) {
    try {
      await updateJobStatus({ jobID, status });
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <>
      <TabNav
        nav={tabs}
        active={active}
        setActive={updateTabs}
        rightSidebar={
          <div className="flex gap-2 items-center ">
            <div className="relative">
              <img
                src={"/assets/icons/search.svg"}
                alt="alt"
                className="w-6 h-8 cursor-pointer"
                onClick={handleSearchClick}
              />

              <SearchDropdown
                isOpen={showSearchDropdown}
                onClose={handleSearchClose}
                onSearch={handleSearch}
                activeTab={active}
              />
            </div>
            {/* View Options Dropdown */}
            <ViewOptionsDropdown
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              setShowFilters={setShowFilters}
              setShowSorting={setShowSorting}
              showFilters={showFilters}
              showSorting={showSorting}
            />

            {/* Add Job Dropdown */}
            <DropDownButton
              button={
                <>
                  <span className="text-sm font-medium">+ Add Job</span>
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </>
              }
              dropdownSize="w-64"
              buttonDropDown={
                <ul className="py-0" role="none">
                  <li>
                    <div
                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={() => {
                        window.location.href =
                          "https://hirring.s3.us-east-1.amazonaws.com/Templates/fulltime_job_template.xlsx";
                      }}
                    >
                      <img
                        src={"/assets/icons/download.svg"}
                        alt="Download Full-time Template"
                      />
                      Download Full-time Template
                    </div>
                  </li>
                  <li>
                    <div
                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={() => {
                        handleDownload(
                          [
                            "externalJobID",
                            "jobTitle",
                            "industry",
                            "commissionAmount",
                            "commissionCurrency",
                            "country",
                            "state",
                            "city",
                            "zipCode",
                            "minexperience",
                            "maxexperience",
                            "experienceunit(year/month)",
                            "jobStatus",
                            "jobType",
                            "remote",
                            "requiredHoursPerWeek",
                            "jobpriority",
                            "jobopenings",
                            "minSalary",
                            "maxSalary",
                            "salaryCurrency",
                            "primarySkills",
                            "benefits",
                            "jobDescription",
                            "client",
                            "guaranteePeriod",
                            "jobProfile",
                          ],
                          "Contract_job_Template"
                        );
                      }}
                    >
                      <img
                        src={"/assets/icons/download.svg"}
                        alt="Download Contract Template"
                      />
                      Download Contract Template
                    </div>
                  </li>
                  <li>
                    <div
                      onClick={(e) => {
                        e.preventDefault();
                        setIsModalOpen(true);
                      }}
                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                    >
                      <img src={"/assets/icons/upload.svg"} alt="Bulk Upload" />
                      Bulk Upload
                    </div>
                  </li>
                  <li>
                    <div
                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={(e) => {
                        e.preventDefault();
                        navigate("/jobs/add-job");
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 18 18"
                        fill="none"
                      >
                        <path
                          d="M13.5078 9.75391H9.75781V13.5039C9.75781 13.9164 9.42031 14.2539 9.00781 14.2539C8.59531 14.2539 8.25781 13.9164 8.25781 13.5039V9.75391H4.50781C4.09531 9.75391 3.75781 9.41641 3.75781 9.00391C3.75781 8.59141 4.09531 8.25391 4.50781 8.25391H8.25781V4.50391C8.25781 4.09141 8.59531 3.75391 9.00781 3.75391C9.42031 3.75391 9.75781 4.09141 9.75781 4.50391V8.25391H13.5078C13.9203 8.25391 14.2578 8.59141 14.2578 9.00391C14.2578 9.41641 13.9203 9.75391 13.5078 9.75391Z"
                          fill="#27364B"
                        />
                      </svg>
                      <div>Add Job</div>
                    </div>
                  </li>
                </ul>
              }
            />

            {/* Assign Manager Button - Only show for Un-assigned Jobs tab */}
            {active == 0 && (
              <AppButton
                type="submit"
                label={"Assign Manager"}
                disabled={
                  Object.values(selectedJobs)?.filter((item) => item).length ===
                  0
                }
                css={`flex items-center ${
                  Object.values(selectedJobs)?.filter((item) => item).length > 0
                    ? "cursor-pointer bg-green-600"
                    : "cursor-not-allowed bg-[#94A3B8]"
                } justify-center gap-2 px-2 py-2 text-white rounded-md`}
                onClick={(e) => {
                  e.preventDefault();
                  if (
                    Object.values(selectedJobs)?.filter((item) => item).length >
                    0
                  ) {
                    setAssignManagerModel(true);
                  }
                }}
              />
            )}
          </div>
        }
      />

      {/* Filter and Sorting Panels */}
      <FiltersPanel
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        filters={filters[active]}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        availableFilters={availableFilters}
        pageType="jobs"
      />

      <SortingPanel
        showSorting={showSorting}
        setShowSorting={setShowSorting}
        sorting={sorting[active]}
        setSorting={handleSortingChange}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableSortOptions={availableSortOptions}
      />

      {/* Modals */}
      <BulkUpload
        getAllJob={() => {
          setActive(6);
          setTimeout(() => {
            navigate(`${pathname}?${`tabs=${0}`}`, { replace: true });
            setActive(0);
          }, 100);
        }}
        setIsModalOpen={setIsModalOpen}
        isModalOpen={isModalOpen}
      />

      <AccountManagerAssignment
        setIsModalOpen={setAssignManagerModel}
        isModalOpen={assignManagerModel}
        selectedJobs={selectedJobs}
        getAllJob={() => {
          toast.success("Selected jobs assigned successfully.");
          setActive(6);
          setTimeout(() => {
            navigate(`${pathname}?${`tabs=${0}`}`, { replace: true });
            setActive(0);
          }, 100);
        }}
        setSelectedJobs={setSelectedJobs}
      />

      {/* Job Tables */}
      <div className="overflow-x-auto rounded-lg mt-2 border border-gray-200 shadow-sm">
        {active == 0 && (
          <UnAssignedJobs
            selectedJobs={selectedJobs}
            setSelectedJobs={setSelectedJobs}
            getJob={getUnAssignedJobs}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
        {active == 1 && (
          <HighPriorityJobs
            getJob={getHighPriorityJob}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
        {active == 2 && (
          <ActiveJobs
            selectedJobs={selectedJobs}
            setSelectedJobs={setSelectedJobs}
            getJob={getActiveJob}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
        {active == 3 && (
          <UnEngagedJobs
            getJob={getUnEngagedJob}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
        {active == 4 && (
          <AllJobs
            getJob={getAllJob}
            updateStatus={updateStatus}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
        {active == 5 && (
          <CloseJobs
            getJob={getCloseJob}
            updateStatus={updateStatus}
            filters={filters[active]}
            sorting={sorting[active]}
            search={searchData}
          />
        )}
      </div>
    </>
  );
};

export default JobsPage;
