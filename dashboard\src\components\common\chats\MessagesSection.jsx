import { useEffect, useRef, useState } from "react";
import { getChatDetails } from "../../../services/operations/chatAPI";
import { useSelector } from "react-redux";
import Message from "./Message";

const MessagesSection = ({
  jobId,
  getMessages,
  setGetMessages,
  selectedRecruiter,
  newChatId,
  minHeight = "20vh",
}) => {
  const [messages, setMessages] = useState([]);
  const [chatId, setChatId] = useState("");
  const scrollRef = useRef(null);

  const userType = useSelector((state) => state.auth?.user?.role);

  const jobtest = useSelector((state) =>
    state.auth?.user?.profile?.jobsWorkingOn?.find((job) => job.jobId === jobId)
  );

  //* Used to get chatId from the current user's profile - Only for recruiters
  let idChat = useSelector(
    (state) =>
      state.auth?.user?.profile?.jobsWorkingOn?.find(
        (job) => job.jobId === jobId
      )?.chatId || ""
  );

  const fetchMessages = async () => {
    try {
      if (!chatId) {
        return;
      }

      const response = await getChatDetails(chatId);

      if (response?.success) {
        setMessages(response?.data?.messages || []);
        setGetMessages(false);
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
    }
  };

  useEffect(() => {
    if (chatId) {
      fetchMessages();
    }
  }, [chatId]);

  useEffect(() => {
    if (getMessages) {
      fetchMessages();
      setGetMessages(false);
    }
  }, [getMessages]);

  useEffect(() => {
    // Scroll to bottom when messages change
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    const chatId =
      (userType !== "recruiter" &&
        selectedRecruiter?.profile?.jobsWorkingOn?.find(
          (job) => job.jobId === jobId
        )?.chatId) ||
      "";

    setChatId(chatId);
  }, [jobId, selectedRecruiter, userType]);

  useEffect(() => {
    // If newChatId is provided, update chatId
    if (newChatId) {
      setChatId(newChatId);
      return;
    }

    setChatId(idChat);
  }, [idChat, newChatId]);

  return (
    <div
      ref={scrollRef}
      className=" max-h-[70vh] overflow-y-auto"
      style={{ minHeight: minHeight }}
    >
      <div className="flex flex-col justify-center gap-[1.125rem] w-full">
        {messages?.map((msg, index) => (
          <Message
            key={index}
            name={`${msg?.firstName} ${msg?.lastName}`}
            message={msg}
            userType={userType}
          />
        ))}
      </div>
    </div>
  );
};

export default MessagesSection;
