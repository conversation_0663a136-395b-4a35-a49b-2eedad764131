const express = require("express");
const router = express.Router();
const {
  createChat,
  sendMessage,
  getChatDetails,
  getUserChats,
  getJobChats,
  getCandidateChats,
} = require("../controllers/Chat");
const { auth } = require("../middlewares/auth");

router.post("/create-chat", auth, createChat);

router.post("/send-message", auth, sendMessage);

router.get("/messages", auth, getChatDetails);

router.get("/user-chats", auth, getUserChats);

router.get("/job-chats", auth, getJobChats);

router.get("/candidate-chats", auth, getCandidateChats);

module.exports = router;
