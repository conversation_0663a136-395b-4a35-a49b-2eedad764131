import { useEffect, useState } from "react";
import { getRecruiterDetails } from "../../../services/operations/recruiterAPI";
import MessagesSection from "./MessagesSection";
import AccountManager from "../../../pages/HeadAccountManager/AccountManager";
import AccountManagerChatHeader from "./AccountManagerChatHeader";
import InputContainer from "./InputContainer";

const CandidateChatAccountManager = ({ recruiterId, submission }) => {
  const [recruiterDetails, setRecruiterDetails] = useState({});
  const [getMessages, setGetMessages] = useState(false);
  const [chatId, setChatId] = useState("");

  const fetchRecruiterDetails = async () => {
    try {
      const response = await getRecruiterDetails(recruiterId);
      setRecruiterDetails(response?.data[0]);

      console.log("Recruiter Details:", response?.data[0]);
    } catch (error) {
      console.error("Error fetching recruiter details:", error);
    }
  };

  useEffect(() => {
    if (recruiterId) {
      console.log("Fetching recruiter details for ID:", recruiterId);
      fetchRecruiterDetails();
    }
  }, [recruiterId]);

  useEffect(() => {
    if (submission && submission?.chatId) {
      console.log("Setting chat ID from submission:", submission?.chatId);
      setChatId(submission?.chatId);
    }
  }, [submission]);

  return (
    <div>
      <AccountManagerChatHeader selectedRecruiter={recruiterDetails} />
      <MessagesSection
        newChatId={chatId}
        minHeight="50vh"
        getMessages={getMessages}
        setGetMessages={setGetMessages}
      />
      <InputContainer
        setGetMessages={setGetMessages}
        newChatId={chatId}
        setNewChatId={setChatId}
      />
    </div>
  );
};

export default CandidateChatAccountManager;
