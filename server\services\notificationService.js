const { Notification } = require("../models/Notification.models");

const createNotification = async (data) => {
  try {
    const notificationInstance = new Notification(data);
    await notificationInstance.validate();
    return await notificationInstance.save();
  } catch (error) {
    console.error("create notification error:", error);
    throw error;
  }
};

module.exports = { createNotification };

// export const readNotification = async (data) => {
//   try {

//   } catch (error) {
//     console.error("create notification error:", error);
//     throw error;
//   }
// };
