const express = require("express");
const router = express.Router();
const Fieldvalidator = require("../middlewares/Fieldvalidator");
const { removeJobValidateConfig } = require("../validator/jobValidator");

//Jobs controller
const {
  getAllJobs,
  getSingleJob,
  createJob,
  updatejob,
  removeJob,
  bulkUpload,
  jobAssignToManager,
  publishJob,

  updateJob,
  getAllUnassignedJobs,
  getHighPeriorityJobs,
  getActiveJobs,
  getCloseJobs,
  getUnEngagedJobs,
  updateJobStatus,
  getJobRecruiterWorkOnRequest,
  getAllRecruitersWorkingOnJob,
  getJobsByAccountManager,
} = require("../controllers/Job");

//jobSubmission with condidate details controller
const { updateSubmitedJobsubmission } = require("../controllers/JobSubmission");

//workRequest controller
const {
  createWorkOnRequest,
  removeWorkOnRequest,
} = require("../controllers/WorkRequest");
const upload = require("../middlewares/uploadMiddleware");

const { auth, authorize } = require("../middlewares/auth");
const { getAllManagerWorkHistory } = require("../controllers/Manager");

// =========================== head manager ====================

//create new job
router.post("/create-job", auth, authorize("headAccountManager"), createJob);

// bulk upload job
router.post(
  "/bulkupload",
  auth,
  authorize("headAccountManager"),
  upload.single("csvFile"),
  bulkUpload
);

// assign to manager
router.patch(
  "/jobassigntomanager",
  auth,
  authorize("headAccountManager"),
  jobAssignToManager
);

// get job status wise
router.get("/getalljobs", auth, getAllJobs);
router.get(
  "/getunassignedjobs",
  auth,
  authorize("headAccountManager"),
  getAllUnassignedJobs
);
router.get("/gethighperiorityjobs", auth, getHighPeriorityJobs);
router.get("/getactivejobs", auth, getActiveJobs);
router.get("/getclosejobs", auth, getCloseJobs);
router.get("/getunengagedjobs", auth, getUnEngagedJobs);
router.get("/jobs-by-account-manager", auth, getJobsByAccountManager);

// update job status
router.patch("/updatejobstatus", auth, updateJobStatus);

// get job in details
router.get("/getJob/:jobID", auth, getSingleJob);

// publish job by account manager
// router.patch(
//   "/publishJobheadAccount",
//   auth,
//   authorize("headAccountManager"),
//   publishJob
// );

// update job by head account
router.patch(
  "/updatejobheadAccount",
  auth,
  authorize("headAccountManager"),
  updateJob
);

// get recruiter with jobs  with recruiter det to work
router.get(
  "/get-job-details-and-recruiters/:jobID",
  auth,
  getJobRecruiterWorkOnRequest
);

router.post("/workonrequest/create", auth, createWorkOnRequest);
router.delete("/workonrequest/remove", auth, removeWorkOnRequest);

// =========================== account manager ====================
// publish jobs
router.patch("/publishJob", auth, authorize("accountManager"), publishJob);

// update job by account manager
// router.patch("/updatejob", auth, authorize("accountManager"), updateJob);

router.get(
  "/am/gethighperiorityjobs",
  auth,
  authorize("accountManager"),
  getHighPeriorityJobs
);
router.get(
  "/am/getactivejobs",
  auth,
  authorize("accountManager"),
  getActiveJobs
);
router.get(
  "/am/getunengagedjobs",
  auth,
  authorize("accountManager"),
  getUnEngagedJobs
);
router.get("/am/getalljobs", auth, authorize("accountManager"), getAllJobs);
router.get("/am/getclosejobs", auth, authorize("accountManager"), getCloseJobs);

router.get(
  "/am/get-recruiters-working-on-job",
  auth,
  authorize("accountManager", "headAccountManager"),
  getAllRecruitersWorkingOnJob
);

// router.patch(
//   "/am/updatejobstatus",
//   "/am/getJob/:jobID",
//   auth,
//   authorize("accountManager"),
//   updateJobStatus
// );

// =========================== recruiter ====================

//insert job into workRequest by recruiter
router.post("/work/request", auth, authorize("recruiter"), createWorkOnRequest);

// update job details
router.patch("/update-job", updatejob);

//remove/delete jobs
router.delete(
  "/remove-job",
  Fieldvalidator(removeJobValidateConfig),
  removeJob
);

//get Condidate
// router.get(
//   "/condidate/fetch-condidate",
//   Fieldvalidator(getSingleJobSubmissionValidateConfig),
//   getSingleJobSubmission
// );
//get multiple Condidate
// router.get("/condidate/condidates-list", getMultipleJobSubmission);
//create new Condidate
// router.post(
//   "/condidate/job-submission",
//   Fieldvalidator(jobSubmissionCreationValidateConfig),
//   JobSubmissionANDcreateCondidate
// );
//update Condidate
router.patch("/condidate/update-condidate", updateSubmitedJobsubmission);

module.exports = router;
