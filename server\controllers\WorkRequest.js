const mongoose = require("mongoose");
const { workOnRequest } = require("../models/WorkOnRequest.models");
const job = require("../models/Job.models");
const { createNotification } = require("../services/notificationService");

//* @Desc create work on request
//* @Route POST /api/v1/job/workonrequest/create
//* @Access private
const createWorkOnRequest = async (req, res) => {
  try {
    const user = req.user;

    const { jobId, recruiters } = req.body;

    //user not found
    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "user not found" });
    }

    if (!jobId || !recruiters || recruiters.length < 1) {
      return res
        .status(400)
        .json({ success: false, message: "all field are required." });
    }

    let findQuery = {
      jobId: jobId,
      isDeleted: false,
      accountManager: { $exists: true },
      visibility: true,
    };

    if (user.userType == "accountManager") {
      findQuery = {
        jobId: jobId,
        isDeleted: false,
        accountManager: { $exists: true },
        visibility: true,
        "accountManager.userID": user.userId,
      };
    }
    // check job is publish and exist
    const jobDetails = await job.findOne(findQuery);

    if (!jobDetails) {
      return res.status(400).json({ success: false, message: "job not found" });
    }

    // check all recruiter have valid work on requests
    const recruitersCopy = JSON.parse(JSON.stringify(recruiters))?.map(
      (item) => {
        return { "recruiter.userId": item?.userID };
      }
    );

    const isJobAssigned = await workOnRequest.aggregate([
      {
        $facet: {
          isAlradyExist: [
            {
              $match: {
                "job.jobId": jobId,
                status: { $ne: "revokeRequest" },
                $or: recruitersCopy,
              },
            },
          ],
          revokeRequest: [
            {
              $match: {
                "job.jobId": jobId,
                status: "revokeRequest",
                $or: recruitersCopy,
              },
            },
            {
              $project: {
                _id: 1,
                userId: "$job.userId",
              },
            },
          ],
        },
      },
    ]);

    // check if request exist for that
    if (isJobAssigned[0]?.isAlradyExist[0]) {
      return res.status(400).json({
        success: false,
        message: "Job is already associated with recruiter .",
      });
    }

    const filterRecruiters = recruiters?.filter(
      (item) =>
        !isJobAssigned[0]?.revokeRequest
          ?.map((item) => item.userId)
          .includes(item)
    );

    const createWorkonRequests =
      filterRecruiters && filterRecruiters?.length > 0
        ? filterRecruiters?.map((item) => {
            return {
              job: {
                _id: jobDetails._id,
                jobId: jobId,
              },
              recruiter: {
                _id: item?._id,
                userId: item?.userID,
              },
              statusLogs: [
                {
                  from: "requestToWork",
                  to: "requestToWork",
                  changedBy: user._id,
                },
              ],
            };
          })
        : null;

    let workonrequest;
    if (createWorkonRequests) {
      workonrequest = await workOnRequest.insertMany(createWorkonRequests);
    }

    // update work on request if it will be revoke
    if (isJobAssigned[0]?.revokeRequest?.length > 0) {
      const bulkOps = isJobAssigned[0]?.revokeRequest?.map((item) => ({
        updateOne: {
          filter: {
            _id: new mongoose.Types.ObjectId(item?._id),
          },
          update: {
            $set: {
              status: "requestToWork",
            },
            $push: {
              statusLogs: {
                from: "revokeRequest",
                to: "requestToWork",
                changedBy: user?._id,
              },
            },
          },
        },
      }));

      // Execute all in one call
      await workOnRequest.bulkWrite(bulkOps);
    }

    await createNotification({
      recipients: [...new Set(recruiters?.map((item) => item?._id))],
      type: "work-on-request",
      relatedJobId: [jobId],
    });

    return res.status(200).json({
      success: true,
      data: workonrequest,
      message: "work on request will be create",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: err?.message,
    });
  }
};

//* @Desc remove work on request
//* @Route DELETE /api/v1/job/workonrequest/remove
//* @Access private
const removeWorkOnRequest = async (req, res) => {
  try {
    const user = req.user;
    const { jobId, recruiter } = req.body;

    //user not found
    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "user not found" });
    }

    if (!jobId || !recruiter) {
      return res
        .status(400)
        .json({ success: false, message: "all field are required." });
    }

    let findQuery = {
      jobId: jobId,
      isDeleted: false,
      accountManager: { $exists: true },
      visibility: true,
    };

    if (user.userType == "accountManager") {
      findQuery = {
        jobId: jobId,
        isDeleted: false,
        accountManager: { $exists: true },
        visibility: true,
        "accountManager.userID": user.userId,
      };
    }

    // check job is publish and exist
    const jobDetails = await job.findOne(findQuery);

    if (!jobDetails) {
      return res.status(400).json({ success: false, message: "job not found" });
    }

    const jobAssigned = await workOnRequest.findOneAndUpdate(
      {
        "job.jobId": jobId,
        "recruiter.userId": recruiter,
      },
      {
        $set: {
          status: "revokeRequest",
        },
        $push: {
          statusLogs: {
            to: "revokeRequest",
            from: "requestToWork",
            changedBy: user?._id,
          },
        },
      },
      { new: true }
    );

    if (!jobAssigned) {
      return res
        .status(400)
        .json({ success: false, message: "recruiter not found." });
    }

    return res.status(200).json({
      success: true,
      data: jobAssigned,
      message: "Recruiter remove from job.",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      message: err.message,
    });
  }
};

module.exports = {
  createWorkOnRequest,
  removeWorkOnRequest,
};
