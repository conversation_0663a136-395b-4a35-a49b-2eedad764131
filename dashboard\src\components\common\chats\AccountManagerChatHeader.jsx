import useAvatar from "../../../utils/AvatarGenerator";

const AccountManagerChatHeader = ({ selectedRecruiter }) => {
  const recruiterName = `${selectedRecruiter?.name?.firstName} ${
    selectedRecruiter?.name?.middleName
      ? selectedRecruiter?.name?.middleName + " "
      : ""
  }${selectedRecruiter?.name?.lastName}`;

  const avatarUri = useAvatar(recruiterName);

  return (
    <div className="flex p-6 justify-between items-center h-[4.375rem] self-stretch border-b border-b-[#F5F5F5] text-[#CBD4E1]">
      <div className="flex gap-4">
        {avatarUri && (
          <img
            src={avatarUri}
            alt="Avatar"
            className="w-10 h-10 rounded-[1.25rem]"
          />
        )}
        <div className="flex flex-col">
          <span className=" text-[#27364B] font-semibold text-[1.125rem] leading-[1.75rem]">
            {recruiterName}
          </span>
          <div className="w-fit flex items-center gap-2 ">
            <span className="w-[0.625rem] h-[0.625rem] bg-[#419E6A] rounded-full"></span>
            <span className=" text-[#94A3B8] text-[0.75rem] font-medium">
              Online
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountManagerChatHeader;
