const { Readable } = require('stream');
const csvParser = require('csv-parser');
const XLSX = require('xlsx');

//* @Desc Helper function for read file 
function readFileFromArrayBuffer(arrayBuffer, fileExtension) {
  const buffer = Buffer.from(arrayBuffer);

  return new Promise((resolve, reject) => {
    switch (fileExtension) {
      case 'csv':
        return readCSV(buffer)
          .then(resolve) 
          .catch(reject); 

      case 'xlsx':
      case 'xls':
        try {
          const data = readExcel(buffer);
          resolve(data); 
        } catch (error) {
          reject(error); 
        }
        break;

      default:
        reject('Unsupported file type');
    }
  });
}

function readCSV(buffer) {
  return new Promise((resolve, reject) => {
    const results = [];
    const readable = Readable.from(buffer);

    readable
      .pipe(csvParser())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results)) 
      .on('error', (err) => reject(err)); 
  });
}

function readExcel(buffer) {
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  const sheetName = workbook.SheetNames[0];
  const data = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
  return data; 
}


module.exports = {readFileFromArrayBuffer}