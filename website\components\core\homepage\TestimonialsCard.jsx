import Image from "next/image";

const TestimonialsCard = ({ name, testimonial, image, height }) => {
  return (
    <div
      className={`flex flex-col justify-between gap-3 p-4 bg-[#CEFFAD] lg:w-[18rem] xl:w-[24rem]  rounded-2xl`}
      style={{ height: `${height}rem` }}
    >
      <p className=" text-sm xl:text-[1rem]">{testimonial}</p>
      <div className="flex items-center justify-between w-full">
        <div className="flex flex-col ">
          <p>{name}</p>
          <p>Freelance Recruiter</p>
        </div>
        <div className="w-16 h-16 rounded-full overflow-hidden">
          <Image src={image} alt={name} />
        </div>
      </div>
    </div>
  );
};

export default TestimonialsCard;
