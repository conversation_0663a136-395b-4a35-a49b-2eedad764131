import React, { useState, useRef, useEffect } from "react";
import LocationDropdown from "./dropdowns/LocationDropdown";
import MultiSelectDropdown from "./dropdowns/MultiSelectDropdown";
import ExperienceLevelDropdown from "./dropdowns/ExperienceLevelDropdown";
import industries from "../../../data/industries.json";

const FilterSortBar = ({
  showSorting,
  setShowSorting,
  appliedSorting,
  availableFilters = [],
  selectedFilters = [],
  onFilterSelect,
  onFilterRemove,
  appliedFilters,
  onFilterChange,
  onApplyFilters,
  onClearAll,
  setShowFilters,
  pageType,
}) => {
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showSortingDropdown, setShowSortingDropdown] = useState(false);
  const [showSortingPillDropdown, setShowSortingPillDropdown] = useState(false);
  const [selectedSortingOptions, setSelectedSortingOptions] = useState([]);
  const [openOrderDropdown, setOpenOrderDropdown] = useState(null);
  const filterDropdownRef = useRef(null);
  const sortingDropdownRef = useRef(null);
  const sortingPillDropdownRef = useRef(null);

  // Filter labels for better display
  const filterLabels = {
    specialization: "Specialization",
    jobType: "Job Type",
    jobPosted: "Job Posted",
    submissionDate: "Submission Date",
    submissionRange: "Submission Range",
    salaryRange: "Salary Range",
    commission: "Commission",
    numberOfPosition: "No. of Position",
    numberOfRecruiter: "No. of Recruiter",
    jobTitle: "Job Title",
    country: "Country",
    state: "State",
    location: "Location",
    jobStatus: "Job Status",
    status: "Status",
    experienceLevel: "Experience Level",
    priority: "Priority",
    visibility: "Visibility",
    recruiterCount: "Recruiter Count",
    workOnRequestCount: "Work Requests",
    postedDate: "Posted Date",
    recruiterName: "Recruiter Name",
    clientName: "Client Name",
    jobsWorkingOn: "Jobs Working On",
  };

  // Sorting options
  const sortingOptions = [
    { key: "postedDate", label: "Posted Date" },
    { key: "submissionCount", label: "No. of Submission" },
    { key: "recruiterCount", label: "Total Recruiter" },
    { key: "submissionDate", label: "Submission Date" },
  ];

  // Sorting order options
  const sortingOrderOptions = {
    postedDate: [
      { key: "newest", label: "Newest First" },
      { key: "oldest", label: "Oldest First" }
    ],
    submissionCount: [
      { key: "highest", label: "Highest First" },
      { key: "lowest", label: "Lowest First" }
    ],
    recruiterCount: [
      { key: "highest", label: "Highest First" },
      { key: "lowest", label: "Lowest First" }
    ],
    submissionDate: [
      { key: "newest", label: "Newest First" },
      { key: "oldest", label: "Oldest First" }
    ]
  };

  // Handle clicks outside the filter dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target)
      ) {
        setShowFilterDropdown(false);
      }
    };

    if (showFilterDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showFilterDropdown]);

  // Handle clicks outside the sorting dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sortingDropdownRef.current &&
        !sortingDropdownRef.current.contains(event.target)
      ) {
        setShowSortingDropdown(false);
      }
    };

    if (showSortingDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSortingDropdown]);

  // Handle clicks outside the sorting pill dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        sortingPillDropdownRef.current &&
        !sortingPillDropdownRef.current.contains(event.target)
      ) {
        setShowSortingPillDropdown(false);
      }
    };

    if (showSortingPillDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showSortingPillDropdown]);

  // Handle clicks outside order dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close order dropdown if clicking outside
      if (openOrderDropdown && !event.target.closest('.relative')) {
        setOpenOrderDropdown(null);
      }
    };

    if (openOrderDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openOrderDropdown]);

  // Handle sorting option selection/deselection
  const handleSortingSelection = (sortingKey) => {
    const existingIndex = selectedSortingOptions.findIndex(option => option.key === sortingKey);

    if (existingIndex === -1) {
      // Add new sorting option
      const sortingOption = sortingOptions.find(option => option.key === sortingKey);
      if (sortingOption) {
        const defaultOrder = sortingOrderOptions[sortingKey][0];
        const newSortingOption = {
          key: sortingKey,
          label: sortingOption.label,
          order: defaultOrder.key,
          orderLabel: defaultOrder.label
        };
        setSelectedSortingOptions([...selectedSortingOptions, newSortingOption]);
      }
    } else {
      // Remove sorting option (uncheck)
      setSelectedSortingOptions(prev => prev.filter(option => option.key !== sortingKey));
    }

    // Keep dropdown open for multiple selections (don't close immediately)
  };

  // Handle sorting order change
  const handleSortingOrderChange = (sortingKey, newOrder) => {
    const orderOption = sortingOrderOptions[sortingKey].find(option => option.key === newOrder);
    setSelectedSortingOptions(prev =>
      prev.map(option =>
        option.key === sortingKey
          ? { ...option, order: newOrder, orderLabel: orderOption.label }
          : option
      )
    );
  };

  // Remove sorting option
  const removeSortingOption = (sortingKey) => {
    setSelectedSortingOptions(prev => prev.filter(option => option.key !== sortingKey));
  };

  // Get sorting pill display text
  const getSortingPillText = () => {
    if (selectedSortingOptions.length === 0) {
      return "Sorting";
    } else if (selectedSortingOptions.length === 1) {
      return selectedSortingOptions[0].label; // Show the name for first option
    } else {
      return `${selectedSortingOptions.length} Sort`; // Show count for multiple
    }
  };

  // Check if there are any applied filters or sorting
  const hasAppliedFiltersOrSorting = () => {
    return selectedFilters.length > 0 || selectedSortingOptions.length > 0;
  };

  // Handle Clear All - clear both filters and sorting from actual data
  const handleClearAll = () => {
    // Clear sorting options from UI
    setSelectedSortingOptions([]);
    setOpenOrderDropdown(null);
    setShowSortingPillDropdown(false);

    // Clear filters from UI
    selectedFilters.forEach(filterKey => {
      onFilterRemove(filterKey);
    });

    // Clear actual applied filters and sorting by calling parent's clear function
    // This should clear the data/results, not just the UI
    if (onClearAll) {
      onClearAll();
    } else {
      // Fallback: clear filters individually and apply empty state
      const clearedFilters = {};
      selectedFilters.forEach(filterKey => {
        clearedFilters[filterKey] = [];
        onFilterChange(filterKey, []);
      });

      // Apply the cleared state to actually clear the data
      if (onApplyFilters) {
        onApplyFilters(null, clearedFilters);
      }
    }
  };

  // Handle Apply - apply both filters and sorting
  const handleApply = () => {
    // Apply filters and sorting
    if (onApplyFilters) {
      onApplyFilters();
    }

    // TODO: Apply sorting options to parent component
    console.log("Applying sorting options:", selectedSortingOptions);

    // Don't show filter panels after applying
    if (setShowFilters) {
      setShowFilters(false);
    }
    if (setShowSorting) {
      setShowSorting(false);
    }
  };

  // Handle filter option toggle from dropdown
  const handleFilterOptionToggle = (filterKey) => {
    if (selectedFilters.includes(filterKey)) {
      // Remove filter if already selected
      onFilterRemove(filterKey);
    } else {
      // Add filter if not selected
      onFilterSelect(filterKey);
    }
    // Keep dropdown open for multiple selections
  };

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex gap-4 items-center flex-wrap">
        {/* Sorting Pills */}
        {selectedSortingOptions.length > 0 && (
          <div className="relative" ref={sortingPillDropdownRef}>
            <button
              onClick={() => setShowSortingPillDropdown(!showSortingPillDropdown)}
              className="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <span className="text-sm font-medium">{getSortingPillText()}</span>
              <svg
                className={`w-4 h-4 transition-transform ${
                  showSortingPillDropdown ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {/* Sorting Pills Dropdown */}
            {showSortingPillDropdown && (
              <div className="absolute top-full left-0 mt-1 w-[300px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="p-3">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-medium text-gray-700">Sorting</p>
                    <button
                      onClick={() => setShowSortingPillDropdown(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  <div className="space-y-3">
                    {selectedSortingOptions.map((sortingOption) => (
                      <div key={sortingOption.key} className="flex items-center gap-2">
                        {/* Sorting Option Name Pill */}
                        <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 border border-blue-200 text-blue-700 text-sm rounded">
                          <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                          {sortingOption.label}
                          <button
                            onClick={() => removeSortingOption(sortingOption.key)}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </span>

                        {/* Order Selection Pill with Dropdown */}
                        <div className="relative">
                          <button
                            onClick={() => {
                              setOpenOrderDropdown(openOrderDropdown === sortingOption.key ? null : sortingOption.key);
                            }}
                            className="inline-flex items-center gap-1 px-3 py-1 bg-blue-50 border border-blue-200 text-blue-700 text-sm rounded hover:bg-blue-100 transition-colors"
                          >
                            <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                            {sortingOption.orderLabel}
                            <svg
                              className={`w-3 h-3 transition-transform ${
                                openOrderDropdown === sortingOption.key ? "rotate-180" : ""
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>

                          {/* Order Options Dropdown */}
                          {openOrderDropdown === sortingOption.key && (
                            <div className="absolute top-full left-0 mt-1 w-[150px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
                              <div className="p-2">
                                {sortingOrderOptions[sortingOption.key].map((orderOption) => (
                                  <button
                                    key={orderOption.key}
                                    onClick={() => {
                                      handleSortingOrderChange(sortingOption.key, orderOption.key);
                                      setOpenOrderDropdown(null); // Close dropdown after selection
                                    }}
                                    className={`w-full text-left px-2 py-1 text-sm rounded hover:bg-gray-100 transition-colors ${
                                      sortingOption.order === orderOption.key ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                                    }`}
                                  >
                                    {orderOption.label}
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Sorting Dropdown Button */}
        <div className="relative" ref={sortingDropdownRef}>
          <button
            onClick={() => setShowSortingDropdown(!showSortingDropdown)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
              showSortingDropdown || appliedSorting
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <span className="text-lg">+</span>
            <span className="text-sm font-medium">Sorting</span>
            {appliedSorting && (
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                {appliedSorting}
              </span>
            )}
            <svg
              className={`w-4 h-4 transition-transform ${
                showSortingDropdown ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {/* Sorting Options Dropdown */}
          {showSortingDropdown && (
            <div className="absolute top-full left-0 mt-1 w-[250px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="p-3">
                <p className="text-sm font-medium text-gray-700 mb-3">
                  Select Sorting
                </p>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {sortingOptions.map((option) => (
                    <button
                      key={option.key}
                      onClick={() => handleSortingSelection(option.key)}
                      className="w-full text-left flex items-center gap-2 text-sm text-gray-700 hover:bg-gray-50 px-2 py-2 rounded cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedSortingOptions.some(selected => selected.key === option.key)}
                        readOnly
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span>{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Selected Filter Pills */}
        {selectedFilters.map((filterKey) => (
          <FilterPill
            key={filterKey}
            filterKey={filterKey}
            label={filterLabels[filterKey] || filterKey}
            selectedValues={appliedFilters[filterKey] || []}
            onFilterChange={onFilterChange}
            onApplyFilters={onApplyFilters}
            pageType={pageType}
          />
        ))}

        {/* Filter Dropdown Button */}
        <div className="relative" ref={filterDropdownRef}>
          <button
            onClick={() => setShowFilterDropdown(!showFilterDropdown)}
            className="flex items-center gap-2 px-4 py-2 rounded-lg border bg-white border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <span className="text-lg">+</span>
            <span className="text-sm font-medium">Filter</span>
            <svg
              className={`w-4 h-4 transition-transform ${
                showFilterDropdown ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {/* Filter Options Dropdown */}
          {showFilterDropdown && (
            <div className="absolute top-full left-0 mt-1 w-[250px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <div className="p-3">
                <p className="text-sm font-medium text-gray-700 mb-3">
                  Select Filters
                </p>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {availableFilters.map((filterKey) => (
                    <button
                      key={filterKey}
                      onClick={() => handleFilterOptionToggle(filterKey)}
                      className="w-full text-left flex items-center gap-2 text-sm text-gray-700 hover:bg-gray-50 px-2 py-2 rounded cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedFilters.includes(filterKey)}
                        readOnly
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <span>{filterLabels[filterKey] || filterKey}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Clear All and Apply Buttons */}
        {hasAppliedFiltersOrSorting() && (
          <div className="flex items-center gap-2 ml-auto">
            <button
              onClick={handleClearAll}
              className="px-4 py-2 text-[#A4A7AE] text-sm font-inter font-medium hover:text-gray-600 cursor-pointer"
            >
              Clear all
            </button>
            <button
              onClick={handleApply}
              className="px-4 py-2 bg-[#3E9900] hover:bg-green-700 text-white rounded-md text-sm font-medium"
            >
              Apply
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// FilterPill Component - Individual filter pill that can be clicked to open its dropdown
const FilterPill = ({
  filterKey,
  label,
  selectedValues,
  onFilterChange,
  pageType,
}) => {

  // Get filter options for different filter types
  const getFilterOptions = () => {
    const statusOptions = {
      submissions: [
        "submitted", "reviewing", "submitted to client", "selected",
        "interviewing", "awaiting offer", "rejected", "offer released",
        "offer accepted", "offer rejected", "hired-under guarantee period",
        "guarantee period not completed", "guarantee period completed",
      ],
      manager: ["Active", "Inactive", "on-leave"],
      jobs: ["Active", "Inactive", "Onhold", "Holdbyclient", "Filled", "Cancelled", "Closed"],
    };

    const currentStatusOptions = statusOptions[pageType] || statusOptions.jobs;

    return {
      location: [],
      jobType: ["full-time", "contract"],
      status: currentStatusOptions,
      specialization: industries?.map((item) => item.name) || [],
      experienceLevel: [],
      priority: ["High", "Medium", "Low"],
      visibility: ["Publish", "Unpublish"],
      recruiterCount: ["1-5", "6-10", "11-20", "20+"],
      workOnRequestCount: ["0", "1-5", "6-10", "10+"],
      postedDate: ["Past 7 days", "Past 15 days", "Past 30 days", "Custom"],
      jobPosted: ["Past 7 days", "Past 15 days", "Past 30 days", "Custom"],
      recruiterName: ["John Doe", "Jane Smith", "Mike Johnson", "Sarah Wilson"],
      jobTitle: ["Software Engineer", "Software Engineer", "Product Manager", "Data Scientist", "UX Designer"],
      submissionDate: ["Past 7 days", "Past 15 days", "Past 30 days", "Custom"],
      submissionRange: ["1-10", "11-50", "51-100", "100+"],
      salaryRange: ["0-50k", "50k-100k", "100k-150k", "150k+"],
      commission: ["5%", "10%", "15%", "20%"],
      country: [],
      state: ["Active", "Inactive"],
      clientName: ["Client A", "Client B", "Client C"],
      jobsWorkingOn: ["1-5", "6-10", "11-20", "20+"],
    };
  };

  // 1. ExperienceLevelDropdown -> (numberOfPosition, submissionRange, numberOfRecruiter, commission, salaryRange)
  if (filterKey === "numberOfPosition" || filterKey === "submissionRange" ||
      filterKey === "numberOfRecruiter" || filterKey === "commission" ||
      filterKey === "salaryRange") {

    // Configure min/max/prefix/suffix based on filter type
    let config = { min: 0, max: 100, step: 1, prefix: "", suffix: "", showInputFields: true };

    if (filterKey === "numberOfPosition" || filterKey === "numberOfRecruiter") {
      config = { min: 1, max: 50, step: 1, prefix: "", suffix: "", showInputFields: true };
    } else if (filterKey === "submissionRange") {
      config = { min: 1, max: 100, step: 1, prefix: "", suffix: "", showInputFields: true };
    } else if (filterKey === "salaryRange") {
      config = { min: 0, max: 200000, step: 1000, prefix: "$", suffix: "", showInputFields: true };
    } else if (filterKey === "commission") {
      config = { min: 0, max: 50, step: 1, prefix: "", suffix: "%", showInputFields: true };
    }

    return (
      <ExperienceLevelDropdown
        label={label}
        selectedValues={selectedValues}
        filterKey={filterKey}
        onLocalChange={onFilterChange}
        min={config.min}
        max={config.max}
        step={config.step}
        prefix={config.prefix}
        suffix={config.suffix}
        showInputFields={config.showInputFields}
      />
    );
  }

  // For location-based filters (country, state), use LocationDropdown
  if (filterKey === "country" || filterKey === "state") {
    return (
      <LocationDropdown
        label={label}
        selectedValues={selectedValues}
        filterKey={filterKey}
        onLocalChange={onFilterChange}
      />
    );
  }

  // 2. MultiSelectDropdown -> (jobPosted, specialization, jobType, submissionDate)
  // For all remaining filters, use MultiSelectDropdown
  return (
    <MultiSelectDropdown
      label={label}
      selectedValues={selectedValues}
      options={getFilterOptions()[filterKey] || []}
      filterKey={filterKey}
      onLocalChange={onFilterChange}
    />
  );
};

export default FilterSortBar;
