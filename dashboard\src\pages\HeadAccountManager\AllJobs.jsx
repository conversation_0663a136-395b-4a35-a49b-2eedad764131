import React, { useEffect, useState } from "react";
import PaginationFooter from "../../components/Table/TableFooter";
import ThreeDot from "../../components/Button/ThreeDot";
import TableHeader from "../../components/Table/TableHeader";
import viewImg from "../../assets/icons/view.svg";
import { useNavigate } from "react-router-dom";

const AllJobs = ({ getAllJobs }) => {
  const columns = [
    "Job ID",
    "Job Title",
    "Location",
    "Job Type",
    "Submission",
    "Status",
    "",
  ];

  const dummyJobs = [
    {
      jobid: "J101",
      jobtitle: "Software Engineer",
      location: "Delhi",
      jobtype: "Full Time",
      submission: 12,
      status: "Active",
    },
    {
      jobid: "J102",
      jobtitle: "Frontend Developer",
      location: "Mumbai",
      jobtype: "Contract",
      submission: 8,
      status: "Active",
    },
    {
      jobid: "J103",
      jobtitle: "Backend Developer",
      location: "Bangalore",
      jobtype: "Full Time",
      submission: 15,
      status: "Active",
    },
    {
      jobid: "J104",
      jobtitle: "UI/UX Designer",
      location: "Pune",
      jobtype: "Part Time",
      submission: 6,
      status: "Active",
    },
  ];

  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [allJobs, setAllJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const totalResults = allJobs.length;
  const totalPages = Math.ceil(totalResults / pageSize);

  const paginatedData = allJobs.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  useEffect(() => {
    // Simulate API call with dummy data
    async function callApi() {
      setLoading(true);
      try {
        // Simulate API delay
       
        setAllJobs(dummyJobs);
      } catch (error) {
        console.error("Error fetching active jobs:", error);
      } finally {
        setLoading(false);
      }
    }
    callApi();
  }, []);
  return (
    <>
      <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <TableHeader columns={columns} />
        <tbody>
          {paginatedData?.map((job, index) => (
            <tr
              key={job.id || index}
              className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800"
            >
              {Object.keys(job)?.map((item, idx) => {
                return (
                  <td key={idx} className="px-4 py-3 text-left">
                    {job[item]}
                  </td>
                );
              })}
              <td className="px-4 py-3 text-start">
                <ThreeDot
                  dropdownSize="w-32"
                  buttonDropDown={
                    <>
                      <ul className="py-0" role="none">
                        <li>
                          <div
                            onClick={(e) => {
                              e.preventDefault();
                              navigate(
                                `/jobs/jobdetails?jobId=${job.jobid}&tab=jobdetails`
                              );
                            }}
                            className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
                            role="menuitem"
                          >
                            <img src={viewImg} alt="View Job" />
                            View Job
                          </div>
                        </li>
                      </ul>
                    </>
                  }
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <PaginationFooter
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        totalResults={totalResults}
      />
    </>
  );
};
export default AllJobs;
