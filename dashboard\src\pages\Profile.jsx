import React, { useState, useEffect, useMemo } from "react";
import { useFormik } from "formik";
import InputField from "../components/common/Input/InputField";
import SelectField from "../components/common/Input/SelectionField";
import FileInput from "../components/common/Input/FileInput";
import AppButton from "../components/common/Button/AppButton";
import candidateRoles from "../data/candidateRoles";
import industries from "../data/industries";
import ProfileSummary from "../components/common/ProfileSummary";
import Select from "react-select";
import countries from "../data/countries.json";

import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import countriesWithPhoneNumberLength from "../data/countriesWithPhoneNumberLength.json";
import { updateProfile } from "../services/operations/userAPI";
import { userInfo } from "../redux/reducer/auth.slice";

const Profile = () => {
  const [resumeFile, setResumeFile] = useState(null);
  const [existingResume, setExistingResume] = useState("");
  const [resumeError, setResumeError] = useState("");
  const [loading, setLoading] = useState(false); // State to manage loading during API call
  const userInfoFromRedux = useSelector((state) => state.auth?.user); // Renamed to avoid conflict with `userInfo` action
  const dispatch = useDispatch(); // Initialize useDispatch

  const MAX_RESUME_SIZE_MB = 10;

  const formik = useFormik({
    initialValues: {
      fullName: "",
      email: "",
      phone: "",
      countryCode: "",
      linkedin: "",
      country: "",
      state: "",
      industry: "",
      role: "",
      about: "",
    },
    validate: (values) => {
      const errors = {};

      if (!values.fullName) errors.fullName = "Name is Required";
      if (!values.email) errors.email = "Valid Email is Required";
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(values.email))
        errors.email = "Invalid email address";

      if (!values.phone || values.phone.length < 5) {
        errors.phone = "Valid Phone number is Required";
      }

      if (!values.linkedin) errors.linkedin = "Valid LinkedIn ID is Required";
      else if (!/^https?:\/\/(www\.)?linkedin\.com\/.+$/.test(values.linkedin))
        errors.linkedin = "Invalid LinkedIn URL";

      if (!values.country) errors.country = "Country is Required";
      if (!values.state) errors.state = "State is Required";
      if (!values.industry) errors.industry = "Industry is Required";
      if (!values.role) errors.role = "Role is Required";
      if (!values.about) errors.about = "About is Required";

      return errors;
    },
    validateOnChange: false, // Prevents validation on every input change, validates on blur or submit
    validateOnBlur: true, // Validates on input blur
    onSubmit: async (values, { setSubmitting }) => {
      console.log("Form submission initiated with values:", values);
      setSubmitting(true); // Disable the submit button immediately

      const errors = await formik.validateForm(); // Run full validation before proceeding

      if (Object.keys(errors).length > 0) {
        // Display all accumulated errors from validation
        Object.values(errors).forEach((error) => toast.error(error));
        setSubmitting(false); // Re-enable button if validation fails
        return;
      }

      // Resume specific validation check
      const currentResumeError =
        resumeError ||
        (!resumeFile && !existingResume ? "Resume is required." : "");
      if (currentResumeError) {
        toast.error(currentResumeError);
        setResumeError(currentResumeError); // Keep the error state updated for FileInput
        setSubmitting(false); // Re-enable button if resume is missing/invalid
        return;
      }

      // If all validations pass, proceed with profile update
      await handleProfileUpdate(values);
      // setSubmitting(false) will be handled in the finally block of handleProfileUpdate
    },
  });

  const handleProfileUpdate = async (values) => {
    try {
      setLoading(true);
      const formData = new FormData();

      formData.append("fullname", values.fullName);
      formData.append("email", values.email);
      let phoneNumber = values.phone;
      if (values.countryCode && phoneNumber.startsWith(values.countryCode)) {
        phoneNumber = phoneNumber.substring(values.countryCode.length);
      }
      formData.append("phoneNo", phoneNumber);
      formData.append("phoneCountryCode", values.countryCode);
      formData.append("linkedin", values.linkedin);
      formData.append("country", values.country);
      formData.append("state", values.state);
      formData.append("candidateRole", JSON.stringify(values.role));
      formData.append("about", values.about);
      formData.append("domain", JSON.stringify(values.industry));
      if (resumeFile) {
        formData.append("resume", resumeFile);
      }

      console.log(userInfoFromRedux);

      // Call the API function to update the profile
      const response = await updateProfile(formData, userInfoFromRedux.userID); // Use userInfoFromRedux here
      console.log("Profile update API response:", response);

      // Dispatch Redux action to update user information in the store
      if (response?.data?.user) {
        dispatch(userInfo(response.data.user));
      }

      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneInputChange = (value, country) => {
    formik.setFieldValue("phone", value);
    // Ensure country.dialCode exists; provide a default empty string if not
    formik.setFieldValue("countryCode", country?.dialCode || "");
    formik.setFieldTouched("phone", true); // Mark phone field as touched for validation
  };

  const handleResumeChange = (event) => {
    const file = event.target.files[0];
    setResumeError(""); // Clear previous errors
    setResumeFile(null); // Clear previous file state
    setExistingResume(""); // Clear existing resume URL if a new file is being chosen

    if (!file) {
      if (!existingResume) {
        setResumeError("Resume is required."); // Only set required error if no file AND no existing resume
      }
      return;
    }

    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    ];
    if (!allowedTypes.includes(file.type)) {
      const errorMsg = "Invalid file type. Only PDF and DOC/DOCX are allowed.";
      toast.error(errorMsg);
      setResumeError(errorMsg);
      event.target.value = null; // Clear the input field to allow re-selection
      return;
    }

    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > MAX_RESUME_SIZE_MB) {
      const errorMsg = `Resume size exceeds the limit of ${MAX_RESUME_SIZE_MB}MB.`;
      toast.error(errorMsg);
      setResumeError(errorMsg);
      event.target.value = null; // Clear the input field to allow re-selection
      return;
    }
    setResumeFile(file); // Set the selected file
    toast.success("Resume selected successfully!");
  };

  const handleResumeRemove = () => {
    setResumeFile(null); // Clear the selected file
    setExistingResume(""); // Clear the existing resume URL
    setResumeError("Resume is required."); // Mark as required after removal
  };

  // Helper to check if a Formik field has an error and has been touched
  function isFormFormik(name) {
    return !!(formik.touched[name] && formik.errors[name]);
  }

  // Helper to get error message for a Formik field
  const getFormErrorMessage = (name) =>
    isFormFormik(name) && (
      <small className="text-red-500 block">{formik.errors[name]}</small>
    );

  const commonInputCss =
    "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm placeholder-gray-400";

  useEffect(() => {
    if (userInfoFromRedux) {
      // Use userInfoFromRedux here
      const fullName = [
        userInfoFromRedux?.firstName,
        userInfoFromRedux?.middleName,
        userInfoFromRedux?.lastName,
      ]
        .filter(Boolean)
        .join(" "); // Joins non-empty parts with a space

      formik.setFieldValue("fullName", fullName);
      formik.setFieldValue("email", userInfoFromRedux?.email || "");
      formik.setFieldValue(
        "phone",
        userInfoFromRedux?.phoneNo?.countryCode +
          userInfoFromRedux?.phoneNo?.number || ""
      );
      formik.setFieldValue(
        "countryCode",
        userInfoFromRedux?.phoneNo?.countryCode || ""
      );
      formik.setFieldValue(
        "linkedin",
        userInfoFromRedux?.profile?.linkedin || ""
      );

      const userCountryName = userInfoFromRedux?.profile?.country;

      formik.setFieldValue("country", userCountryName || "");

      formik.setFieldValue("state", userInfoFromRedux?.profile?.state || "");
      formik.setFieldValue(
        "role",
        userInfoFromRedux?.profile?.candidateRole || ""
      );
      formik.setFieldValue(
        "industry",
        userInfoFromRedux?.profile?.domain || ""
      );
      formik.setFieldValue("about", userInfoFromRedux?.profile?.about || "");
      setExistingResume(userInfoFromRedux?.profile?.resume || ""); // Set existing resume URL
    }
  }, [userInfoFromRedux]);

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">My Profile</h2>

      <div className="flex flex-col md:flex-row gap-6">
        <ProfileSummary
          name={formik.values.fullName || "User Name"}
          email={formik.values.email || "<EMAIL>"}
        />

        <div className="md:w-3/4">
          <form
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
            onSubmit={formik.handleSubmit}
          >
            <InputField
              label="Full Name"
              required
              name="fullName"
              value={formik.values.fullName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              errorMessage={getFormErrorMessage("fullName")}
              css={commonInputCss}
            />
            <InputField
              label="Email Address"
              required
              name="email"
              type="email"
              disable={true} // Email is typically not editable
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              errorMessage={getFormErrorMessage("email")}
              css={commonInputCss}
            />

            <div className="flex flex-col w-full space-y-1">
              <label
                htmlFor="phone"
                className="text-sm font-medium text-[#374151]"
              >
                Phone Number
                <span className="text-red-500">*</span>
              </label>
              <PhoneInput
                country={"us"} // Default country
                value={formik.values.phone}
                onChange={handlePhoneInputChange}
                countries={countriesWithPhoneNumberLength}
                required={true}
                name="phone"
                id="phone"
                enableSearch={true}
                containerClass="w-full"
                inputClass="!h-[2.6rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                buttonClass="!h-[2.6rem] !border-r border-gray-300"
                placeholder="Enter your phone number"
                onBlur={formik.handleBlur("phone")}
              />
              {getFormErrorMessage("phone")}
            </div>

            <InputField
              label="LinkedIn Profile"
              required
              name="linkedin"
              value={formik.values.linkedin}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              errorMessage={getFormErrorMessage("linkedin")}
              css={commonInputCss}
            />

            {/* <SelectField
              label="Country"
              required
              name="country"
              value={formik.values.country}
              onChange={(e) => {
                formik.handleChange(e);
                formik.setFieldValue("state", ""); // Reset state when country changes
              }}
              onBlur={formik.handleBlur}
              placeholder="Select country"
              options={Country.getAllCountries()?.map((item) => ({
                value: `${item.name}-${item.isoCode}`, // Store both name and ISO code for easy lookup
                label: item.name,
              }))}
              errorMessage={getFormErrorMessage("country")}
            /> */}

            <div className="w-full">
              <label
                htmlFor="country"
                className="text-sm font-medium text-[#374151]"
              >
                Country {<span className="text-red-800">*</span>}
              </label>
              <Select
                options={countries.map((country) => ({
                  value: country.name,
                  label: country.name,
                }))}
                onChange={(e) => {
                  formik.setFieldValue("country", e.value);
                  formik.setFieldValue("state", "");
                }}
                value={
                  formik.values.country
                    ? {
                        value: formik.values.country,
                        label: formik.values.country,
                      }
                    : null
                }
              />
              {getFormErrorMessage("country")}
            </div>

            <div className="w-full">
              <label
                htmlFor="State"
                className="text-sm font-medium text-[#374151]"
              >
                State {<span className="text-red-800">*</span>}
              </label>
              <Select
                options={
                  countries
                    .find((country) => country.name === formik.values.country)
                    ?.states.map((state) => ({
                      value: state.name,
                      label: state.name,
                    })) || []
                }
                onChange={(e) => {
                  formik.setFieldValue("state", e.value);
                }}
                value={
                  formik.values.state
                    ? {
                        value: formik.values.state,
                        label: formik.values.state,
                      }
                    : null
                }
                isDisabled={!formik.values.country}
              />
              {getFormErrorMessage("state")}
            </div>

            {/* <SelectField
              label="State"
              required
              name="state"
              value={formik.values.state}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Select state"
              options={stateOptions} // Dynamically loaded states
              errorMessage={getFormErrorMessage("state")}
              disabled={!formik.values.country || stateOptions.length === 0} 
            /> */}

            <SelectField
              label="Industry"
              required
              name="industry"
              value={formik.values.industry}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Select industry"
              options={industries.map((industry) => ({
                label: industry.name,
                value: industry.name, // Ensure ID is a string if your backend expects it
              }))}
              errorMessage={getFormErrorMessage("industry")}
            />
            <SelectField
              label="Candidate Role"
              required
              name="role"
              value={formik.values.role}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Select role"
              options={candidateRoles.map((role) => ({
                label: role.name,
                value: role.name, // Ensure ID is a string if your backend expects it
              }))}
              errorMessage={getFormErrorMessage("role")}
            />
            <div className="col-span-1 md:col-span-2 flex flex-col space-y-1">
              <label
                htmlFor="about"
                className="text-sm font-medium text-gray-700"
              >
                About
              </label>
              <textarea
                id="about"
                name="about"
                rows="4"
                placeholder="Write here..."
                className={`w-full ${commonInputCss}`}
                value={formik.values.about}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {getFormErrorMessage("about")}
            </div>
            <div className="col-span-1 md:col-span-2">
              <FileInput
                label="CV/Resume"
                required
                name="resume"
                value={resumeFile} // Current selected file object
                onChange={handleResumeChange}
                onRemove={handleResumeRemove}
                maxSizeMB={MAX_RESUME_SIZE_MB}
                initialFileName={existingResume} // For displaying existing resume name
                errorMessage={
                  resumeError ? (
                    <small className="text-red-500 block">{resumeError}</small>
                  ) : !resumeFile &&
                    !existingResume &&
                    formik.submitCount > 0 ? (
                    <small className="text-red-500 block">
                      Resume is required
                    </small>
                  ) : null
                }
              />
            </div>
            <div className="col-span-1 md:col-span-2 flex gap-3 mt-4">
              <AppButton
                type="submit"
                label={loading ? "Updating..." : "Update"} // Show loading text
                disabled={formik.isSubmitting || loading} // Disable if formik is submitting or local loading state is true
              />
              <AppButton type="button" variant="secondary" label={"Cancel"} />
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Profile;
