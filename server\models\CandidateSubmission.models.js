const mongoose = require("mongoose");
const generateId = require("../utils/genreateId");

const submissionEnums = [
  "submitted",
  "reviewing",
  "submitted to client",
  "selected",
  "interviewing",
  "reject after interview",
  "awaiting offer",
  "rejected",
  "offer released",
  "offer accepted",
  "offer rejected",
  "hired-under guarantee period",
  "guarantee period not completed",
  "guarantee period completed",
];

const candidateSubmissionSchema = new mongoose.Schema(
  {
    submissionId: {
      type: String,
    },
    job: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Job",
        required: true,
      },
      jobID: { type: String, required: true },
    },
    submittedBy: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      userID: { type: String, required: true },
    },
    candidate: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Candidate",
        required: true,
      },
      candidateID: { type: String, required: true },
    },
    isInstantSubmit: {
      type: Boolean,
    },
    status: {
      type: String,
      enum: submissionEnums,
      default: "submitted",
    },
    submittedAt: {
      type: Date,
      default: Date.now,
    },
    notes: {
      type: String,
      require: function () {
        return [
          "rejected",
          "offer rejected",
          "guarantee period not completed",
        ].includes(this.status);
      },
    },
    chatId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Chat",
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

candidateSubmissionSchema.pre(["save"], async function (next) {
  this.submissionId = await generateId("HJS");
  next();
});

const candidateSubmission = mongoose.model(
  "candidateSubmission",
  candidateSubmissionSchema
);

module.exports = { candidateSubmission, submissionEnums };
