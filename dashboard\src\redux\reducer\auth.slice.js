import { createSlice } from "@reduxjs/toolkit";

const accessToken = localStorage.getItem("accessToken");
const refreshToken = localStorage.getItem("refreshToken");

const initialState = {
  token:
    accessToken && refreshToken
      ? {
          accessToken,
          refreshToken,
        }
      : null,
  user: null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginToken: (state, action) => {
      localStorage.setItem("accessToken", action.payload?.accessToken);
      localStorage.setItem("refreshToken", action.payload?.refreshToken);
      state.token = action.payload;
    },
    logOut: (state) => {
      localStorage.clear();
      state.token = null;
      state.user = null;
    },
    userInfo: (state, action) => {
      state.user = action.payload;
    },
    updateUserCoins: (state, action) => {
      if (state.user) {
        state.user.coinBalance = action.payload;
      }
    },
  },
});

export const { loginToken, logOut, userInfo, updateUserCoins } =
  authSlice.actions;

export default authSlice.reducer;
