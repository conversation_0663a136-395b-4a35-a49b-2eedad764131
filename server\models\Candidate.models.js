const mongoose = require("mongoose");

const candidateSchema = new mongoose.Schema(
  {
    candidateID: {
      type: String,
    },
    personalDetails: {
      firstName: { type: String, required: true, trim: true },
      lastName: { type: String, required: true, trim: true },
      phoneCountryCode: { type: String, required: true },
      phoneNumber: {
        type: String,
        required: true,
        // match: [/^\d{10}$/, "Phone number must be 10 digits"],
      },
      emailAddress: {
        type: String,
        required: true,
        lowercase: true,
        match: [/\S+@\S+\.\S+/, "Invalid email"],
      },
      currentAddress: { type: String, required: true },
      city: { type: String, required: true },
      state: { type: String, required: true },
      country: { type: String, required: true },
      zipcode: { type: String, required: true },
      relocationWillingness: { type: Boolean, default: false },
      workAuthorizationStatus: { type: String, required: true },
      ssnLast4Digit: { type: String, match: [/^\d{4}$/, "Must be 4 digits"] },
      availableStartDate: { type: Date },
    },

    licensing: {
      stateLicenses: { type: String },
      licenseExpireDate: { type: Date },
      compactLicense: { type: Boolean },
    },

    certification: {
      blsCertification: { type: Boolean },
      blsExpiration: {
        type: String,
      },
      aclsPalsNals: { type: Boolean },
      aclsPalsNalsExpiration: {
        type: String,
      },
      otherRelevantCertificate: { type: String },
    },

    education: {
      type: [
        {
          _id: false,
          degree: {
            type: String,
          },
          collegeName: {
            type: String,
          },
          graduationYear: {
            type: String,
          },
        },
      ],
      required: false,
    },

    workHistory: {
      mostRecentEmployer: { type: String },
      positionTitle: { type: String },
      employmentDate: { type: String },
      reasonForLeaving: { type: String },
      supervisorReferenceName: { type: String },
      supervisorReferenceTitle: { type: String },
      supervisorReferenceContact: { type: String },
      professionalReferenceName: { type: String },
      professionalReferenceName2: { type: String },
      professionalReferenceContact1: { type: String },
      professionalReferenceContact2: { type: String },
      professionalReferenceContact3: { type: String },
    },

    skillsAndExperience: {
      totalYearsOfExperience: {
        month: { type: Number },
        year: { type: Number },
      },
      relevantExperience: {
        month: { type: Number },
        year: { type: Number },
      },
      otherSkills: { type: [{ type: String }], required: false },
    },

    healthAndCompliance: {
      covid19Status: { type: Boolean },
      dateOfLastCovid19Dose: { type: Date },
      boosterReceived: { type: Boolean },
      proofOfVaccinationAvailable: { type: Boolean },
      fluVaccination: { type: Boolean },
    },

    submissionDetails: {
      rateExpectation: { type: String, enum: ["Hourly", "Salary"] },
      referenceProvider: {
        type: String,
        enum: ["Yes", "No"],
      },
      candidateAvailabilityForInterview: { type: Date },
      additionalNote: { type: String },
    },

    documentAttachments: {
      resume: { type: String },
      coverLetter: { type: String },
      licenseCopy: { type: String },
      blsCertificate: { type: String },
      aclsPalsNalsCertificate: { type: String },
      fluVaccinationProof: { type: String },
      covid19VaccinationProof: { type: String },
      recentSkillChecklist: { type: String },
    },

    createdBy: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: "User",
      },
      userID: { type: String, required: true },
    },
    expireBy: {
      type: Date,
      default: () => new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
    },
    isDeleted: {
      flag: { type: Boolean },
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      userId: { type: String },
    },
  },
  {
    timestamps: true,
  }
);

const Candidates = mongoose.model("Candidate", candidateSchema);

module.exports = { Candidates };
