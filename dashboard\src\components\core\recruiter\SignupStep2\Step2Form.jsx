import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import countries from "../../../../data/countries.json";
import industries from "../../../../data/industries.json";
import candidateRoles from "../../../../data/candidateRoles.json";
import Select from "react-select";
import { updateProfile } from "../../../../services/operations/userAPI";
import { toast, ToastContainer } from "react-toastify";

const Step2Form = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("userId");

  const [isIndutriesLimitReached, setIsIndutriesLimitReached] = useState(false);
  const [isCandidateRolesLimitReached, setIsCandidateRolesLimitReached] =
    useState(false);

  const [userData, setUserData] = useState({
    country: "",
    state: "",
    domain: [], // MULTI-SELECT: Industry
    specialization: [], // MULTI-SELECT: Candidate Roles
    about: "",
  });

  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileSelect = (file) => {
    const validTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    if (!validTypes.includes(file.type)) {
      toast.error("Please upload a PDF or DOC file");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("File size should be less than 10MB");
      return;
    }

    setSelectedFile(file);
  };

  const handleInputChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (
        !userData.country ||
        !userData.state ||
        userData?.domain?.length === 0 ||
        userData?.specialization?.length === 0 ||
        !userData.about
      ) {
        toast.error("Please fill all required fields");
        return;
      }

      if (!userId) {
        toast.error("User ID not found. Please try again.");
        return;
      }
      const formData = new FormData();
      formData.append("country", userData.country);
      formData.append("state", userData.state);
      formData.append("domain", JSON.stringify(userData.domain)); // serialize array
      formData.append("candidateRole", JSON.stringify(userData.specialization)); // serialize array
      formData.append("about", userData.about);
      formData.append("resume", selectedFile);
      formData.append("isSignup", true); // Indicating this is a signup step

      const response = await updateProfile(formData, userId);

      if (response.success) {
        toast.success("Profile updated successfully");
        // navigate("/signup/thank-you");
        navigate(`/signup/verify-email?userId=${userId}`);
      }
    } catch (error) {
      toast.error(error.message || "Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    return (
      userData.country &&
      userData.state &&
      userData.domain.length > 0 && // At least one industry selected
      userData.specialization.length > 0 && // At least one candidate role selected
      userData.about
    );
  };

  useEffect(() => {
    if (userData.domain.length > 5) {
      setIsIndutriesLimitReached(true);
    } else {
      setIsIndutriesLimitReached(false);
    }
  }, [userData.domain]);

  return (
    <form
      onSubmit={handleSubmit}
      className="flex flex-col items-center justify-center gap-6"
    >
      <div className="flex flex-col w-full gap-2">
        <label htmlFor="country" className="text-sm font-medium text-[#374151]">
          Country
        </label>
        <Select
          options={countries.map((country) => ({
            value: country.name,
            label: country.name,
          }))}
          onChange={(e) => setUserData({ ...userData, country: e.value })}
          value={
            userData.country
              ? { value: userData.country, label: userData.country }
              : null
          }
        />
      </div>

      <div className="flex flex-col w-full gap-2">
        <label htmlFor="state" className="text-sm font-medium text-[#374151]">
          State
        </label>
        <Select
          options={
            countries
              .find((country) => country.name === userData.country)
              ?.states.map((state) => ({
                value: state.name,
                label: state.name,
              })) || []
          }
          onChange={(e) => setUserData({ ...userData, state: e.value })}
          value={
            userData.state
              ? { value: userData.state, label: userData.state }
              : null
          }
          isDisabled={!userData.country}
        />
      </div>

      <div className="flex flex-col w-full gap-2">
        <label htmlFor="domain" className="text-sm font-medium text-[#374151]">
          Industry
        </label>
        <Select
          isMulti
          options={industries.map((industry) => ({
            value: industry.name,
            label: industry.name,
          }))}
          onChange={(selected) => {
            if (selected.length > 5) {
              setIsIndutriesLimitReached(true);

              return;
            }
            setUserData({
              ...userData,
              domain: selected ? selected.map((opt) => opt.value) : [],
            });
          }}
          value={industries
            .filter((industry) => userData.domain.includes(industry.name))
            .map((industry) => ({
              value: industry.name,
              label: industry.name,
            }))}
        />

        <p
          className={`${
            isIndutriesLimitReached ? "block" : "hidden"
          } text-red-600`}
        >
          You can select upto 5 industries only
        </p>
      </div>

      <div className="flex flex-col w-full gap-2">
        <label
          htmlFor="specialization"
          className="text-sm font-medium text-[#374151]"
        >
          Candidate Roles
        </label>
        <Select
          isMulti
          options={candidateRoles.map((role) => ({
            value: role.name,
            label: role.name,
          }))}
          onChange={(selected) => {
            setUserData({
              ...userData,
              specialization: selected ? selected.map((opt) => opt.value) : [],
            });
          }}
          value={candidateRoles
            .filter((role) => userData.specialization.includes(role.name))
            .map((role) => ({
              value: role.name,
              label: role.name,
            }))}
        />
      </div>

      <div className="flex flex-col w-full gap-2">
        <label htmlFor="about" className="text-sm font-medium text-[#374151]">
          About
        </label>
        <textarea
          id="about"
          className="w-full resize-none border-2 border-[#E5E7EB] rounded-md p-2"
          placeholder="Tell us a bit about you - your journey, passions, and what drives you."
          value={userData.about}
          onChange={(e) => setUserData({ ...userData, about: e.target.value })}
          rows={5}
        />
      </div>

      <div className="flex flex-col w-full gap-2">
        <label htmlFor="resume" className="text-sm font-medium text-[#374151]">
          CV / Resume
        </label>
        <div
          className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
            isDragging ? "border-indigo-500 bg-indigo-50" : "border-gray-300"
          }`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <div className="space-y-1 text-center flex flex-col items-center justify-center gap-2">
            <img src={"/assets/icons/upload-file.svg"} alt="" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="file-upload"
                className="relative cursor-pointer bg-white rounded-md font-medium focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
              >
                Upload your
                <span className="text-indigo-600 hover:text-indigo-500">
                  {" "}
                  CV / Resume
                </span>{" "}
                or drag and drop
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  className="sr-only"
                  onChange={handleInputChange}
                  accept=".pdf,.doc,.docx"
                />
              </label>
            </div>
            <p className="text-xs text-gray-500">PDF, DOC up to 10MB</p>
            {selectedFile && (
              <p className="text-sm text-indigo-600">
                Selected file: {selectedFile.name}
              </p>
            )}
          </div>
        </div>
      </div>

      <button
        type="submit"
        disabled={!isFormValid() || loading}
        className={`bg-[#65FF00] text-[#102108] cursor-pointer font-medium px-4 py-2 rounded-md w-full ${
          !isFormValid() || loading ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        {loading ? "Updating..." : "Submit"}
      </button>
      <ToastContainer />
    </form>
  );
};

export default Step2Form;
