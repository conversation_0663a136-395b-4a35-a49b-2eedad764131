const FormStepper = ({ currentStepIndex, setCurrentStepIndex, steps }) => {
  return (
    <div
      className="p-6 text-sm h-full"
      style={{
        borderRadius: "16px",
        border: "1px solid #CBD4E1",
        background: "#F7F7F7",
        boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.08)",
      }}
    >
      <ul className="h-full flex flex-col items-start mb-6">
        {steps?.map((step, index) => {
          const isActive = index === currentStepIndex;
          const isCompleted = index < currentStepIndex;
          const canNavigate = index < currentStepIndex;
          const isDisabled = index > currentStepIndex;
          return (
            <li
              key={index}
              // className="h-full flex items-start min-h-[64px] cursor-pointer group"
              className={`relative flex items-start min-h-[64px] group ${
                canNavigate ? "cursor-pointer" : "cursor-not-allowed"
              } ${isDisabled ? "opacity-60" : ""}`}
              onClick={() => {
                if (canNavigate) setCurrentStepIndex(index);
              }}
            >
              <div className="h-full flex flex-col items-center mr-4">
                {/* Icon wrapper */}
                <div className="w-6 h-6 flex items-center justify-center">
                  {isActive ? (
                    <img
                      src="/assets/icons/Stepper.svg"
                      alt="current step"
                      className="w-6 h-6"
                    />
                  ) : isCompleted ? (
                    <div
                      className="w-5 h-5 rounded-full"
                      style={{ backgroundColor: "#419E6A" }}
                    />
                  ) : (
                    <img
                      src="/assets/icons/Stepper.svg"
                      alt="step"
                      className="w-5 h-5"
                    />
                  )}
                </div>

                {/* Vertical Line */}
                {index !== steps.length - 1 && (
                  <div className="flex-1 flex flex-col justify-center">
                    <div
                      className="w-0.75 h-full"
                      style={{
                        backgroundColor: isActive
                          ? "#4D82F3"
                          : isCompleted
                          ? "#419E6A"
                          : "#D1D5DB",
                      }}
                    />
                    <div
                      className="w-0.75 h-full"
                      style={{
                        backgroundColor: isCompleted ? "#419E6A" : "#D1D5DB",
                      }}
                    />
                  </div>
                )}
              </div>

              <div className={`flex flex-col ${!isActive && "mb-7"}`}>
                <span
                  className="text-xs uppercase tracking-wide"
                  style={{ color: "#27364B" }}
                >
                  Step {index + 1}
                </span>
                <span
                  className={`${
                    isActive
                      ? "text-blue-700 font-medium"
                      : isCompleted
                      ? "text-[#419E6A]"
                      : "text-gray-500"
                  } `}
                >
                  {step}
                </span>
                {isActive && (
                  <span className="mt-1 inline-block px-2 py-0.5 bg-blue-100 text-blue-600 text-xs rounded-full w-fit">
                    In Progress
                  </span>
                )}
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default FormStepper;
