import React from "react";

const AppButton = ({
  label,
  disable = false,
  onClick,
  css,
  type = "button",
  variant = "primary", // 'primary' for Save, 'secondary' for Cancel
}) => {
  const baseClasses = "py-[5px] px-6 rounded-md  ";
  const variantClasses =
    variant === "primary"
      ? `${
          disable
            ? "bg-[#94A3B8] text-white"
            : "bg-[#3E9900] hover:bg-green-700 text-white cursor-pointer"
        }`
      : "border border-gray-400 text-gray-600 hover:bg-gray-100 cursor-pointer ";

  return (
    <button
      type={type}
      disabled={disable}
      onClick={onClick}
      className={css ? css : `${baseClasses} ${variantClasses}`}
    >
      {label}
    </button>
  );
};

export default AppButton;
