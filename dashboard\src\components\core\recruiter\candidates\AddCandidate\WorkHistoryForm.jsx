import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";
import PhoneInput from "react-phone-input-2";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";
import "react-phone-input-2/lib/style.css";

const WorkHistoryForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldTouched,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <InputField
        label="Most Recent Employer"
        name="workHistory.mostRecentEmployer"
        value={values.workHistory.mostRecentEmployer}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter employer name"
        errorMessage={
          touched.workHistory?.mostRecentEmployer &&
          errors.workHistory?.mostRecentEmployer
        }
      />
      <InputField
        label="Position Title"
        name="workHistory.positionTitle"
        value={values.workHistory.positionTitle}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter position title"
        errorMessage={
          touched.workHistory?.positionTitle &&
          errors.workHistory?.positionTitle
        }
      />

      <CalendarDropdownField
        label="Employment Date"
        name="workHistory.employmentDate"
        required
        maxDate={new Date()}
        errorMessage={
          touched.workHistory?.employmentDate &&
          errors.workHistory?.employmentDate
        }
      />
      <InputField
        label="Reason for Leaving"
        name="workHistory.reasonForLeaving"
        value={values.workHistory.reasonForLeaving}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter reason for leaving"
        errorMessage={
          touched.workHistory?.reasonForLeaving &&
          errors.workHistory?.reasonForLeaving
        }
      />
      <InputField
        label="Supervisor Reference Name"
        name="workHistory.supervisorReferenceName"
        value={values.workHistory.supervisorReferenceName}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter supervisor name"
        errorMessage={
          touched.workHistory?.supervisorReferenceName &&
          errors.workHistory?.supervisorReferenceName
        }
      />
      <InputField
        label="Supervisor Title"
        name="workHistory.supervisorReferenceTitle"
        value={values.workHistory.supervisorReferenceTitle}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter supervisor title"
        errorMessage={
          touched.workHistory?.supervisorReferenceTitle &&
          errors.workHistory?.supervisorReferenceTitle
        }
      />

      <div className="w-full">
        <label
          htmlFor="workHistory.supervisorReferenceContact"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Supervisor Phone Number <span className="text-red-500">*</span>
        </label>
        <PhoneInput
          country="us"
          value={values.workHistory.supervisorReferenceContact || ""}
          onChange={(value) => {
            setFieldValue("workHistory.supervisorReferenceContact", value);
          }}
          onBlur={() =>
            setFieldTouched("workHistory.supervisorReferenceContact", true)
          }
          enableSearch
          inputProps={{
            name: "workHistory.supervisorReferenceContact",
            required: true,
            autoFocus: false,
            id: "workHistory.supervisorReferenceContact",
          }}
          containerClass="w-full"
          inputClass="!h-[2.6rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          buttonClass="!h-[2.6rem] !border-r border-gray-300"
          placeholder="Enter your phone number"
        />
        {touched.workHistory?.supervisorReferenceContact &&
          errors.workHistory?.supervisorReferenceContact && (
            <div className="text-xs text-red-600 mt-1">
              {errors.workHistory.supervisorReferenceContact}
            </div>
          )}
      </div>
      <div className="hidden md:block" />
      <InputField
        label="Professional Reference 1 Name"
        name="workHistory.professionalReferenceName"
        value={values.workHistory.professionalReferenceName}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter reference name"
        errorMessage={
          touched.workHistory?.professionalReferenceName &&
          errors.workHistory?.professionalReferenceName
        }
      />
      <div className="w-full">
        <label
          htmlFor="workHistory.professionalReferenceContact1"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number <span className="text-red-500">*</span>
        </label>
        <PhoneInput
          country="us"
          value={values.workHistory.professionalReferenceContact1 || ""}
          onChange={(value) => {
            setFieldValue("workHistory.professionalReferenceContact1", value);
          }}
          onBlur={() =>
            setFieldTouched("workHistory.professionalReferenceContact1", true)
          }
          enableSearch
          inputProps={{
            name: "workHistory.professionalReferenceContact1",
            required: true,
            autoFocus: false,
            id: "workHistory.professionalReferenceContact1",
          }}
          containerClass="w-full"
          inputClass="!h-[2.6rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          buttonClass="!h-[2.6rem] !border-r border-gray-300"
          placeholder="Enter your phone number"
        />
        {touched.workHistory?.professionalReferenceContact1 &&
          errors.workHistory?.professionalReferenceContact1 && (
            <div className="text-xs text-red-600 mt-1">
              {errors.workHistory.professionalReferenceContact1}
            </div>
          )}
      </div>
      <InputField
        label="Professional Reference 2 Name"
        name="workHistory.professionalReferenceName2"
        value={values.workHistory.professionalReferenceName2}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Enter reference name"
        errorMessage={
          touched.workHistory?.professionalReferenceName2 &&
          errors.workHistory?.professionalReferenceName2
        }
      />
      <div className="w-full">
        <label
          htmlFor="workHistory.professionalReferenceContact2"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number <span className="text-red-500">*</span>
        </label>
        <PhoneInput
          country="us"
          value={values.workHistory.professionalReferenceContact2 || ""}
          onChange={(value) => {
            setFieldValue("workHistory.professionalReferenceContact2", value);
          }}
          onBlur={() =>
            setFieldTouched("workHistory.professionalReferenceContact2", true)
          }
          enableSearch
          inputProps={{
            name: "workHistory.professionalReferenceContact2",
            required: true,
            autoFocus: false,
            id: "workHistory.professionalReferenceContact2",
          }}
          containerClass="w-full"
          inputClass="!h-[2.6rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          buttonClass="!h-[2.6rem] !border-r border-gray-300"
          placeholder="Enter your phone number"
        />
        {touched.workHistory?.professionalReferenceContact2 &&
          errors.workHistory?.professionalReferenceContact2 && (
            <div className="text-xs text-red-600 mt-1">
              {errors.workHistory.professionalReferenceContact2}
            </div>
          )}
      </div>
    </form>
  );
};

export default WorkHistoryForm;
