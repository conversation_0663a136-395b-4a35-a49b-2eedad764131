import FeaturesCard from "./FeaturesCard";
import directClientIcon from "@/public/assets/icons/homepage/direct-client.svg";
import factoringSolutionsIcon from "@/public/assets/icons/homepage/factoring-solutions.svg";
import accountManagerIcon from "@/public/assets/icons/homepage/account-manager.svg";
import commissionIcon from "@/public/assets/icons/homepage/commission.svg";
import onboardingIcon from "@/public/assets/icons/homepage/onboarding.svg";
import toolsIcon from "@/public/assets/icons/homepage/tools.svg";

const FeaturesSection = () => {
  return (
    <section className="w-full flex flex-col py-24 bg-[#CEFFAD]  items-center gap-7 ">
      <h2 className=" font-bold text-5xl text-center">What Makes Us the One</h2>
      <p className=" text-center text-2xl w-[75%] mx-auto  font-light">
        Everything you need to thrive as a recruiter - from direct client access
        to streamlined support and top-tier commissions.
      </p>
      <div className="flex flex-col items-center justify-center md:flex-row  flex-wrap w-[75%]  mx-auto md:w-full gap-8">
        <div className="flex flex-col gap-8">
          <FeaturesCard
            title="Direct Job Opening Access"
            description="Build direct relationships with clients, cutting out intermediaries and maximizing your earnings."
            icon={directClientIcon}
          />
          <FeaturesCard
            size={"large"}
            title="Higher Commissions & Incentives"
            description="Enjoy unmatched commissions and exclusive incentives tailored to reward your hard work and success."
            icon={commissionIcon}
          />
        </div>
        <div className="flex flex-col gap-8">
          <FeaturesCard
            size={"large"}
            title="Advanced Tools & Resources"
            description="Access cutting-edge tools, technology, and support materials that help streamline your recruiting process and increase success."
            icon={toolsIcon}
          />
          <FeaturesCard
            title="Dedicated Account Manager"
            description="Receive personalized guidance from a dedicated account manager, ensuring seamless onboarding and client management."
            icon={accountManagerIcon}
          />
        </div>
        <div className="flex flex-col gap-8">
          <FeaturesCard
            title="Seamless Onboarding"
            description="Handle everything from candidate screening to onboarding with ease, ensuring a quick and smooth experience for both recruiters and candidates."
            icon={onboardingIcon}
          />
          <FeaturesCard
            size={"large"}
            title="Payrolls"
            description="Soon, enjoy the added benefit of payroll  to improve cash flow and accelerate your growth as a recruiter."
            icon={factoringSolutionsIcon}
          />
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
