import React, { useState, useRef, useEffect } from "react";

const MultiSelectionField = ({
  label,
  required = false,
  placeholder = "Select options",
  disable = false,
  options = [],
  selected,
  setSelected,
  errorMessage,
  css = "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm placeholder-gray-400",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef();

  const toggleDropdown = () => {
    if (!disable) setIsOpen((prev) => !prev);
  };

  const handleOptionClick = (option) => {
    if (selected.includes(option)) {
      setSelected(selected.filter((item) => item !== option));
    } else {
      setSelected([...selected, option]);
    }
  };

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="flex flex-col space-y-1 relative" ref={dropdownRef}>
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-800">*</span>}
        </label>
      )}
      <input
        className={`relative ${css} cursor-pointer bg-white ${
          disable ? "bg-gray-100 cursor-not-allowed" : ""
        }`}
        value={selected.length > 0 ? selected.join(", ") : ""}
        onClick={toggleDropdown}
        readOnly
        onFocus={toggleDropdown}
        placeholder={placeholder}
      />

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-md w-full max-h-48 overflow-y-auto z-10">
          {options.map((option) => (
            <div
              key={option}
              className={`px-4 py-2 hover:bg-blue-100 cursor-pointer flex items-center ${
                selected.includes(option) ? "bg-blue-50" : ""
              }`}
              onClick={() => handleOptionClick(option)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option)}
                readOnly
                className="mr-2"
              />
              {option}
            </div>
          ))}
        </div>
      )}
      {errorMessage}
    </div>
  );
};

export default MultiSelectionField;
