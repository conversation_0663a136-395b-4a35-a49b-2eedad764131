import React from "react";
import { useSelector } from "react-redux";
import { Pencil } from "lucide-react";

const DefaultUserIcon = () => (
  <svg
    className="w-12 h-12 text-gray-400"
    fill="none"
    stroke="currentColor"
    strokeWidth="1.5"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 20.25a8.25 8.25 0 0115 0"
    ></path>
  </svg>
);

const ProfileSummary = ({ imageUrl = "" }) => {
  const user = useSelector((state) => state.auth?.user);

  return (
    <div className="w-[15rem] h-[15rem] rounded-2xl border border-gray-200 bg-white shadow-md p-4 flex flex-col items-center justify-center gap-3">
      <div className="relative group w-24 h-24 rounded-full overflow-hidden border border-gray-300 bg-gray-100">
        {imageUrl ? (
          <img
            src={imageUrl}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <DefaultUserIcon />
          </div>
        )}
        <div className="absolute inset-0 bg-white/60 opacity-0 group-hover:opacity-100 transition flex items-center justify-center cursor-pointer">
          <Pencil className="text-gray-700 w-5 h-5" />
        </div>
      </div>

      <h2 className="text-sm font-medium text-gray-900 text-center">
        {user?.firstName?.slice(0,1)?.toUpperCase() + user?.firstName?.slice(1)?.toLowerCase()} {user?.lastName?.slice(0,1)?.toUpperCase() + user?.lastName?.slice(1)?.toLowerCase()}
      </h2>
      <p className="text-xs text-gray-500 text-center">
        {user?.email || "<EMAIL>"}
      </p>
    </div>
  );
};

export default ProfileSummary;
