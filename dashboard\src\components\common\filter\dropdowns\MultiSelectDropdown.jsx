import React, { useState, useEffect, useRef } from "react";

const MultiSelectDropdown = ({
  label,
  selectedValues,
  options,
  filterKey,
  onLocalChange,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelected, setLocalSelected] = useState(selectedValues || []);
  const [isOpen, setIsOpen] = useState(false);
  const [showCustomFields, setShowCustomFields] = useState(false);
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // Check if this is a date filter that supports custom range
  const isDateFilter = filterKey === "jobPosted" || filterKey === "submissionDate";

  // Sync with parent when selectedValues change (for clear all functionality)
  React.useEffect(() => {
    setLocalSelected(selectedValues || []);

    // Handle custom date fields for date filters
    if (isDateFilter && selectedValues && selectedValues.length > 0) {
      const customValue = selectedValues.find(val => val.startsWith("Custom:"));
      if (customValue) {
        setShowCustomFields(true);
        const dateRange = customValue.replace("Custom:", "");
        const [from, to] = dateRange.split(" to ");
        setFromDate(from || "");
        setToDate(to || "");
      } else if (selectedValues.includes("Custom")) {
        setShowCustomFields(true);
      } else {
        setShowCustomFields(false);
        setFromDate("");
        setToDate("");
      }
    } else if (isDateFilter) {
      setShowCustomFields(false);
      setFromDate("");
      setToDate("");
    }
  }, [selectedValues, isDateFilter]);

  const toggleOption = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const option = e.target.value;
    let updated;

    if (isDateFilter && option === "Custom") {
      if (localSelected.includes("Custom") || localSelected.some(val => val.startsWith("Custom:"))) {
        // Remove custom option
        updated = localSelected.filter((item) => item !== "Custom" && !item.startsWith("Custom:"));
        setShowCustomFields(false);
        setFromDate("");
        setToDate("");
      } else {
        // Add custom option and remove other options for date filters
        updated = ["Custom"];
        setShowCustomFields(true);
      }
    } else if (isDateFilter && option !== "Custom") {
      // For non-custom options in date filters, remove Custom if selected
      const filteredSelected = localSelected.filter(item => item !== "Custom" && !item.startsWith("Custom:"));

      if (filteredSelected.includes(option)) {
        updated = filteredSelected.filter((item) => item !== option);
      } else {
        updated = [...filteredSelected, option];
      }
      setShowCustomFields(false);
      setFromDate("");
      setToDate("");
    } else {
      // Regular multi-select behavior for non-date filters
      if (localSelected.includes(option)) {
        updated = localSelected.filter((item) => item !== option);
      } else {
        updated = [...localSelected, option];
      }
    }

    setLocalSelected(updated);
    onLocalChange(filterKey, updated);
    setIsOpen(true);
  };

  const handleDateChange = (type, value) => {
    if (type === "from") {
      setFromDate(value);
    } else {
      setToDate(value);
    }

    // Update the selected values with custom date range
    let updated;
    if (value && (type === "from" ? toDate : fromDate)) {
      const from = type === "from" ? value : fromDate;
      const to = type === "to" ? value : toDate;
      updated = [`Custom:${from} to ${to}`];
    } else if (value) {
      // Keep Custom selected even if only one date is filled
      updated = ["Custom"];
    } else {
      updated = ["Custom"];
    }

    setLocalSelected(updated);
    onLocalChange(filterKey, updated);
  };

  const removeTag = (tagToRemove) => {
    const updated = localSelected.filter((item) => item !== tagToRemove && !item.startsWith("Custom:"));
    setLocalSelected(updated);
    onLocalChange(filterKey, updated);

    if (isDateFilter && (tagToRemove === "Custom" || tagToRemove.startsWith("Custom:"))) {
      setShowCustomFields(false);
      setFromDate("");
      setToDate("");
    }
  };

  const filteredOptions = options.filter((opt) =>
    opt.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Format display text for the button
  const getDisplayText = () => {
    if (isDateFilter) {
      const customValue = localSelected.find(val => val.startsWith("Custom:"));
      if (customValue) {
        const dateRange = customValue.replace("Custom:", "");
        return `Custom (${dateRange})`;
      } else if (localSelected.includes("Custom")) {
        return "Custom";
      } else {
        return localSelected.join(", ");
      }
    } else {
      return localSelected.join(", ");
    }
  };

  const closeDropdown = () => {
    setIsOpen(false);
    setSearchTerm("");
  };

  const wrapperRef = useRef(null);

  // Close when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        isOpen &&
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () =>
      document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  return (
    <div className="relative" ref={wrapperRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
      >
        <span className="text-[#2E90FA] font-medium">
          {label}
          {localSelected.length > 0 ? `: ${getDisplayText()}` : ""}
        </span>
        <svg
          className={`w-4 h-4 text-[#94A3B8] transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className="absolute top-full left-0 mt-1 w-[300px] bg-white border border-gray-200 rounded-md shadow-lg z-50"
          onClick={(e) => e.stopPropagation()}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {/* Header with close button */}
          <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-700">{label}</p>
            <button
              onClick={closeDropdown}
              className="text-gray-400 hover:text-gray-600 text-lg leading-none cursor-pointer"
            >
              ×
            </button>
          </div>

          {/* Selected Tags */}
          {localSelected.length > 0 && (
            <div className="px-3 py-2 border-b border-gray-100">
              <div className="flex flex-wrap gap-1">
                {localSelected.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center gap-1 bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full"
                  >
                    {tag.startsWith("Custom:") ? `Custom (${tag.replace("Custom:", "")})` : tag}
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeTag(tag);
                      }}
                      className="text-blue-500 hover:text-blue-700 ml-1 cursor-pointer"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Checkbox/Radio List */}
          <div className="max-h-60 overflow-y-auto">
            <div className="p-3">
              {filteredOptions.map((option) => (
                <label
                  key={option}
                  className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-2 py-1.5 rounded"
                  onClick={(e) => e.stopPropagation()}
                >
                  <input
                    type="checkbox"
                    value={option}
                    checked={
                      option === "Custom" && isDateFilter
                        ? (localSelected.includes("Custom") || localSelected.some(val => val.startsWith("Custom:")))
                        : localSelected.includes(option)
                    }
                    onChange={toggleOption}
                    onClick={(e) => e.stopPropagation()}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span>{option}</span>
                </label>
              ))}
            </div>
            {filteredOptions.length === 0 && (
              <div className="p-3 text-center text-gray-500 text-sm">
                No options found
              </div>
            )}

            {/* Custom Date Fields - only show for date filters when Custom is selected */}
            {isDateFilter && showCustomFields && (
              <div className="px-3 pb-3 border-t border-gray-100">
                <div className="mt-3 space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      From <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="date"
                        value={fromDate}
                        onChange={(e) => handleDateChange("from", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="DD:MM:YYYY"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      To <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="date"
                        value={toDate}
                        onChange={(e) => handleDateChange("to", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        placeholder="DD:MM:YYYY"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
