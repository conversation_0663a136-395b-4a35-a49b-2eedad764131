const mongoose = require("mongoose");
const { Candidates } = require("../models/Candidate.models");
const { createID } = require("../utils");
const { uploadToS3 } = require("../configs/s3Config");
const job = require("../models/Job.models");
const {
  candidateSubmission,
  submissionEnums,
} = require("../models/CandidateSubmission.models");
const { workOnRequest } = require("../models/WorkOnRequest.models");
const recruiterProfile = require("../models/RecruiterProfile.models");
const CoinTransaction = require("../models/CoinTransaction");
const UserCoinBalance = require("../models/UserCoinBalance");
const { submissionLog } = require("../models/submissionLog.models");
const { createNotification } = require("../services/notificationService");

// put in utils functions-------
// Helper function to create consistent experience search conditions
function createExperienceSearchConditions(searchTerm, useRangeLogic = true) {
  const expRange = searchTerm.match(/(\d+)\s*[-to]+\s*(\d+)/i);
  const expNum = searchTerm.match(/(\d+)/);

  if (useRangeLogic) {
    // Use same logic as main filtering (range overlap)
    if (expRange) {
      const min = parseInt(expRange[1], 10);
      const max = parseInt(expRange[2], 10);
      if (!isNaN(min) && !isNaN(max)) {
        return {
          $and: [
            { "submission.job.experience.min": { $exists: true, $ne: null } },
            { "submission.job.experience.max": { $exists: true, $ne: null } },
            {
              $expr: {
                $and: [
                  { $lte: [{ $toInt: "$submission.job.experience.min" }, max] },
                  { $gte: [{ $toInt: "$submission.job.experience.max" }, min] },
                ],
              },
            },
          ],
        };
      }
    } else if (expNum) {
      const num = parseInt(expNum[1], 10);
      if (!isNaN(num)) {
        return {
          $and: [
            { "submission.job.experience.min": { $exists: true, $ne: null } },
            { "submission.job.experience.max": { $exists: true, $ne: null } },
            {
              $expr: {
                $and: [
                  { $lte: [{ $toInt: "$submission.job.experience.min" }, num] },
                  { $gte: [{ $toInt: "$submission.job.experience.max" }, num] },
                ],
              },
            },
          ],
        };
      }
    }
  } else {
    // Use exact match logic (for backward compatibility)
    if (expRange) {
      const min = parseInt(expRange[1], 10);
      const max = parseInt(expRange[2], 10);
      if (!isNaN(min) && !isNaN(max)) {
        return {
          $and: [
            { "submission.job.experience.min": { $eq: min } },
            { "submission.job.experience.max": { $eq: max } },
          ],
        };
      }
    } else if (expNum) {
      const num = parseInt(expNum[1], 10);
      if (!isNaN(num)) {
        return {
          $or: [
            { "submission.job.experience.min": num },
            { "submission.job.experience.max": num },
          ],
        };
      }
    }
  }

  return null;
}

// Helper function to get base candidate search conditions
function getBaseCandidateSearchConditions(searchRegex) {
  return [
    { "personalDetails.firstName": searchRegex },
    { "personalDetails.lastName": searchRegex },
    { "personalDetails.emailAddress": searchRegex },
    {
      $expr: {
        $regexMatch: {
          input: {
            $concat: [
              { $ifNull: ["$personalDetails.firstName", ""] },
              " ",
              { $ifNull: ["$personalDetails.lastName", ""] },
            ],
          },
          regex: searchRegex,
        },
      },
    },
  ];
}

// Helper function to get location search conditions
function getLocationSearchConditions(searchRegex) {
  return [
    { "personalDetails.city": searchRegex },
    { "personalDetails.state": searchRegex },
    { "personalDetails.country": searchRegex },
    { "submission.job.location.city": searchRegex },
    { "submission.job.location.state": searchRegex },
    { "submission.job.location.country": searchRegex },
  ];
}

// Helper function to get job-related search conditions
function getJobSearchConditions(searchRegex) {
  return [
    { "submission.job.jobTitle": searchRegex },
    { "submission.job.jobId": searchRegex },
    { "submission.job.jobType": searchRegex },
  ];
}

//-------------------------------------------------------

//* @Desc get single condidate
//* @Route GET /api/v1/condidate/getcondidate/:candidateID
//* @Access private
const getCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const { candidateID } = req.params;
    const { submissionID } = req.query;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }
    let query = {};
    if (user.userType === "recruiter") {
      query = {
        "createdBy.userID": user.userId,
        candidateID: candidateID,
      };
    }

    if (user.userType === "headAccountManager") {
      query = {
        candidateID: candidateID,
      };
    }

    if (user.userType === "accountManager") {
      query = {
        candidateID: candidateID,
      };
    }
    const submissions = await candidateSubmission.findOne({
      submissionId: submissionID,
    });

    const candidateData = await Candidates.find(query);
    res
      .status(200)
      .json({ success: true, data: { ...candidateData, submissions } });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching candidate",
      error: err.message,
    });
  }
};

const getCandidatesByJobAndRecruiter = async (req, res) => {
  try {
    const { jobId, recruiterId } = req.params;
    if (!jobId || !recruiterId) {
      return res.status(400).json({
        success: false,
        message: "Job ID and Recruiter ID are required.",
      });
    }

    const candidates = await candidateSubmission.aggregate([
      {
        $match: {
          "job.jobID": jobId,
          "submittedBy.userID": recruiterId,
        },
      },
      {
        $lookup: {
          from: "candidates",
          localField: "candidate.candidateID",
          foreignField: "candidateID",
          as: "candidateDetails",
        },
      },
      { $unwind: "$candidateDetails" },
      {
        $project: {
          _id: 0,
          candidateID: "$candidateDetails.candidateID",
          firstName: "$candidateDetails.personalDetails.firstName",
          lastName: "$candidateDetails.personalDetails.lastName",
        },
      },
    ]);

    const formatted = candidates.map((cand) => ({
      label: `${cand.candidateID} - ${cand.firstName} ${cand.lastName}`.trim(),
      value: cand.candidateID,
    }));

    res.status(200).json({ success: true, data: formatted });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

//* @Desc get all condidate in basis of hr
//* @Route GET /api/v1/condidate/getallcandidate
//* @Access private
const getAllCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const condidateData = await Candidates.find({
      "createdBy.userID": user.userId,
      "isDeleted.flag": { $exists: false },
    });

    res.status(200).json({ success: true, data: condidateData });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching candidate",
      error: err.message,
    });
  }
};

//* @Desc create condidate
//* @Route POST /api/v1/condidate/addcondidate/personalDetails
//* @Access private
const AddCandidate = async (req, res) => {
  try {
    //User authentication check
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      phoneCountryCode,
      emailAddress,
      currentAddress,
      country,
      city,
      state,
      zipcode,
      relocationWillingness = false,
      workAuthorizationStatus,
      ssnLast4Digit,
      availableStartDate,
    } = req.body;
    const errors = [];

    //field validation
    if (!firstName) errors.push("First name is required");
    if (!lastName) errors.push("Last name is required");
    if (!phoneNumber) errors.push("Valid 10-digit phone number is required");
    if (!phoneCountryCode) errors.push("country code is required");
    if (!emailAddress || !/\S+@\S+\.\S+/.test(emailAddress))
      errors.push("Valid email address is required");
    if (!currentAddress) errors.push("Current address is required");
    if (!country) errors.push("Country is required.");
    if (!city) errors.push("City is required");
    if (!state) errors.push("State is required");
    if (!zipcode) errors.push("Zipcode is required");
    if (!workAuthorizationStatus)
      errors.push("Work authorization status is required");
    // if (ssnLast4Digit && !/^\d{4}$/.test(ssnLast4Digit))
    //   errors.push("SSN last 4 digits must be 4 numbers");

    //Return if validation failed
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors,
      });
    }

    const countCandidate = await Candidates.countDocuments();

    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

    //Check for uniqueness (email and phone number)
    const existingCandidate = await Candidates.findOne({
      $and: [
        {
          $or: [
            { "personalDetails.emailAddress": emailAddress },
            { "personalDetails.phoneNumber": phoneNumber },
          ],
        },
        {
          "isDeleted.flag": { $ne: true },
        },
        {
          createdAt: { $gte: ninetyDaysAgo },
        },
      ],
    });

    if (existingCandidate) {
      return res.status(409).json({
        success: false,
        message: "Candidate with the same email or phone number already exists",
      });
    }

    //Create and save candidate
    const newCandidate = new Candidates({
      ...{
        candidateID: `HC${new Date().getFullYear()}${createID(
          countCandidate,
          1
        )}`,
        personalDetails: {
          firstName,
          lastName,
          phoneNumber,
          emailAddress,
          currentAddress,
          phoneCountryCode,
          city,
          country,
          state,
          zipcode,
          relocationWillingness,
          workAuthorizationStatus,
          ssnLast4Digit,
          availableStartDate,
        },
      },
      ...{ createdBy: { _id: user._id, userID: user.userId } },
    });
    const candidateRegister = await newCandidate.save();

    return res.status(201).json({
      success: true,
      message: "Candidate added successfully",
      data: candidateRegister,
    });
  } catch (err) {
    console.error("Error in AddCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while adding candidate",
      error: err.message,
    });
  }
};

//* @Desc add licensing
//* @Route PATCH /api/v1/condidate/addcondidate/licensing/:candidateID
//* @Access private
const Licensing = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      stateLicense,
      stateLicenseExpiration,
      isCompactLicense = false,
    } = req.body;

    if (!stateLicense) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "licensing.stateLicenses": stateLicense,
          "licensing.licenseExpireDate": stateLicenseExpiration
            ? stateLicenseExpiration
            : undefined,
          "licensing.compactLicense": isCompactLicense,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with licensing.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate licensing added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Certification
//* @Route PATCH /api/v1/condidate/addcondidate/certification/:candidateID
//* @Access private
const Certification = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      blsCertification,
      blsExpiration,
      aclsPalsNals,
      aclsPalsNalsExpiration,
      otherRelevantCertificate = "",
    } = req.body;

    // if (
    //   !(
    //     aclsPalsNalsExpiration ||  &&
    //     aclsPalsNals
    //   )
    // ) {
    //   return res
    //     .status(400)
    //     .json({ success: false, message: "All field is required." });
    // }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "certification.blsCertification": blsCertification,
          "certification.blsExpiration": blsExpiration
            ? blsExpiration
            : undefined,
          "certification.aclsPalsNals": aclsPalsNals,
          "certification.aclsPalsNalsExpiration": aclsPalsNalsExpiration
            ? aclsPalsNalsExpiration
            : undefined,
          "certification.otherRelevantCertificate": otherRelevantCertificate,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with certification.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate certification added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add education
//* @Route PATCH /api/v1/condidate/addcondidate/education/:candidateID
//* @Access private
const Education = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const education = req.body;

    if (education.length < 1) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const isFieldValid = education.filter(
      (item) => !(item.degree && item.collegeName)
    );

    if (isFieldValid.length > 0) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const candidate = await Candidates.findOne({ candidateID: candidateID });
    if (!candidate) {
      return res
        .status(404)
        .json({ success: false, message: "Candidate not found." });
    }

    const existingEducation = candidate.education || [];

    // Remove duplicates: assuming a duplicate means all 3 fields match
    const uniqueNewEducation = education.filter((newEntry) => {
      return !existingEducation.some(
        (existingEntry) =>
          existingEntry.degree === newEntry.degree &&
          existingEntry.collegeName === newEntry.collegeName
      );
    });

    // Merge existing and new entries
    const updatedEducation = [...existingEducation, ...uniqueNewEducation];

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      { $set: { education: updatedEducation } },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Education.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Education added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Work History
//* @Route PATCH /api/v1/condidate/addcondidate/workhistory/:candidateID
//* @Access private
const WorkHistory = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      mostRecentEmployer,
      positionTitle,
      employmentDate,
      reasonForLeaving,
      supervisorReferenceName,
      supervisorReferenceTitle,
      supervisorReferenceContact,
      professionalReferenceName,
      professionalReferenceName2,
      professionalReferenceContact1,
      professionalReferenceContact2,
      professionalReferenceContact3,
    } = req.body;

    // add validation
    if (
      !mostRecentEmployer ||
      !positionTitle ||
      !employmentDate ||
      !reasonForLeaving ||
      !supervisorReferenceName ||
      !supervisorReferenceTitle ||
      !supervisorReferenceContact ||
      !professionalReferenceName ||
      !professionalReferenceContact1
    ) {
      return res.status(400).json({
        success: false,
        message: "All required fields must be provided.",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        workHistory: {
          mostRecentEmployer,
          positionTitle,
          employmentDate,
          reasonForLeaving,
          supervisorReferenceName,
          supervisorReferenceTitle,
          supervisorReferenceContact,
          professionalReferenceName,
          professionalReferenceName2,
          professionalReferenceContact1,
          professionalReferenceContact2,
          professionalReferenceContact3,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Work History.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Work History added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add skills And Experience
//* @Route PATCH /api/v1/condidate/addcondidate/skillsandexperience/:candidateID
//* @Access private
const skillsAndExperience = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const { totalYearsOfExperience, relevantExperience, otherSkills } =
      req.body;

    if (!(totalYearsOfExperience && relevantExperience)) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "skillsAndExperience.totalYearsOfExperience": {
            month: totalYearsOfExperience?.month || 0,
            year: totalYearsOfExperience?.year || 0,
          },
          "skillsAndExperience.relevantExperience": {
            month: relevantExperience?.month || 0,
            year: relevantExperience?.year || 0,
          },
          "skillsAndExperience.otherSkills": otherSkills
            ? otherSkills
                ?.split(",")
                ?.filter((item) => item)
                ?.map((item) => item?.trim())
            : [],
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with skills And Experience.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate skills And Experience added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Health And Compliance
//* @Route PATCH /api/v1/condidate/addcondidate/healthandcompliance/:candidateID
//* @Access private
const HealthAndCompliance = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      covid19Status,
      dateOfLastCovid19Dose,
      boosterReceived,
      proofOfVaccinationAvailable,
      fluVaccination,
    } = req.body;

    if (!(covid19Status && boosterReceived && fluVaccination)) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "healthAndCompliance.covid19Status":
            covid19Status == "none" || covid19Status == "exempt" ? false : true,
          "healthAndCompliance.dateOfLastCovid19Dose": dateOfLastCovid19Dose,
          "healthAndCompliance.boosterReceived":
            boosterReceived == "yes" ? true : false,
          "healthAndCompliance.proofOfVaccinationAvailable":
            proofOfVaccinationAvailable == "yes" ? true : false,
          "healthAndCompliance.fluVaccination":
            fluVaccination == "yes" ? true : false,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Health And Compliance.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Health And Compliance added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Submission Details
//* @Route PATCH /api/v1/condidate/addcondidate/submissiondetails/:candidateID
//* @Access private
const SubmissionDetails = async (req, res) => {
  try {
    const { candidateID } = req.params;

    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      rateExpectation,
      referenceProvider,
      candidateAvailabilityForInterview,
      additionalNote,
    } = req.body;

    if (
      !(
        rateExpectation &&
        ["Hourly", "Salary"].includes(rateExpectation) &&
        referenceProvider &&
        ["Yes", "No"].includes(referenceProvider) &&
        candidateAvailabilityForInterview
      )
    ) {
      return res
        .status(400)
        .json({ success: false, message: "All field is required." });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "submissionDetails.rateExpectation": rateExpectation,
          "submissionDetails.referenceProvider": referenceProvider,
          "submissionDetails.candidateAvailabilityForInterview":
            candidateAvailabilityForInterview,
          "submissionDetails.additionalNote": additionalNote
            ? additionalNote
            : "",
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Submission Details.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Submission Details added successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc add Document Attachment
//* @Route PATCH /api/v1/condidate/addcondidate/documentattachment/:candidateID
//* @Access private
const DocumentAttachment = async (req, res) => {
  try {
    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const file = req.files;

    //Both resume and cover letter must be present — either in the database or in the uploaded files. If either one is missing from both, return an error.
    const candidate = await Candidates.findOne({ candidateID: candidateID });

    const hasResumeInDB = !!candidate?.documentAttachments?.resume;
    const hasCoverLetterInDB = !!candidate?.documentAttachments?.coverLetter;

    const hasResumeInForm = file?.some((f) => f.fieldname === "resume");
    const hasCoverLetterInForm = file?.some(
      (f) => f.fieldname === "coverLetter"
    );

    const hasResume = hasResumeInDB || hasResumeInForm;
    const hasCoverLetter = hasCoverLetterInDB || hasCoverLetterInForm;

    if (!hasResume && !hasCoverLetter) {
      return res.status(400).json({
        success: false,
        message: "Resume and cover letter are required.",
      });
    }

    if (!hasResume) {
      return res
        .status(400)
        .json({ success: false, message: "Resume is required." });
    }

    if (!hasCoverLetter) {
      return res
        .status(400)
        .json({ success: false, message: "Cover letter is required." });
    }

    // file upload to s3
    const s3Urls = {};
    try {
      async function proccess(index) {
        if (index == file.length) {
          return;
        }
        let fileData = file[index];

        let uploadUrl = await uploadToS3(
          fileData.buffer,
          fileData.originalname,
          fileData.mimetype,
          "candidate"
        );
        s3Urls[fileData.fieldname] = uploadUrl;
        await proccess(index + 1);
      }
      await proccess(0);
    } catch (error) {
      console.error("Error uploading file:", uploadError);
      return res.status(400).json({
        success: false,
        message: "Failed to upload candidate document",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      { $set: { documentAttachments: s3Urls } },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update with Education.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate Document upload successfully",
      data: updatedCandidate,
    });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc update condidate
//* @Route PATCH /api/v1/condidate/updatecandidate/personaldetails/:candidateID
//* @Access private
const updateCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      emailAddress,
      currentAddress,
      city,
      state,
      country,
      phoneCountryCode,
      zipcode,
      relocationWillingness = false,
      workAuthorizationStatus,
      ssnLast4Digit,
      availableStartDate,
    } = req.body;
    const errors = [];

    //field validation
    if (!firstName) errors.push("First name is required");
    if (!lastName) errors.push("Last name is required");
    if (!phoneNumber) errors.push("Valid 10-digit phone number is required");

    if (!phoneCountryCode) errors.push("phone country code is required");
    if (!country) errors.push("Country is required.");

    if (!emailAddress || !/\S+@\S+\.\S+/.test(emailAddress))
      errors.push("Valid email address is required");
    if (!currentAddress) errors.push("Current address is required");
    if (!city) errors.push("City is required");
    if (!state) errors.push("State is required");
    if (!zipcode) errors.push("Zipcode is required");
    if (!workAuthorizationStatus)
      errors.push("Work authorization status is required");
    // if (ssnLast4Digit && !/^\d{4}$/.test(ssnLast4Digit))
    //   errors.push("SSN last 4 digits must be 4 numbers");

    //Return if validation failed
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors,
      });
    }

    //Check for uniqueness (email and phone number)
    const existingCandidate = await Candidates.findOne({
      "createdBy.userID": user.userId,
      candidateID: candidateID,
    });

    if (!existingCandidate) {
      return res.status(400).json({
        success: false,
        message: "Recruiter with this candidate does not exist.",
      });
    }

    const updatedCandidate = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "personalDetails.firstName": firstName,
          "personalDetails.lastName": lastName,
          "personalDetails.phoneNumber": phoneNumber,
          "personalDetails.emailAddress": emailAddress,
          "personalDetails.currentAddress": currentAddress,
          "personalDetails.phoneCountryCode": phoneCountryCode,
          "personalDetails.country": country,
          "personalDetails.city": city,
          "personalDetails.state": state,
          "personalDetails.zipcode": zipcode,
          "personalDetails.relocationWillingness": relocationWillingness,
          "personalDetails.workAuthorizationStatus": workAuthorizationStatus,
          "personalDetails.ssnLast4Digit": ssnLast4Digit,
          "personalDetails.availableStartDate": availableStartDate,
        },
      },
      { new: true }
    );

    if (!updatedCandidate) {
      return res.status(400).json({
        success: false,
        message: "No candidate update.",
      });
    }

    return res.status(200).json({
      success: true,
      message: "Candidate profile is update successfully",
      data: updatedCandidate,
    });
  } catch (err) {
    console.error("Error updating candidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while updating candidate",
      error: err.message,
    });
  }
};

//* @Desc remove single condidate
//* @Route DELETE /api/v1/condidate/removecondidate/:candidateID
//* @Access private
const removeCandidate = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { candidateID } = req.params;
    if (!candidateID) {
      return res
        .status(400)
        .json({ success: false, message: "candidate ID is required." });
    }

    const candidateData = await Candidates.findOneAndUpdate(
      { candidateID: candidateID },
      {
        $set: {
          "isDeleted.flag": true,
          "isDeleted._id": user._id,
          "isDeleted.userId": user.userId,
        },
      },
      { new: true }
    );

    return res.status(200).json({
      success: true,
      message: "Candidate marked as deleted",
      data: candidateData,
    });
  } catch (err) {
    console.error("Error in removeCandidate:", err);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while deleting candidate",
      error: err.message,
    });
  }
};

//* @Desc submit candidate for jobs
//* @Route POST /api/v1/condidate/candidateJobSubmissions
//* @Access private
const candidateJobSubmissions = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobID, candidateID } = req.body;

    if (!(jobID && candidateID)) {
      return res.status(400).json({
        success: false,
        message: "All filed are requires",
      });
    }

    const jobDetails = await job.findOne({ jobId: jobID });
    if (!jobDetails) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    const isWorkRequest = await recruiterProfile.findOne({
      "user.userId": user.userId,
      jobsWorkingOn: {
        $elemMatch: {
          jobId: new mongoose.Types.ObjectId(jobDetails?._id),
          isActive: true,
          status: "assigned",
        },
      },
    });

    if (!isWorkRequest) {
      return res.status(400).json({
        success: false,
        message:
          "You do not have permission to submit a candidate for this job. Please start working on the job first to gain access.",
      });
    }

    const workRequest = await candidateSubmission.findOne({
      "job.jobID": jobID,
      "candidate.candidateID": candidateID,
    });

    if (workRequest) {
      return res.status(400).json({
        success: false,
        message: "candidate is already submitted on this request.",
      });
    }

    const candidiateDetails = await Candidates.findOne({
      candidateID: candidateID,
    });
    if (!candidiateDetails) {
      return res.status(400).json({
        success: false,
        message: "candidate not found",
      });
    }

    const CandidateSubmissionIntance = new candidateSubmission({
      job: {
        _id: jobDetails._id,
        jobID: jobDetails.jobId,
      },
      submittedBy: {
        _id: user._id,
        userID: user.userId,
      },
      candidate: {
        _id: candidiateDetails._id,
        candidateID: candidiateDetails.candidateID,
      },
    });
    const newCandidateSubmission = await CandidateSubmissionIntance.save();

    let eventDate = new Date();
    eventDate.setHours(0, 0, 0, 0);

    // add logs that status will be updated
    const submissionEventLog = new submissionLog({
      status: "submitted",
      submittedBy: user._id,
      submissionID: newCandidateSubmission._id,
      eventDate: eventDate,
    });

    await submissionEventLog.save();

    // create notifications for account manager from recruiter
    await createNotification({
      recipients: [jobDetails?.accountManager?._id],
      type: "submission",
      relatedJobId: [jobID],
      relatedCandidateId: [newCandidateSubmission.submissionId],
    });

    return res.status(200).json({
      success: true,
      message: "candidate mapped with job",
      data: newCandidateSubmission,
    });
  } catch (error) {
    console.error("Error in candidate submission:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate submission",
      error: error.message,
    });
  }
};

//* @Desc get candidate submissions with there status like hired,rejected,active & all submissions
//* @Route GET /api/v1/condidate/getSubmissions
//* @Access private, recruiter
const getSubmissions = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const {
      submissionType = "any",
      location: locationParam,
      status: statusParam,
      experienceLevel: experienceLevelParam,
      jobType: jobTypeParam,
      searchTerm,
      sortBy = "newest",
    } = req.query;

    // Parse comma-separated arrays back to arrays
    const location = locationParam
      ? locationParam.split(",").map((item) => item.trim())
      : undefined;
    const status = statusParam
      ? statusParam.split(",").map((item) => item.trim())
      : undefined;

    // Parse new filter parameters for hired candidates and all submissions
    const jobType = jobTypeParam
      ? jobTypeParam.split(",").map((item) => item.trim())
      : undefined;
    const experienceLevel = experienceLevelParam
      ? experienceLevelParam.split(",").map((item) => item.trim())
      : undefined;

    let query = {};
    let additionalMatchStages = [];
    let postLookupMatchStages = [];

    // REFACTORED STATUS FILTERING SYSTEM
    // This section implements a unified, maintainable status filtering system that:

    //
    // Status Filtering Architecture:
    // - Base Status Filter: Applied in $lookup pipeline (query object)
    // - User Status Filter: Applied in post-lookup stages (postLookupMatchStages)
    // - Status Search: Integrated with unified status validation

    const TAB_STATUS_CONFIG = {
      activeSubmission: {
        excluded: [
          "rejected",
          "offer rejected",
          "guarantee period not completed",
          "guarantee period completed",
          "Talent Pool",
        ],
        type: "exclude",
      },
      hired: {
        included: ["offer accepted", "hired-under guarantee period"],
        type: "include",
      },
      rejected: {
        included: [
          "rejected",
          "offer rejected",
          "guarantee period not completed",
        ],
        type: "include",
      },
      any: {
        included: [...submissionEnums, "Talent Pool"], // All statuses allowed
        type: "include",
      },
    };

    // Helper function to get allowed statuses for a tab
    function getTabAllowedStatuses(submissionType) {
      const config = TAB_STATUS_CONFIG[submissionType];
      if (!config) return submissionEnums;

      if (config.type === "exclude") {
        return submissionEnums.filter((s) => !config.excluded.includes(s));
      } else {
        return config.included;
      }
    }

    // Base status filtering by tab type
    const statusConfig = TAB_STATUS_CONFIG[submissionType];
    if (statusConfig && submissionType !== "any") {
      if (statusConfig.type === "exclude") {
        query = {
          status: { $nin: statusConfig.excluded },
        };
      } else if (statusConfig.type === "include") {
        query = {
          status: { $in: statusConfig.included },
        };
      }
    }

    // Universal filtering logic for all tabs
    // Location filtering - Filter by both candidate location AND job location
    if (location && location.length > 0) {
      const locations = Array.isArray(location) ? location : [location];
      const locationRegexPatterns = locations.map(
        (loc) => new RegExp(loc, "i")
      );

      // Apply candidate location filtering at pre-lookup stage
      additionalMatchStages.push({
        $match: {
          $or: [
            { "personalDetails.city": { $in: locationRegexPatterns } },
            { "personalDetails.state": { $in: locationRegexPatterns } },
            { "personalDetails.country": { $in: locationRegexPatterns } },
          ],
        },
      });

      // Apply job location filtering at post-lookup stage (after job data is available)
      postLookupMatchStages.push({
        $match: {
          $or: [
            // Candidate location (redundant but ensures consistency)
            { "personalDetails.city": { $in: locationRegexPatterns } },
            { "personalDetails.state": { $in: locationRegexPatterns } },
            { "personalDetails.country": { $in: locationRegexPatterns } },
            // Job location (new addition)
            { "submission.job.location.city": { $in: locationRegexPatterns } },
            { "submission.job.location.state": { $in: locationRegexPatterns } },
            {
              "submission.job.location.country": { $in: locationRegexPatterns },
            },
          ],
        },
      });
    }

    // User-selected status filtering - Unified approach using centralized configuration
    // Skip status filtering completely for hired candidates tab
    if (status && status.length > 0 && submissionType !== "hired") {
      const statuses = Array.isArray(status) ? status : [status];

      // Get allowed statuses for current tab using centralized configuration
      const allowedStatuses = getTabAllowedStatuses(submissionType);

      // Pre-convert to lowercase for performance (done once instead of multiple times)
      const allowedStatusesLower = allowedStatuses.map((s) => s.toLowerCase());

      // Intersect user-selected statuses with tab restrictions
      const finalStatuses = statuses.filter((userStatus) =>
        allowedStatusesLower.includes(userStatus.toLowerCase())
      );

      // Only apply status filtering if we have valid statuses after intersection
      if (finalStatuses.length > 0) {
        // Pre-convert final statuses to lowercase for performance
        const finalStatusesLower = finalStatuses.map((s) => s.toLowerCase());

        postLookupMatchStages.push({
          $match: {
            $and: [
              { submission: { $exists: true, $ne: null } },
              {
                $expr: {
                  $in: [{ $toLower: "$submission.status" }, finalStatusesLower],
                },
              },
            ],
          },
        });
      }
    }

    // Job Type filtering - Using regex patterns for "full-time" and "contract" job types
    if (jobType && jobType.length > 0) {
      const jobTypes = Array.isArray(jobType) ? jobType : [jobType];

      // Create regex patterns for the two supported job types
      const jobTypeRegexPatterns = jobTypes.map((jt) => {
        const normalizedType = jt.toLowerCase().trim();

        if (normalizedType === "full-time") {
          // Matches: "full-time", "Full Time", "full time", "fulltime", etc.
          return /^full[\s-]*time$/i;
        } else if (normalizedType === "contract") {
          // Matches: "contract", "Contract Base", "contract base", "contractbase", etc.
          return /^contract(\s+base)?$/i;
        } else {
          // Fallback: exact match with flexible spacing/hyphens
          const escaped = jt.replace(/[-\s]/g, "[\\s-]*");
          return new RegExp(`^${escaped}$`, "i");
        }
      });

      postLookupMatchStages.push({
        $match: {
          $and: [
            { submission: { $exists: true, $ne: null } },
            { "submission.job.jobType": { $in: jobTypeRegexPatterns } },
          ],
        },
      });
    }

    // Experience Level filtering - Filter based on job experience requirements
    if (experienceLevel && experienceLevel.length > 0) {
      const experienceConditions = [];

      experienceLevel.forEach((expRange) => {
        // Parse experience range like "3-5" or "5-8"
        const rangeParts = expRange.split("-");
        if (rangeParts.length === 2) {
          const minExp = parseInt(rangeParts[0]);
          const maxExp = parseInt(rangeParts[1]);

          if (!isNaN(minExp) && !isNaN(maxExp)) {
            // Add condition for this experience range based on job's experience requirements
            experienceConditions.push({
              $or: [
                // Case 1: Job experience range overlaps with user filter range
                {
                  $and: [
                    {
                      "submission.job.experience.min": {
                        $exists: true,
                        $ne: null,
                      },
                    },
                    {
                      "submission.job.experience.max": {
                        $exists: true,
                        $ne: null,
                      },
                    },
                    {
                      $expr: {
                        $and: [
                          {
                            $lte: [
                              { $toInt: "$submission.job.experience.min" },
                              maxExp,
                            ],
                          },
                          {
                            $gte: [
                              { $toInt: "$submission.job.experience.max" },
                              minExp,
                            ],
                          },
                        ],
                      },
                    },
                  ],
                },
                // Case 2: Job has single experience value that falls within range
                {
                  $and: [
                    {
                      "submission.job.experience.min": {
                        $exists: true,
                        $ne: null,
                      },
                    },
                    { "submission.job.experience.max": { $exists: false } },
                    {
                      $expr: {
                        $and: [
                          {
                            $gte: [
                              { $toInt: "$submission.job.experience.min" },
                              minExp,
                            ],
                          },
                          {
                            $lte: [
                              { $toInt: "$submission.job.experience.min" },
                              maxExp,
                            ],
                          },
                        ],
                      },
                    },
                  ],
                },
                // Case 3: Handle null/undefined max experience (treat as single value)
                {
                  $and: [
                    {
                      "submission.job.experience.min": {
                        $exists: true,
                        $ne: null,
                      },
                    },
                    {
                      "submission.job.experience.max": {
                        $in: [null, undefined],
                      },
                    },
                    {
                      $expr: {
                        $and: [
                          {
                            $gte: [
                              { $toInt: "$submission.job.experience.min" },
                              minExp,
                            ],
                          },
                          {
                            $lte: [
                              { $toInt: "$submission.job.experience.min" },
                              maxExp,
                            ],
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            });
          }
        }
      });

      // Apply experience filtering if we have valid conditions
      if (experienceConditions.length > 0) {
        postLookupMatchStages.push({
          $match: {
            $and: [
              { submission: { $exists: true, $ne: null } },
              { "submission.job": { $exists: true, $ne: null } },
              { "submission.job.experience": { $exists: true, $ne: null } },
              {
                $or: experienceConditions, // Match any of the experience ranges
              },
            ],
          },
        });
      }
    }

    // Search functionality
    const searchField = req.query.searchField;
    if (searchTerm && searchTerm.trim()) {
      const searchRegex = new RegExp(searchTerm.trim(), "i");
      let searchOr = [];
      // Fields that can be searched before lookup
      const topLevelFields = ["all", "candidateName", "email", "Phonenumber"];
      // Fields that require post-lookup
      const postLookupFields = [
        "JobId",
        "jobTitle",
        "location",
        "status",
        "JobType",
        "Experience",
        "submissionDate",
      ];
      if (
        !searchField ||
        searchField === "all" ||
        searchField === "candidateName" ||
        searchField === "email" ||
        searchField === "Phonenumber"
      ) {
        // Top-level fields
        if (!searchField || searchField === "all") {
          // Handle "All Fields" search with tab-specific fields

          // Start with base candidate fields for "All Fields" search
          let allSearchConditions = [
            ...getBaseCandidateSearchConditions(searchRegex),
            ...getJobSearchConditions(searchRegex),
            ...getLocationSearchConditions(searchRegex),
            // Common fields across tabs (exclude status for hired candidates)
            { "personalDetails.phoneNumber": searchRegex },
            { "personalDetails.mobileNumber": searchRegex },
          ];

          // Add status search only for tabs that support status filtering
          if (submissionType !== "hired") {
            allSearchConditions.push({ "submission.status": searchRegex });
          }

          // Add tab-specific post-lookup fields
          if (submissionType === "activeSubmission") {
            allSearchConditions.push({ "submission.submittedAt": searchRegex });
          } else if (submissionType === "hired") {
            // Add job experience search for 'hired' tab using helper function
            const jobExpCondition = createExperienceSearchConditions(
              searchTerm,
              false
            );
            if (jobExpCondition) {
              allSearchConditions.push(jobExpCondition);
            }
          } else if (submissionType === "any") {
            // All Submissions tab: add job experience search using helper function
            const jobExpCondition = createExperienceSearchConditions(
              searchTerm,
              true
            );
            if (jobExpCondition) {
              allSearchConditions.push(jobExpCondition);
            }
            // No additional fields needed - all covered in base conditions
          }

          // Add the combined search as post-lookup match
          if (allSearchConditions.length > 0) {
            postLookupMatchStages.push({
              $match: { $or: allSearchConditions },
            });
          }

          // Skip individual field search logic for "All Fields"
          searchOr = [];
        } else if (searchField === "candidateName") {
          searchOr = [
            { "personalDetails.firstName": searchRegex },
            { "personalDetails.lastName": searchRegex },
            {
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      "$personalDetails.firstName",
                      " ",
                      "$personalDetails.lastName",
                    ],
                  },
                  regex: searchRegex,
                },
              },
            },
          ];
        } else if (searchField === "email") {
          searchOr = [{ "personalDetails.emailAddress": searchRegex }];
        } else if (searchField === "Phonenumber") {
          searchOr = [{ "personalDetails.phoneNumber": searchRegex }];
        }
        if (searchOr.length > 0) {
          additionalMatchStages.push({ $match: { $or: searchOr } });
        }
      } else if (postLookupFields.includes(searchField)) {
        // Post-lookup fields
        if (searchField === "JobId") {
          searchOr = [{ "submission.job.jobId": searchRegex }];
        } else if (searchField === "jobTitle") {
          searchOr = [{ "submission.job.jobTitle": searchRegex }];
        } else if (searchField === "location") {
          searchOr = [
            { "personalDetails.city": searchRegex },
            { "personalDetails.state": searchRegex },
            { "personalDetails.country": searchRegex },
            { "submission.job.location.city": searchRegex },
            { "submission.job.location.state": searchRegex },
            { "submission.job.location.country": searchRegex },
          ];
        } else if (searchField === "status" && submissionType !== "hired") {
          // Status search is not available for hired candidates tab
          searchOr = [{ "submission.status": searchRegex }];
        } else if (searchField === "JobType") {
          searchOr = [{ "submission.job.jobType": searchRegex }];
        } else if (searchField === "Experience") {
          // Search job experience requirements only (based on job min/max experience)
          const jobExpCondition = createExperienceSearchConditions(
            searchTerm,
            false
          );
          if (jobExpCondition) {
            searchOr = [jobExpCondition];
          } else {
            // If no valid experience pattern found, don't match anything
            searchOr = [];
          }
        }
        if (searchOr.length > 0) {
          postLookupMatchStages.push({ $match: { $or: searchOr } });
        }
      } else {
        // If searchField is not recognized, do not match any records
        additionalMatchStages.push({ $match: { _id: null } });
      }
    }

    // Build the aggregation pipeline
    let pipeline = [
      {
        $match: {
          "createdBy.userID": user.userId,
        },
      },
      // Add additional match stages for filtering (location, experience, search)
      ...additionalMatchStages,
      {
        $lookup: {
          from: "candidatesubmissions",
          let: {
            candidateID: "$candidateID",
            userID: "$createdBy.userID",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$candidate.candidateID", "$$candidateID"],
                    },
                    {
                      $eq: ["$submittedBy.userID", "$$userID"],
                    },
                  ],
                },
                // Apply submission type filter (rejected, hired, activeSubmission)
                ...query,
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job._id",
                foreignField: "_id",
                as: "job",
              },
            },
            {
              $unwind: {
                path: "$job",
                preserveNullAndEmptyArrays: false,
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $unwind: {
          path: "$submission",
          preserveNullAndEmptyArrays: false, // Only keep candidates with submissions
        },
      },
      // Add post-lookup filtering stages (status, job type, search on job fields, etc.)
      ...postLookupMatchStages,
    ];

    // Add sorting logic based on submission date
    const sortStage = {
      $sort: {
        "submission.submittedAt": sortBy === "oldest" ? 1 : -1,
      },
    };

    // Add pagination with sorting
    pipeline.push({
      $facet: {
        metadata: [{ $count: "total" }],
        data: [sortStage, { $skip: skip }, { $limit: limit }],
      },
    });

    const results = await Candidates.aggregate(pipeline);

    const totalSubmission = results[0].metadata[0]?.total || 0;
    const submission = results[0].data;

    res.status(200).json({
      success: true,
      message: "all submitted candidate fetched successfully",
      total: totalSubmission,
      page,
      totalPages: Math.ceil(totalSubmission / limit),
      results: submission,
    });
  } catch (error) {
    console.error("Error in get candidate submission:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while get candidate submission",
      error: error.message,
    });
  }
};

//* @Desc get candidate submissions with there status like hired,rejected,active & all submissions
//* @Route POST /api/v1/condidate/instantsubmit
//* @Access private, recruiter
const instantSubmit = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    const recruiterId = user?._id;

    const { jobID, candidateID } = req.body;

    const amountDeducted = 2;

    if (!jobID || !candidateID) {
      return res.status(400).json({
        success: false,
        message: "JobId and candidateId are required.",
      });
    }

    const jobs = await job.findOne({ jobId: jobID });
    if (!jobs) {
      return res.status(400).json({
        success: false,
        message: "Job not found",
      });
    }

    const recruiter = await recruiterProfile.findOne({
      "user._id": recruiterId,
    });
    if (!recruiter) {
      return res.status(400).json({
        success: false,
        message: "Recruiter not found",
      });
    }

    // Check if the job is already selected by the recruiter
    if (
      recruiter?.jobsWorkingOn?.filter(
        (item) =>
          item.jobId.toString() == jobID?.toString() &&
          item.status == "assigned" &&
          item.isActive
      ).length > 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Job is already selected by the recruiter",
      });
    }

    const userCoinBalances = await UserCoinBalance.findOne({
      userId: recruiter.user._id,
    });

    if (!userCoinBalances) {
      return res.status(400).json({
        success: false,
        message: "User coin balance not found",
      });
    }
    const candidiateDetails = await Candidates.findOne({
      candidateID: candidateID,
    });
    if (!candidiateDetails) {
      return res.status(400).json({
        success: false,
        message: "candidate not found",
      });
    }

    const workRequest = await candidateSubmission.findOne({
      "job.jobID": jobID,
      "candidate.candidateID": candidateID,
    });

    if (workRequest) {
      return res.status(400).json({
        success: false,
        message: "candidate is already submitted on this request.",
      });
    }

    // candidate submittions
    const CandidateSubmissionIntance = new candidateSubmission({
      isInstantSubmit: true,
      job: {
        _id: jobs._id,
        jobID: jobs.jobId,
      },
      submittedBy: {
        _id: user._id,
        userID: user.userId,
      },
      candidate: {
        _id: candidiateDetails._id,
        candidateID: candidiateDetails.candidateID,
      },
    });

    const submission = await CandidateSubmissionIntance.save();

    let eventDate = new Date();
    eventDate.setHours(0, 0, 0, 0);

    // add logs that status will be updated
    const submissionEventLog = new submissionLog({
      status: "submitted",
      submittedBy: user._id,
      submissionID: submission._id,
      eventDate: eventDate,
    });

    await submissionEventLog.save();

    // create coin transactions
    await CoinTransaction.create({
      userId: recruiter.user._id,
      relatedJobId: jobs._id,
      relatedSubmissionId: submission?._id,
      transactionType: "spent",
      quantity: 1,
      balanceBefore: userCoinBalances.currentBalance,
      balanceAfter: userCoinBalances.currentBalance - amountDeducted,
      status: "completed",
      spendType: "instantSubmit",
      amount: amountDeducted,
      createdAt: Date.now(),
    });

    // Update the recruiter to include the selected job
    recruiter.jobsWorkingOn.push({
      jobId: jobs._id,
      assignedBy: "self",
      isInstantSubmit: true,
      isSlotEmpty: true,
      assignedAt: Date.now(),
    });
    await recruiter.save();

    // Update the coin balance
    userCoinBalances.currentBalance -= amountDeducted; // Deduct 1 coin for job selection
    userCoinBalances.totalSpent += amountDeducted; // Update total spent
    userCoinBalances.lastTransactionAt = Date.now();
    await userCoinBalances.save();

    // create notifications for account manager from recruiter
    await createNotification({
      recipients: [jobs?.accountManager?._id],
      type: "submission",
      relatedJobId: [jobID],
      relatedCandidateId: [submission.submissionId],
    });

    res.status(200).json({
      success: true,
      message: "Job selected successfully",
    });
  } catch (error) {
    console.error("Error in candidate instant submit:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate instant submit",
      error: error.message,
    });
  }
};

const UpdateCandidateStatus = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { newstatus, submissionId, notes, eventdate } = req.body;

    if (!newstatus || !submissionId) {
      return res.status(400).json({
        success: false,
        message: "Submission ID and status are required.",
      });
    }

    if (!submissionEnums.includes(newstatus)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission status.",
      });
    }

    let query = {};

    if (mongoose.Types.ObjectId.isValid(submissionId)) {
      query._id = new mongoose.Types.ObjectId(submissionId);
    } else {
      query.submissionId = submissionId;
    }

    const isSubmissionWithStatus = await candidateSubmission.findOne({
      ...query,
      status: newstatus,
    });

    if (isSubmissionWithStatus) {
      return res.status(400).json({
        success: false,
        message: "Submission already has this status.",
      });
    }

    const submission = await candidateSubmission.findOneAndUpdate(
      query,
      { status: newstatus },
      { new: true }
    );

    if (!submission) {
      return res.status(400).json({
        success: false,
        message: "Submission not found.",
      });
    }

    let earnedTypeValue = null;

    // Coin logic
    if (["interviewing", "offer released"].includes(newstatus)) {
      let earnAmount = 0;

      if (newstatus === "interviewing") {
        earnAmount = 1;
        earnedTypeValue = "Interviewing";

        await recruiterProfile.findOneAndUpdate(
          {
            "user._id": new mongoose.Types.ObjectId(
              submission?.submittedBy?._id
            ),
            "jobsWorkingOn.jobId": new mongoose.Types.ObjectId(
              submission?.job?._id
            ),
          },
          {
            $set: {
              "jobsWorkingOn.$.isSlotEmpty": true,
            },
          },
          { new: true }
        );
      }

      if (newstatus === "offer released") {
        earnAmount = 3;
        earnedTypeValue = "Hired";
      }

      const userCoin = await UserCoinBalance.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(submission?.submittedBy?._id) },
        { $inc: { currentBalance: earnAmount, totalEarned: earnAmount } },
        { new: true }
      );

      await createNotification({
        recipients: [submission?.submittedBy?._id],
        type: "coin-earn",
        message: `Congratulations! You've earned ${earnAmount} coins.`,
        relatedCandidateId: [submission?.submissionId],
        relatedJobId: [submission?.job?.jobID],
      });

      if (!userCoin) {
        return res.status(400).json({
          success: false,
          message: "User coin balance not found.",
        });
      }

      const coinTransaction = new CoinTransaction({
        userId: submission?.submittedBy?._id,
        relatedJobId: submission?.job?._id,
        relatedSubmissionId: submission._id,
        transactionType: "earned",
        earnedType: earnedTypeValue,
        amount: earnAmount,
        quantity: earnAmount,
        balanceBefore: userCoin.currentBalance - earnAmount,
        balanceAfter: userCoin.currentBalance,
      });

      await coinTransaction.save();
    }

    // Event Date
    let eventDate = eventdate ? new Date(eventdate) : new Date();
    eventDate.setHours(0, 0, 0, 0);

    const submissionEventLog = new submissionLog({
      status: newstatus,
      submittedBy: user._id,
      submissionID: submission._id,
      eventDate,
      notes,
    });

    await submissionEventLog.save();

    const statusMessages = {
      submitted: "Candidate has been submitted.",
      reviewing: "Candidate is under review.",
      "submitted to client": "Candidate has been submitted to the client.",
      selected: "Candidate has been selected.",
      interviewing: "Candidate is scheduled for an interview.",
      "awaiting offer": "Awaiting offer for the candidate.",
      rejected: "Candidate has been rejected.",
      "offer released": "An offer has been released to the candidate.",
      "offer accepted": "Candidate has accepted the offer.",
      "offer rejected": "Candidate has rejected the offer.",
      "hired-under guarantee period":
        "Candidate is hired under guarantee period.",
      "guarantee period not completed":
        "Guarantee period has not yet been completed.",
      "guarantee period completed":
        "Guarantee period has been successfully completed.",
    };

    await createNotification({
      recipients: [submission?.submittedBy?._id],
      type: "submission",
      message: statusMessages[newstatus] || `Status updated to: ${newstatus}`,
      relatedCandidateId: [submission?.submissionId],
      relatedJobId: [submission?.job?.jobID],
    });
    res.status(200).json({
      success: true,
      submission,
      earnedType: earnedTypeValue,
      message: "Candidate status updated successfully.",
    });
  } catch (error) {
    console.error("Error updating candidate status:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while updating candidate status.",
      error: error.message,
    });
  }
};

//* @Desc get candidate timeline
//* @Route GET /api/v1/condidate/candidate-timeline/:candidiateID
//* @Access private,
const getCandidateTimeline = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { submissionID } = req.params;

    console.log(submissionID);

    if (!submissionID) {
      return res.status(400).json({
        success: false,
        message: "submission id not found",
      });
    }

    const results = await submissionLog.aggregate([
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "submissionID",
          foreignField: "_id",
          as: "submissionID",
        },
      },
      {
        $unwind: {
          path: "$submissionID",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          "submissionID.submissionId": submissionID,
        },
      },
      {
        $sort: {
          eventDate: -1,
        },
      },
    ]);

    res.status(200).json({
      success: true,
      message: "candidate timeline successfully",
      data: results,
    });
  } catch (error) {
    console.error("Error in candidate timeline:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate timeline",
      error: error.message,
    });
  }
};

//* @Desc get candidate timeline
//* @Route GET /api/v1/condidate/submission-stats
//* @Access private,
const submissionStats = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const submission = await submissionLog.aggregate([
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "submissionID",
          foreignField: "_id",
          as: "submissionID",
        },
      },
      {
        $unwind: "$submissionID",
      },
      {
        $lookup: {
          from: "jobs",
          localField: "submissionID.job._id",
          foreignField: "_id",
          as: "job",
        },
      },
      {
        $unwind: "$job",
      },
      {
        $match: {
          "job.accountManager.userID": user.userId,
          "job.jobStatus": "Active",
          "job.isDeleted": false,
        },
      },
      {
        $facet: {
          responseRate: [
            {
              $match: {
                status: {
                  $in: ["reviewing", "submitted"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                submitted: {
                  $min: {
                    $cond: [
                      {
                        $eq: ["$status", "submitted"],
                      },
                      "$createdAt",
                      null,
                    ],
                  },
                },
                reviewing: {
                  $min: {
                    $cond: [
                      {
                        $eq: ["$status", "reviewing"],
                      },
                      "$createdAt",
                      null,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                timeDiff: {
                  $cond: [
                    {
                      $and: ["$submitted", "$reviewing"],
                    },
                    {
                      $round: [
                        {
                          $divide: [
                            {
                              $subtract: ["$reviewing", "$submitted"],
                            },
                            1000 * 60,
                          ],
                        },
                        3,
                      ],
                    },
                    null,
                  ],
                },
              },
            },
            {
              $match: {
                timeDiff: { $ne: null },
              },
            },
            {
              $group: {
                _id: null,
                averageTimeDiffMs: {
                  $avg: "$timeDiff",
                },
              },
            },
            {
              $project: {
                _id: 0,
                averageTimeDiffMs: 1,
              },
            },
          ],
          submissionToInterview: [
            {
              $match: {
                status: {
                  $in: ["submitted", "interviewing"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                submitted: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "submitted"],
                      },
                      1,
                      0,
                    ],
                  },
                },
                interviewing: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "interviewing"],
                      },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                interviewRate: {
                  $cond: [
                    { $eq: ["$submitted", 0] },
                    0,
                    {
                      $round: [
                        {
                          $multiply: [
                            {
                              $divide: ["$interviewing", "$submitted"],
                            },
                            100,
                          ],
                        },
                        2,
                      ],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                interviewRate: 1,
              },
            },
          ],
          InterviewToOffer: [
            {
              $match: {
                status: {
                  $in: ["offer released", "interviewing"],
                },
              },
            },
            {
              $group: {
                _id: "$submissionID._id",
                offer: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "offer released"],
                      },
                      1,
                      0,
                    ],
                  },
                },
                interviewing: {
                  $sum: {
                    $cond: [
                      {
                        $eq: ["$status", "interviewing"],
                      },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                offerRate: {
                  $cond: [
                    { $eq: ["$interviewing", 0] },
                    0,
                    {
                      $round: [
                        {
                          $multiply: [
                            {
                              $divide: ["$offer", "$interviewing"],
                            },
                            100,
                          ],
                        },
                        2,
                      ],
                    },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                offerRate: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$InterviewToOffer",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$submissionToInterview",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$responseRate",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          "Interviewing to Offer": "$InterviewToOffer.offerRate",
          "Submission to Interviewing": "$submissionToInterview.interviewRate",
          "Response Rate": "$responseRate.averageTimeDiffMs",
        },
      },
    ]);

    const jobs = await job.aggregate([
      {
        $match: {
          "accountManager.userID": user.userId,
          jobStatus: "Active",
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          localField: "_id",
          foreignField: "job._id",
          as: "candidatesubmissions",
        },
      },
      {
        $addFields: {
          candidatesubmissions: {
            $size: "$candidatesubmissions",
          },
        },
      },
      {
        $group: {
          _id: null,
          totalJobs: { $sum: 1 },
          submissionJobs: {
            $sum: {
              $cond: [{ $gt: ["$candidatesubmissions", 0] }, 1, 0],
            },
          },
        },
      },
      {
        $addFields: {
          submissionPercentage: {
            $cond: [
              { $eq: ["$totalJobs", 0] },
              0,
              {
                $round: [
                  {
                    $multiply: [
                      { $divide: ["$submissionJobs", "$totalJobs"] },
                      100,
                    ],
                  },
                  2,
                ],
              },
            ],
          },
        },
      },
      {
        $project: {
          _id: 0,
          submissionPercentage: 1,
        },
      },
    ]);

    const data = {
      "Coverage Ratio": jobs[0]?.submissionPercentage ?? 0,
      "Interviewing to Offer": submission[0]?.["Interviewing to Offer"] ?? 0,
      "Submission to Interviewing":
        submission[0]?.["Submission to Interviewing"] ?? 0,
      "Response Rate": submission[0]?.["Response Rate"] ?? 0,
    };

    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error in candidate timeline:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate timeline",
      error: error.message,
    });
  }
};

//* @Desc get maximum experience value from all candidates
//* @Route GET /api/v1/condidate/max-experience
//* @Access private
const getMaxExperience = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    // Get maximum experience requirement from jobs that have submissions by this recruiter
    const result = await candidateSubmission.aggregate([
      { $match: { "submittedBy.userID": user.userId } },
      {
        $lookup: {
          from: "jobs",
          localField: "job._id",
          foreignField: "_id",
          as: "jobDetails",
        },
      },
      { $unwind: "$jobDetails" },
      {
        $group: {
          _id: null,
          maxExperience: { $max: "$jobDetails.experience.max" },
        },
      },
    ]);
    const maxExperience = result[0]?.maxExperience || 12; // fallback to 12 if no data
    res.status(200).json({ success: true, maxExperience });
  } catch (error) {
    console.error("Error in getMaxExperience:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while fetching max experience",
      error: error.message,
    });
  }
};

//* @Desc get candidate without that job submissions
//* @Route GET /api/v1/condidate/candidate-without-job-submissions/:jobId
//* @Access private,
const candidateWithoutSubmission = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const { jobId } = req.params;
    if (!jobId) {
      return res.status(400).json({
        success: false,
        message: "job not found",
      });
    }

    const data = await Candidates.aggregate([
      {
        $match: {
          "createdBy.userID": user.userId,
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { candidateId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$candidate._id", "$$candidateId"],
                },
              },
            },
          ],
          as: "allSubmissions",
        },
      },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: {
            candidateId: "$_id",
            jobId: jobId,
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$candidate._id", "$$candidateId"] },
                    { $eq: ["$job.jobID", "$$jobId"] },
                  ],
                },
              },
            },
          ],
          as: "submission",
        },
      },
      {
        $addFields: {
          isAlreadySubmission: { $size: "$submission" },
          submittedJobsCount: { $size: "$allSubmissions" },
        },
      },
      {
        $match: {
          $expr: { $lt: ["$isAlreadySubmission", 1] },
        },
      },
      {
        $project: {
          submission: 0,
          isAlreadySubmission: 0,
          allSubmissions: 0,
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error in candidate with submission:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while candidate with submission",
      error: error.message,
    });
  }
};

module.exports = {
  getCandidate,
  getCandidatesByJobAndRecruiter,
  getAllCandidate,
  AddCandidate,
  updateCandidate,
  removeCandidate,
  Licensing,
  Certification,
  Education,
  WorkHistory,
  skillsAndExperience,
  HealthAndCompliance,
  SubmissionDetails,
  DocumentAttachment,
  candidateJobSubmissions,
  getSubmissions,
  instantSubmit,
  UpdateCandidateStatus,
  getCandidateTimeline,
  submissionStats,
  getMaxExperience,
  candidateWithoutSubmission,
};
