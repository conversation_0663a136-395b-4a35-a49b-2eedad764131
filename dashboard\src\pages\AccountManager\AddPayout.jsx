import React, { useState, useEffect } from "react";
import { useFormik } from "formik";
import { useNavigate } from "react-router-dom";
import InputField from "../../components/common/Input/InputField";
import SelectField from "../../components/common/Input/SelectionField";
import CalendarDropDownField from "../../components/common/Input/CalenderDropDownField";
import AppButton from "../../components/common/Button/AppButton";
import { getJobsByAccountManager } from "../../services/operations/jobAPI";
import { getRecruitersByJobId } from "../../services/operations/recruiterAPI";
import { getCandidatesByJobAndRecruiter } from "../../services/operations/candidateAPI";
import { addPayout } from "../../services/operations/payoutAPI";

const jobTypeOptions = [
  { label: "Full-time", value: "full-time" },
  { label: "Contract", value: "contract" },
];

const payRateOptions = [
  { label: "Hourly", value: "hourly" },
  { label: "Salary", value: "salary" },
];

const AddPayout = () => {
  const [jobOptions, setJobOptions] = useState([]);
  const [recruiterOptions, setRecruiterOptions] = useState([]);
  const [candidateOptions, setCandidateOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      jobId: "",
      payoutDate: "",
      jobTitle: "",
      candidate: "",
      recruiter: "",
      jobType: "",
      payRate: "",
      payRateAmount: "",
      commission: "1500",
      guaranteePeriod: "",
      bankRefId: "",
    },
    onSubmit: async (values, { resetForm }) => {
      console.log("Form submitted with values:", values);
      setError(null);
      setSuccess(null);
      const payload = {
        jobID: values.jobId,
        recruiterID: values.recruiter,
        candidateID: values.candidate,
        payoutReleaseDate: values.payoutDate,
        payRate: values.payRate,
        payRateAmount: values.payRateAmount,
        commission: values.commission,
        bankReference: values.bankRefId,
        guaranteePeriod: values.guaranteePeriod,
      };
      try {
        await addPayout(payload);
        setSuccess("Payout added successfully!");
        navigate("/payout");
        resetForm();
      } catch (err) {
        setError(err.message || "Failed to add payout");
      }
    },
  });

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getJobsByAccountManager();
        if (response?.success && response?.data) {
          setJobOptions(
            response.data.map((job) => ({
              label: `${job.jobId} - ${job.jobTitle}`,
              value: job.jobId,
              jobData: {
                jobTitle: job.jobTitle,
                jobType: job.jobType,
                payRate: job.payRate || "",
                guaranteePeriod: job.guaranteePeriod || "",
              },
            }))
          );
        } else {
          setError("Failed to fetch jobs");
        }
      } catch (err) {
        setError(err.message || "Failed to fetch jobs");
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, []);

  const handleJobSelection = async (selectedJobIdOrEvent) => {
    let selectedJobId;
    if (selectedJobIdOrEvent && selectedJobIdOrEvent.target) {
      selectedJobId = selectedJobIdOrEvent.target.value;
    } else {
      selectedJobId = selectedJobIdOrEvent;
    }
    formik.setFieldValue("jobId", selectedJobId);
    formik.setFieldValue("recruiter", "");
    formik.setFieldValue("candidate", "");

    const selectedJob = jobOptions.find((job) => job.value === selectedJobId);
    if (selectedJob?.jobData) {
      formik.setFieldValue("jobTitle", selectedJob.jobData.jobTitle || "");
      formik.setFieldValue("jobType", selectedJob.jobData.jobType || "");
      formik.setFieldValue("payRate", selectedJob.jobData.payRate || "");
      formik.setFieldValue(
        "guaranteePeriod",
        selectedJob.jobData.guaranteePeriod || ""
      );
    } else {
      formik.setFieldValue("jobTitle", "");
      formik.setFieldValue("jobType", "");
      formik.setFieldValue("payRate", "");
      formik.setFieldValue("payRateAmount", "");
      formik.setFieldValue("guaranteePeriod", "");
    }

    try {
      const recruiterRes = await getRecruitersByJobId(selectedJobId);
      if (recruiterRes?.success && recruiterRes?.data) {
        setRecruiterOptions(
          recruiterRes.data.map((rec) => ({
            label:
              `${rec.userId} - ${rec.name.firstName} ${rec.name.lastName}`.trim(),
            value: rec.userId,
          }))
        );
      } else {
        setRecruiterOptions([]);
      }
    } catch {
      setRecruiterOptions([]);
    }
    setCandidateOptions([]);
  };

  useEffect(() => {
    const fetchCandidates = async () => {
      if (formik.values.jobId && formik.values.recruiter) {
        try {
          const res = await getCandidatesByJobAndRecruiter(
            formik.values.jobId,
            formik.values.recruiter
          );

          setCandidateOptions(res.data || []);
        } catch {
          setCandidateOptions([]);
        }
      } else {
        setCandidateOptions([]);
      }
    };
    fetchCandidates();
  }, [formik.values.jobId, formik.values.recruiter]);

  const handleRecruiterSelection = (val) => {
    const recruiterId =
      typeof val === "object" && val.target ? val.target.value : val;
    formik.setFieldValue("recruiter", recruiterId);
    formik.setFieldValue("candidate", "");
  };

  const handlePayRateSelection = (val) => {
    const payRateValue =
      typeof val === "object" && val.target ? val.target.value : val;
    formik.setFieldValue("payRate", payRateValue);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <form
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
        onSubmit={formik.handleSubmit}
        autoComplete="off"
      >
        <SelectField
          label="Job ID"
          options={jobOptions}
          value={formik.values.jobId}
          onChange={(e) => handleJobSelection(e.target?.value || e)}
          placeholder="Select Job ID"
          required
        />

        <InputField
          label="Job Title"
          name="jobTitle"
          value={formik.values.jobTitle}
          onChange={formik.handleChange}
          placeholder="Job Title"
          required
        />

        <SelectField
          label="Job Type"
          options={jobTypeOptions}
          value={formik.values.jobType}
          onChange={(val) => formik.setFieldValue("jobType", val)}
          placeholder="Job type"
          required
        />

        <SelectField
          label="Recruiter"
          options={recruiterOptions}
          value={formik.values.recruiter}
          onChange={handleRecruiterSelection}
          placeholder="Select Recruiter"
          required
        />

        <SelectField
          label="Candidate"
          options={candidateOptions}
          value={formik.values.candidate}
          onChange={(val) => {
            const candidateId =
              typeof val === "object" && val.target ? val.target.value : val;
            formik.setFieldValue("candidate", candidateId);
          }}
          placeholder="Select Candidate"
          required
        />

        <SelectField
          label="Pay Rate Type"
          options={payRateOptions}
          value={formik.values.payRate}
          onChange={handlePayRateSelection}
          placeholder="Select Pay Rate Type"
          required
        />

        <InputField
          label="Pay Rate"
          name="payRateAmount"
          type="number"
          placeholder="Enter Pay Rate"
          value={formik.values.payRateAmount}
          onChange={formik.handleChange}
          required
        />

        <CalendarDropDownField
          label="Payout Released Date"
          name="payoutDate"
          values={formik.values.payoutDate}
          onChange={(val) =>
            formik.setFieldValue("payoutDate", val ? val.toISOString() : "")
          }
          placeholder="Select Date"
          required
        />

        <InputField
          label="Guarantee Period (days)"
          name="guaranteePeriod"
          placeholder="Guarantee period"
          value={formik.values.guaranteePeriod}
          onChange={formik.handleChange}
          required
        />

        <InputField
          label="Commission"
          name="commission"
          placeholder="Enter Commission"
          value={formik.values.commission}
          onChange={formik.handleChange}
          required
        />

        <InputField
          label="Bank Reference ID"
          name="bankRefId"
          placeholder="Enter Bank Reference ID"
          value={formik.values.bankRefId}
          onChange={formik.handleChange}
          required
        />

        <div className="col-span-3 flex gap-4 mt-6">
          <AppButton label="Save" type="submit" />
          <AppButton type="button" variant="outline" label="Cancel" />
        </div>
      </form>
    </div>
  );
};

export default AddPayout;
