import React, { useEffect, useRef, useState } from "react";

import {
  getNotification,
  markAsRead,
} from "../../../services/operations/notificationAPI";
import { getFormatDate } from "../../../utils/getTimeAgo";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const Notification = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();
  const [notification, setNotification] = useState(null);
  const userInfo = useSelector((state) => state.auth?.user);
  let isRecruiter = userInfo?.role === "recruiter";
  // Close dropdown if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  async function getAllNotification() {
    const res = await getNotification();
    setNotification(res.data);
  }

  useEffect(() => {
    if (isOpen) {
      getAllNotification();
    }
  }, [isOpen]);

  const handleMarkAllAsRead = async () => {
    await markAsRead();
    getAllNotification();
  };

  async function makasReadAPI(notificationId) {
    await markAsRead(notificationId);
  }

  function handleRedirect(data) {
    if (!data?.isRead) {
      makasReadAPI(data?._id);
    }
    if (data.type == "submission") {
      if (isRecruiter) {
        navigate(
          `/jobs/jobdetails?tab=submissions&jobId=${
            data?.relatedJobId[0]
          }&istimeline=true${
            data?.relatedCandidateId
              ? `&submissionId=${data?.relatedCandidateId[0]}`
              : ""
          }`
        );
      } else {
        navigate("/submissions");
      }
    } else if (data.type == "job-update") {
      navigate(`/jobs/jobdetails?tab=updates&jobId=${data.relatedJobId[0]}`);
    } else if (data.type == "job-assigned") {
      navigate(`/jobs?tabs=3`);
    } else if (data.type == "coin-earn") {
      navigate(`/coin-history`);
    } else if (data.type == "work-on-request") {
      navigate(`/jobs?tabs=2`);
    } else if (data.type == "message-job") {
      navigate(`/jobs/jobdetails?tab=chat&jobId=${data?.relatedJobId[0]}`);
    }
  }

  return (
    <>
      <button
        type="button"
        className="flex text-sm cursor-pointer p-1 rounded-full focus:ring-1 focus:ring-gray-300"
        aria-expanded={isOpen}
        onClick={() => {
          setIsOpen(!isOpen);
        }}
      >
        <span className="sr-only">bell icon</span>
        <img
          className="w-5 h-5 rounded-full"
          src={"assets/icons/bell_icons.svg"}
          alt="bell icon"
        />
      </button>

      {isOpen && (
        // <div className="absolute right-0 top-10 z-50 my-3 me-3 w-56 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-lg">
        <div
          ref={dropdownRef}
          className="w-96 rounded-xl mt-3 shadow-[0px_10px_40px_rgba(0,0,0,0.2)] bg-white py-4 px-4 space-y-4 absolute right-10 top-10 z-50"
        >
          <div className="text-lg font-semibold border-b pb-2">
            Notification
          </div>
          <div className="max-h-[25rem]  overflow-y-auto">
            {notification &&
            Array.isArray(notification) &&
            notification.length > 0 ? (
              notification?.map((item, i) => (
                <div
                  key={i}
                  className="space-y-2"
                  onClick={(e) => {
                    e.preventDefault();
                    handleRedirect(item);
                  }}
                >
                  <div className="flex items-center justify-between text-sm text-gray-700 cursor-pointer  py-2 px-1 hover:bg-gray-50">
                    <div className="flex items-center space-x-2">
                      {/* <div className=" bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-semibold ">
                        {item?.name?.charAt(0)}
                      </div> */}
                      {/* <img
                        className="w-7 h-7 rounded-full text-xs flex-shrink-0"
                        src={slotEmpty}
                        alt="slot icon"
                      /> */}
                      <div className="relative w-7 h-7 flex items-center justify-center text-xs flex-shrink-0">
                        <img
                          className={
                            [
                              "work-on-request",
                              "job-assigned",
                              "job-update",
                            ].includes(item.type)
                              ? "w-6 h-6 rounded-full text-xsflex items-center justify-center flex-shrink-0"
                              : ["coin-earn"].includes(item.type)
                              ? "w-6 h-6 p-0.5 rounded-full text-xsflex items-center justify-center flex-shrink-0"
                              : ["submission"].includes(item.type)
                              ? "w-6 h-6 rounded-full text-xsflex items-center justify-center flex-shrink-0"
                              : ["message-job"].includes(item.type)
                              ? "w-6 h-6 rounded-full text-xsflex items-center justify-center flex-shrink-0"
                              : "w-6 h-6 rounded-full text-xsflex items-center justify-center flex-shrink-0"
                          }
                          src={
                            [
                              "work-on-request",
                              "job-assigned",
                              "job-update",
                            ].includes(item.type)
                              ? "/assets/icons/slot_empty.svg"
                              : ["coin-earn"].includes(item.type)
                              ? "/assets/icons/coins.svg"
                              : ["submission"].includes(item.type)
                              ? "/assets/icons/resume_left.svg"
                              : ["message-job"].includes(item.type)
                              ? "/assets/icons/resume_left.svg"
                              : "/assets/icons/slot_empty.svg"
                          }
                          alt="slot icon"
                        />

                        {!item?.isRead && (
                          <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white" />
                        )}
                      </div>
                      <span>{item?.message}</span>
                    </div>
                    <span className="text-xs text-gray-400 whitespace-nowrap">
                      {getFormatDate(item?.createdAt)}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-400 text-sm">No notifications</div>
            )}
          </div>
          {notification &&
            Array.isArray(notification) &&
            notification.length > 0 && (
              <div className="flex justify-between text-sm text-blue-500 pt-2 border-t">
                <button
                  onClick={handleMarkAllAsRead}
                  className="hover:underline cursor-pointer"
                >
                  Mark all as read
                </button>
                {/* <button onClick={handleSeeAll} className="hover:underline">
              See all
            </button> */}
              </div>
            )}
        </div>
      )}
    </>
  );
};

export default Notification;
