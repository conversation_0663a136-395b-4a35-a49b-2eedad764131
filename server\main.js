const express = require("express");
const app = express();
const { connectDB } = require("./configs/database");
const cors = require("cors");
const dotenv = require("dotenv");
const bodyParser = require("body-parser");
const cookieParser = require("cookie-parser");

//routers
const authRoutes = require("./routes/authRoutes");
const profileRoutes = require("./routes/profileRoutes");
const jobRouter = require("./routes/jobRoutes");
const candidateRouter = require("./routes/CandidateRoute");
const payout = require("./routes/payoutRoutes");
const headmanager = require("./routes/headManagerRoutes");
const accountManager = require("./routes/accountManagerRoute");
const notification = require("./routes/notificationRoute");
const recruiter = require("./routes/recruiterRoute");
const chatRoutes = require("./routes/chatRoutes");
const TestEmail = require("./utils/TestEmail");
const saveContact = require("./routes/contactRoutes");

dotenv.config();

// Set up middleware to parse incoming request bodies in JSON format
// and handle URL-encoded data with a size limit of 10mb
app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ extended: true, limit: "10mb" }));

// Connect to the database
connectDB();

const port = process.env.PORT || 4000;

// Set up middleware to parse cookies and JSON data
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Set up CORS to allow requests from specific origins
const allowedOrigins = [
  "http://localhost:3000",
  "http://localhost:5173",
  "http://localhost:4173",
  "https://hirring.com",
  "https://recruiter.hirring.com",
  "https://www.hirring.com",
];

// Configure CORS with dynamic origin based on the request
const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
};

// Use the CORS middleware
app.use(cors(corsOptions));

app.get("/", (req, res) => {
  return res.json({
    success: true,
    message: "Your server is running",
  });
});

//* Routes
app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/user", profileRoutes);
app.use("/api/v1/job", jobRouter);
app.use("/api/v1/candidate", candidateRouter);
app.use("/api/v1/payout", payout);
app.use("/api/v1/headmanager", headmanager);
app.use("/api/v1/accountmanager", accountManager);
app.use("/api/v1/notification", notification);
app.use("/api/v1/recruiter", recruiter);
app.use("/api/v1/chat", chatRoutes);
app.use("/api/v1/save-contact", saveContact);

// Set up the server to listen for incoming requests
app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});
