const { RefreshToken } = require("../models/RefreshToken");
const {
  verifyAccessToken,
  isTokenBlacklisted,
} = require("../services/tokenService");
const userService = require("../services/userService");

exports.auth = async (req, res, next) => {
  try {
    // Get token from header
    const token =
      req?.header("Authorization")?.replace("Bearer ", "") ||
      req?.header("authorization")?.replace("Bearer ", "");

    if (!token) {
      return res
        .status(401)
        .json({ message: "No token, authorization denied" });
    }

    // Check if token is blacklisted
    const isBlacklisted = await isTokenBlacklisted(token);

    if (isBlacklisted) {
      return res
        .status(401)
        .json({ message: "Token is invalid, please login again." });
    }

    // Verify token
    const decoded = verifyAccessToken(token);
    if (!decoded?.success) {
      return res
        .status(401)
        .json({ message: "Token is invalid, please login again." });
    }

    // Get user from the token
    const user = await userService.findUserById(decoded.data._id);

    // Check if user exists
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    const isRefreshToken = await RefreshToken.findOne({
      userId: decoded.data._id,
    });

    if (!isRefreshToken) {
      return res
        .status(401)
        .json({ message: "Token is invalid, please login again." });
    }

    // Check if user is active
    // if (!user.isActive) {
    //   return res.status(401).json({ message: "User has been deactivated" });
    // }

    // Check token version (for forced logout)
    if (user.tokenVersion !== decoded.data.tokenVersion) {
      return res
        .status(401)
        .json({ message: "Session expired, please login again" });
    }

    // Attach user to request
    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    res.status(401).json({ message: "Token is invalid, please login again." });
  }
};

// Role-based authorization middleware
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.userType)) {
      return res.status(403).json({
        message: `User with role ${req.user.userType} is not authorized to access this resource`,
      });
    }
    next();
  };
};
