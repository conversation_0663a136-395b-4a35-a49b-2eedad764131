import { Link } from "react-router";

const LeftSection = () => {
  return (
    <div className=" w-[100%] md:w-[60%] lg:w-[50%] py-15  flex  items-center justify-center">
      <div className="w-[80%] md:w-[60%] lg:w-[45%] mx-auto flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <img
            src={"/assets/images/icon.png"}
            alt="icon"
            width={50}
            height={100}
          />
          <h2 className="text-4xl font-bold">Thank You!</h2>
          <p className="text-gray-600 text-md leading-5">
            We’ve received your information and are excited to have you on
            board. One of our account managers will be reaching out to you very
            soon to help you get started.
            <br />
            <br />
            We look forward to connecting with you!
          </p>
          <a href="https://www.hirring.com">
            <button className="mt-10 bg-[#65FF00] text-[#102108] font-medium cursor-pointer py-2 px-3 rounded-md">
              {" "}
              {"<"} Back to Website
            </button>
          </a>
        </div>
      </div>
    </div>
  );
};

export default LeftSection;
