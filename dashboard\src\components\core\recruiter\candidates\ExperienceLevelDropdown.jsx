import React, { useState, useEffect, useRef } from "react";

const ExperienceLevelDropdown = ({ selected, setSelected, onClose, min = 1, max = 12 }) => {
  // selected is expected to be [min, max]
  const [range, setRange] = useState(selected && selected.length === 2 ? selected : [min, max]);
  const sliderRef = useRef(null);

  useEffect(() => {
    setRange(selected && selected.length === 2 ? selected : [min, max]);
  }, [selected, min, max]);

  function handleMinChange(e) {
    const newMin = Number(e.target.value);
    if (newMin <= range[1]) {
      setRange([newMin, range[1]]);
      setSelected([newMin, range[1]]);
    }
  }
  function handleMaxChange(e) {
    const newMax = Number(e.target.value);
    if (newMax >= range[0]) {
      setRange([range[0], newMax]);
      setSelected([range[0], newMax]);
    }
  }

  // Scroll the slider so both handles are always visible (centered if possible)
  useEffect(() => {
    if (sliderRef.current) {
      const percent = ((range[0] + range[1]) / 2 - min) / (max - min);
      const scrollWidth = sliderRef.current.scrollWidth - sliderRef.current.clientWidth;
      sliderRef.current.scrollLeft = percent * scrollWidth;
    }
  }, [range, min, max]);

  return (
    <div className="bg-white rounded-lg shadow-lg border p-4 w-[340px] max-w-full box-border">
      <div className="flex items-center justify-between mb-2">
        <span className="font-semibold text-gray-800 text-base">Experience Level</span>
        <button
          className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
          onClick={onClose}
          aria-label="Close"
          type="button"
        >
          ×
        </button>
      </div>
      <div className="border-b mb-2" />
      <div className="flex flex-col gap-4 px-2 py-4">
        <div
          ref={sliderRef}
          className="relative flex items-center w-full"
          style={{ minHeight: 64, minWidth: 0, overflow: 'visible' }}
        >
          {/* Track */}
          <div className="absolute left-0 right-0 top-1/2 h-2 bg-gray-200 rounded-full" style={{ zIndex: 0, transform: 'translateY(-50%)' }} />
          {/* Selected range highlight */}
          <div
            className="absolute top-1/2 h-2 bg-blue-400 rounded-full"
            style={{
              left: `${((range[0] - min) / (max - min)) * 100}%`,
              width: `${((range[1] - range[0]) / (max - min)) * 100}%`,
              zIndex: 1,
              transform: 'translateY(-50%)',
            }}
          />
          {/* Min handle */}
          <input
            type="range"
            min={min}
            max={max}
            value={range[0]}
            onChange={handleMinChange}
            className="w-full accent-blue-500 bg-transparent"
            style={{ zIndex: 3, pointerEvents: 'auto', position: 'absolute', left: 0, top: 0 }}
          />
          {/* Max handle */}
          <input
            type="range"
            min={min}
            max={max}
            value={range[1]}
            onChange={handleMaxChange}
            className="w-full accent-blue-500 bg-transparent"
            style={{ zIndex: 2, pointerEvents: 'auto', position: 'absolute', left: 0, top: 24 }}
          />
          {/* Value bubbles */}
          <div
            className="absolute"
            style={{ left: `calc(${((range[0] - min) / (max - min)) * 100}% - 12px)`, top: -32, zIndex: 10 }}
          >
            <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded shadow">{range[0]}</div>
          </div>
          <div
            className="absolute"
            style={{ left: `calc(${((range[1] - min) / (max - min)) * 100}% - 12px)`, top: 32, zIndex: 10 }}
          >
            <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded shadow">{range[1]}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExperienceLevelDropdown;
