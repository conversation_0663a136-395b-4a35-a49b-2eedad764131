import React, { useState, useEffect, useRef } from "react";
import countries from "../../../data/countries.json";
import industries from "../../../data/industries.json";

const FiltersPanel = ({
  showFilters,
  setShowFilters,
  filters,
  handleFilterChange,
  handleApplyFilters,
  availableFilters,
  pageType,
}) => {
  // Local state to hold pending filter changes
  const [pendingFilters, setPendingFilters] = useState(filters);

  // State for the filter selection dropdown
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState(
    new Set(availableFilters || [])
  );
  const filterDropdownRef = useRef(null);

  // Sync pending filters with actual filters when they change (for clear all and applied filters)
  React.useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Update selectedFilters when availableFilters changes (tab change)
  React.useEffect(() => {
    setSelectedFilters(new Set(availableFilters || []));
  }, [availableFilters]);

  // Handle clicks outside the filter dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        filterDropdownRef.current &&
        !filterDropdownRef.current.contains(event.target)
      ) {
        setShowFilterDropdown(false);
      }
    };

    if (showFilterDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showFilterDropdown]);

  // Handle local filter changes (before Apply is clicked)
  const handleLocalFilterChange = (filterKey, value) => {
    setPendingFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  // Handle Apply button click - Modified to NOT close the panel
  const handleApplyClick = () => {
    // Apply all pending filters at once
    Object.keys(pendingFilters).forEach((key) => {
      handleFilterChange(key, pendingFilters[key]);
    });
    // Pass the pending filters to handleApplyFilters to avoid state timing issues
    handleApplyFilters(null, pendingFilters);
    // Remove the panel closing logic - panel will stay open
  };

  // Handle Clear All button click - Only clear filters, not sorting
  const handleClearAllClick = () => {
    // Clear both pending and actual filters
    const clearedFilters = Object.keys(pendingFilters).reduce((acc, key) => {
      acc[key] = [];
      return acc;
    }, {});

    setPendingFilters(clearedFilters);

    // Clear filters individually, not through the global clear function
    Object.keys(clearedFilters).forEach((key) => {
      handleFilterChange(key, clearedFilters[key]);
    });

    // Apply the changes with cleared filters
    handleApplyFilters(null, clearedFilters);

    // Close the panel after clearing
    setShowFilters(false);
  };

  // Handle filter selection toggle - now clears values instead of hiding field
  const handleFilterToggle = (filterKey) => {
    const hasSelectedValues =
      pendingFilters[filterKey] &&
      Array.isArray(pendingFilters[filterKey]) &&
      pendingFilters[filterKey].length > 0;

    if (hasSelectedValues) {
      // If filter has values, clear them
      handleLocalFilterChange(filterKey, []);
    }
    // If no values, do nothing (field stays visible but empty)
  };

  const LocationDropdown = ({
    label,
    selectedValues,
    filterKey,
    onLocalChange,
  }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [localSelected, setLocalSelected] = useState(selectedValues || []);
    const [isOpen, setIsOpen] = useState(false);

    // Sync with parent when selectedValues change (for clear all functionality)
    React.useEffect(() => {
      setLocalSelected(selectedValues || []);
    }, [selectedValues]);

    // Get country options from JSON data
    const countryOptions = countries.map((country) => country.name);

    const toggleOption = (option) => {
      let updated;
      if (localSelected.includes(option)) {
        updated = localSelected.filter((item) => item !== option);
      } else {
        updated = [...localSelected, option];
      }
      setLocalSelected(updated);
      onLocalChange(filterKey, updated);
    };

    const removeTag = (tagToRemove) => {
      const updated = localSelected.filter((item) => item !== tagToRemove);
      setLocalSelected(updated);
      onLocalChange(filterKey, updated);
    };

    const filteredOptions = countryOptions.filter((opt) =>
      opt.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const closeDropdown = () => {
      setIsOpen(false);
      setSearchTerm("");
    };

    const wrapperRef = useRef(null);

    // Close when clicking outside
    useEffect(() => {
      function handleClickOutside(event) {
        if (
          isOpen &&
          wrapperRef.current &&
          !wrapperRef.current.contains(event.target)
        ) {
          setIsOpen(false);
        }
      }
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, [isOpen]);

    return (
      <div className="relative" ref={wrapperRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
        >
          <span className="text-[#2E90FA] font-medium">
            {label}
            {localSelected.length > 0 ? `: ${localSelected.join(", ")}` : ""}
          </span>
          <svg
            className={`w-4 h-4 text-[#94A3B8] transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-1 w-[350px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
            {/* Header with close button */}
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-700">Location</p>
              <button
                onClick={closeDropdown}
                className="text-gray-400 hover:text-gray-600 text-lg leading-none"
              >
                ×
              </button>
            </div>

            {/* Search Input */}
            <div className="p-3 border-b border-gray-100">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Type to search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg
                  className="absolute right-3 top-2.5 w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            {/* Selected Tags */}
            {localSelected.length > 0 && (
              <div className="px-3 py-2 border-b border-gray-100">
                <div className="flex flex-wrap gap-1">
                  {localSelected.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="text-blue-500 hover:text-blue-700 ml-1"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Checkbox Grid */}
            <div className="max-h-60 overflow-y-auto">
              <div className="grid grid-cols-2 gap-x-2 gap-y-1 p-3">
                {filteredOptions.map((option) => (
                  <label
                    key={option}
                    className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-1 py-0.5 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={localSelected.includes(option)}
                      onChange={() => toggleOption(option)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="truncate">{option}</span>
                  </label>
                ))}
              </div>
              {filteredOptions.length === 0 && (
                <div className="p-3 text-center text-gray-500 text-sm">
                  No countries found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Experience Level Dropdown Component (Slider-based)
  const ExperienceLevelDropdown = ({
    label,
    selectedValues,
    filterKey,
    onLocalChange,
  }) => {
    // Convert selectedValues array to range format [min, max]
    const parseSelectedValues = (values) => {
      if (!values || values.length === 0) return [1, 12];

      // If it's already in range format like ["1-12"]
      if (values.length === 1 && values[0].includes('-')) {
        const [min, max] = values[0].split('-').map(Number);
        return [min || 1, max || 12];
      }

      // Default range
      return [1, 12];
    };

    const [range, setRange] = useState(parseSelectedValues(selectedValues));
    const [isOpen, setIsOpen] = useState(false);
    const wrapperRef = useRef(null);

    // Sync with parent when selectedValues change
    React.useEffect(() => {
      setRange(parseSelectedValues(selectedValues));
    }, [selectedValues]);

    // Handle clicks outside to close dropdown
    React.useEffect(() => {
      const handleClickOutside = (event) => {
        if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen]);

    const handleMinChange = (e) => {
      const newMin = Number(e.target.value);
      if (newMin <= range[1]) {
        const newRange = [newMin, range[1]];
        setRange(newRange);
        // Convert back to string format for consistency with other filters
        onLocalChange(filterKey, [`${newRange[0]}-${newRange[1]}`]);
      }
    };

    const handleMaxChange = (e) => {
      const newMax = Number(e.target.value);
      if (newMax >= range[0]) {
        const newRange = [range[0], newMax];
        setRange(newRange);
        // Convert back to string format for consistency with other filters
        onLocalChange(filterKey, [`${newRange[0]}-${newRange[1]}`]);
      }
    };

    const closeDropdown = () => {
      setIsOpen(false);
    };

    const isDefaultRange = range[0] === 1 && range[1] === 12;

    return (
      <div className="relative" ref={wrapperRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
        >
          <span className="text-[#2E90FA] font-medium">
            {label}
            {!isDefaultRange ? `: ${range[0]}-${range[1]} years` : ""}
          </span>
          <svg
            className={`w-4 h-4 text-[#94A3B8] transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg border p-4 w-[340px] max-w-full box-border z-50">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-gray-800 text-base">Experience Level</span>
              <button
                className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
                onClick={closeDropdown}
                aria-label="Close"
                type="button"
              >
                ×
              </button>
            </div>
            <div className="border-b mb-2" />
            <div className="flex flex-col gap-4 px-2 py-4">
              <div
                className="relative flex items-center w-full"
                style={{ minHeight: 64, minWidth: 0, overflow: 'visible' }}
              >
                {/* Track */}
                <div className="absolute left-0 right-0 top-1/2 h-2 bg-gray-200 rounded-full" style={{ zIndex: 0, transform: 'translateY(-50%)' }} />
                {/* Selected range highlight */}
                <div
                  className="absolute top-1/2 h-2 bg-blue-400 rounded-full"
                  style={{
                    left: `${((range[0] - 1) / (12 - 1)) * 100}%`,
                    width: `${((range[1] - range[0]) / (12 - 1)) * 100}%`,
                    zIndex: 1,
                    transform: 'translateY(-50%)',
                  }}
                />
                {/* Min handle */}
                <input
                  type="range"
                  min={1}
                  max={12}
                  value={range[0]}
                  onChange={handleMinChange}
                  className="w-full accent-blue-500 bg-transparent"
                  style={{ zIndex: 3, pointerEvents: 'auto', position: 'absolute', left: 0, top: 0 }}
                />
                {/* Max handle */}
                <input
                  type="range"
                  min={1}
                  max={12}
                  value={range[1]}
                  onChange={handleMaxChange}
                  className="w-full accent-blue-500 bg-transparent"
                  style={{ zIndex: 2, pointerEvents: 'auto', position: 'absolute', left: 0, top: 24 }}
                />
                {/* Value bubbles */}
                <div
                  className="absolute"
                  style={{ left: `calc(${((range[0] - 1) / (12 - 1)) * 100}% - 12px)`, top: -32, zIndex: 10 }}
                >
                  <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded shadow">{range[0]}</div>
                </div>
                <div
                  className="absolute"
                  style={{ left: `calc(${((range[1] - 1) / (12 - 1)) * 100}% - 12px)`, top: 32, zIndex: 10 }}
                >
                  <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded shadow">{range[1]}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const MultiSelectDropdown = ({
    label,
    selectedValues,
    options,
    filterKey,
    onLocalChange,
  }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [localSelected, setLocalSelected] = useState(selectedValues || []);
    const [isOpen, setIsOpen] = useState(false);

    // Sync with parent when selectedValues change (for clear all functionality)
    React.useEffect(() => {
      if (!isOpen) {
        setLocalSelected(selectedValues || []);
      }
    }, [selectedValues, isOpen]);

    const toggleOption = (e) => {
      e.preventDefault();
      e.stopPropagation();

      const option = e.target.value;
      let updated;
      if (localSelected.includes(option)) {
        updated = localSelected.filter((item) => item !== option);
      } else {
        updated = [...localSelected, option];
      }

      setLocalSelected(updated);
      onLocalChange(filterKey, updated);
      setIsOpen(true);
    };

    const removeTag = (tagToRemove) => {
      const updated = localSelected.filter((item) => item !== tagToRemove);
      setLocalSelected(updated);
      onLocalChange(filterKey, updated);
    };

    const filteredOptions = options.filter((opt) =>
      opt.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const closeDropdown = () => {
      setIsOpen(false);
      setSearchTerm("");
    };

    const wrapperRef = useRef(null);

    // Close when clicking outside
    useEffect(() => {
      function handleClickOutside(event) {
        if (
          isOpen &&
          wrapperRef.current &&
          !wrapperRef.current.contains(event.target)
        ) {
          setIsOpen(false);
        }
      }
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, [isOpen]);

    return (
      <div className="relative" ref={wrapperRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-1 px-3 py-2 border border-[#E2E8F0] rounded-full bg-white text-[#475569] text-sm hover:bg-gray-50 cursor-pointer"
        >
          <span className="text-[#2E90FA] font-medium">
            {label}
            {localSelected.length > 0 ? `: ${localSelected.join(", ")}` : ""}
          </span>
          <svg
            className={`w-4 h-4 text-[#94A3B8] transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </button>

        {isOpen && (
          <div
            className="absolute top-full left-0 mt-1 w-[300px] bg-white border border-gray-200 rounded-md shadow-lg z-50"
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
          >
            {/* Header with close button */}
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-100">
              <p className="text-sm font-medium text-gray-700">{label}</p>
              <button
                onClick={closeDropdown}
                className="text-gray-400 hover:text-gray-600 text-lg leading-none cursor-pointer"
              >
                ×
              </button>
            </div>

            {/* Selected Tags */}
            {localSelected.length > 0 && (
              <div className="px-3 py-2 border-b border-gray-100">
                <div className="flex flex-wrap gap-1">
                  {localSelected.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full"
                    >
                      {tag}
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          removeTag(tag);
                        }}
                        className="text-blue-500 hover:text-blue-700 ml-1 cursor-pointer"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Checkbox List */}
            <div className="max-h-60 overflow-y-auto">
              <div className="p-3">
                {filteredOptions.map((option) => (
                  <label
                    key={option}
                    className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-2 py-1.5 rounded"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <input
                      type="checkbox"
                      value={option}
                      checked={localSelected.includes(option)}
                      onChange={toggleOption}
                      onClick={(e) => e.stopPropagation()}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
              {filteredOptions.length === 0 && (
                <div className="p-3 text-center text-gray-500 text-sm">
                  No options found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Status options for different pages
  const statusOptions = {
    submissions: [
      "submitted",
      "reviewing",
      "submitted to client",
      "selected",
      "interviewing",
      "awaiting offer",
      "rejected",
      "offer released",
      "offer accepted",
      "offer rejected",
      "hired-under guarantee period",
      "guarantee period not completed",
      "guarantee period completed",
    ],
    manager: ["Active", "Inactive", "on-leave"],
    jobs: [
      "Active",
      "Inactive",
      "Onhold",
      "Holdbyclient",
      "Filled",
      "Cancelled",
      "Closed",
    ],
  };

  // Get current status options based on pageType
  const currentStatusOptions = statusOptions[pageType] || statusOptions.jobs;

  // Filter options for all tabs
  const filterOptions = {
    location: [
      "United States, CA",
      "United States, NY",
      "United States, TX",
      "Canada, ON",
      "United Kingdom, London",
      "India, Mumbai",
      "Australia, Sydney",
    ],
    jobType: ["full-time", "contract"],
    status: currentStatusOptions, // Use dynamic status options
    specialization: industries?.map((item) => item.name),
    priority: ["High", "Medium", "Low"],
    visibility: ["Publish", "Unpublish"],
    recruiterCount: ["1-5", "6-10", "11-20", "20+"],
    workOnRequestCount: ["0", "1-5", "6-10", "10+"],
    postedDate: [
      "Last 24 hours",
      "Last 7 days",
      "Last 30 days",
      "Last 3 months",
      "Last 6 months",
    ],
    recruiterName: ["John Doe", "Jane Smith", "Mike Johnson", "Sarah Wilson"],
    jobTitle: [
      "Software Engineer",
      "Product Manager",
      "Data Scientist",
      "UX Designer",
    ],
  };

  // Filter labels for better display
  const filterLabels = {
    location: "Location",
    jobType: "Job Type",
    jobStatus: "Job Status",
    status: "Status",
    specialization: "Specialization",
    experienceLevel: "Experience Level",
    priority: "Priority",
    visibility: "Visibility",
    recruiterCount: "Recruiter Count",
    workOnRequestCount: "Work Requests",
    postedDate: "Posted Date",
    recruiterName: "Recruiter Name",
    jobTitle: "Job Title",
  };

  if (!showFilters) return null;

  const renderAvailableFilters = () => {
    // Always show all available filters (no longer controlled by selectedFilters)
    return availableFilters?.map((filterKey) => {
      if (!filterOptions[filterKey]) return null;

      if (filterKey === "location") {
        return (
          <LocationDropdown
            key={filterKey}
            label={filterLabels[filterKey]}
            selectedValues={pendingFilters[filterKey] || []}
            filterKey={filterKey}
            onLocalChange={handleLocalFilterChange}
          />
        );
      }

      if (filterKey === "experienceLevel") {
        return (
          <ExperienceLevelDropdown
            key={filterKey}
            label={filterLabels[filterKey]}
            selectedValues={pendingFilters[filterKey] || []}
            filterKey={filterKey}
            onLocalChange={handleLocalFilterChange}
          />
        );
      }

      return (
        <MultiSelectDropdown
          key={filterKey}
          label={filterLabels[filterKey]}
          selectedValues={pendingFilters[filterKey] || []}
          options={filterOptions[filterKey]}
          filterKey={filterKey}
          onLocalChange={handleLocalFilterChange}
        />
      );
    });
  };

  // Count active filters based on pending filters from available filters
  const activeFilterCount =
    availableFilters?.filter((filterKey) => {
      const filterValue = pendingFilters[filterKey];
      if (Array.isArray(filterValue)) {
        return filterValue.length > 0;
      }
      return filterValue && filterValue !== "";
    }).length || 0;

  // Check if there are pending changes that haven't been applied
  const hasPendingChanges =
    JSON.stringify(pendingFilters) !== JSON.stringify(filters);

  return (
    <div className="bg-white border-b border-[#E5E7EB] py-3">
      <div className="flex items-center justify-between gap-4 mb-2 px-4">
        <div className="flex items-center gap-3 flex-wrap">
          {renderAvailableFilters()}

          <div className="relative" ref={filterDropdownRef}>
            <button
              onClick={() => setShowFilterDropdown(!showFilterDropdown)}
              className="text-gray-400 text-xs font-medium px-2 py-1 rounded hover:bg-gray-100 flex items-center gap-1 border border-gray-200"
              type="button"
            >
              {activeFilterCount === 0 ? (
                "+ Filter"
              ) : (
                <span className="flex items-center gap-1">
                  Filter
                  <span className="bg-blue-100 text-blue-600 rounded-full px-1.5 py-0.5 text-[11px] font-semibold">
                    {activeFilterCount}
                  </span>
                </span>
              )}
              <svg
                width="14"
                height="14"
                fill="none"
                viewBox="0 0 24 24"
                className={`transition-transform ${
                  showFilterDropdown ? "rotate-180" : ""
                }`}
              >
                <path
                  d="M6 9l6 6 6-6"
                  stroke="#888"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </button>

            {/* Filter Selection Dropdown */}
            {showFilterDropdown && (
              <div className="absolute top-full left-0 mt-1 w-[250px] bg-white border border-gray-200 rounded-md shadow-lg z-50">
                <div className="p-3">
                  <p className="text-sm font-medium text-gray-700 mb-3">
                    Select Filters
                  </p>
                  <div className="space-y-2">
                    {availableFilters?.map((filterKey) => {
                      // Check if this filter has any selected values
                      const hasSelectedValues =
                        pendingFilters[filterKey] &&
                        Array.isArray(pendingFilters[filterKey]) &&
                        pendingFilters[filterKey].length > 0;

                      return (
                        <label
                          key={filterKey}
                          className="flex items-center gap-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded"
                        >
                          <input
                            type="checkbox"
                            checked={hasSelectedValues}
                            onChange={() => handleFilterToggle(filterKey)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                          />
                          <span className="flex items-center gap-2">
                            {filterLabels[filterKey] || filterKey}
                            {hasSelectedValues && (
                              <span className="bg-blue-100 text-blue-600 rounded-full px-1.5 py-0.5 text-[10px] font-semibold">
                                {pendingFilters[filterKey].length}
                              </span>
                            )}
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 px-4">
        <div className="flex items-center gap-2">
          <button
            onClick={handleClearAllClick}
            className="px-4 py-2 text-[#A4A7AE] text-sm font-inter font-medium disabled:opacity-50 cursor-pointer"
          >
            Clear all
          </button>
          <button
            onClick={handleApplyClick}
            className={`px-4 py-2 text-white rounded-md text-sm font-medium ${
              hasPendingChanges
                ? "bg-[#3E9900] hover:bg-green-700"
                : "bg-gray-400 cursor-not-allowed"
            }`}
            disabled={!hasPendingChanges}
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default FiltersPanel;
