const TableHeader = ({
  columns,
  css = "px-4 py-3 text-left font-semibold",
  noWrap,
}) => {
  return (
    <thead
      className={`bg-[#F1F4F9] text-sm text-[#475569] ${
        noWrap ? "text-nowrap " : ""
      }`}
    >
      <tr>
        {columns.map((col, index) => (
          <th key={index} className={css}>
            {col}
          </th>
        ))}
      </tr>
    </thead>
  );
};

export default TableHeader;
