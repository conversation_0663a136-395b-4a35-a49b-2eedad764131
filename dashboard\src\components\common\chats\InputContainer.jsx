import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { createChat, sendMessage } from "../../../services/operations/chatAPI";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";

const InputContainer = ({
  accountManager,
  jobId,
  setGetMessages,
  selectedRecruiter,
  setNewChatId,
  newChatId,
  submissionId = "", // Only for submission chats
}) => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [recruiterDetails, setRecruiterDetails] = useState({});
  const [chatId, setChatId] = useState("");

  const userType = useSelector((state) => state.auth?.user?.role);
  const userId = useSelector((state) => state.auth?.user?.id);

  let idChat = useSelector(
    (state) =>
      state.auth?.user?.profile?.jobsWorkingOn?.find(
        (job) => job.jobId === jobId
      )?.chatId || ""
  );

  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const currentTab = queryParams.get("tab");

  const createNewChatHandler = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    setIsSending(true);

    const participants =
      userType === "recruiter"
        ? [
            {
              userId,
              role: "recruiter",
            },
            {
              userId: accountManager?._id,
              role: "accountManager",
            },
          ]
        : [
            {
              userId: userId,
              role: "accountManager",
            },
            {
              userId: selectedRecruiter?._id,
              role: "recruiter",
            },
          ];

    const payload = {
      message: message.trim(),
      participants,
      chatFor: currentTab === "chat" ? "job" : "submission",
      jobId,
      recruiterId: userType === "recruiter" ? userId : selectedRecruiter?._id,
      submissionId: currentTab === "chat" ? null : submissionId,
    };

    try {
      const response = await createChat(payload);

      if (response?.success) {
        setMessage(""); // clear input on success
        setRecruiterDetails(response?.recruiter);
        setGetMessages(true); // trigger message fetch
        setNewChatId(response?.chat?._id);
      }
    } catch (error) {
      console.log("Error creating chat:", error);
      toast.error("Internal Server Error. Please try again later.");
    } finally {
      setIsSending(false);
    }
  };

  const sendMessageHandler = async (e) => {
    e.preventDefault();

    console.log("Sending message:", message);

    if (!message.trim() || isSending) return;

    setIsSending(true);

    const payload = {
      chatId,
      content: message.trim(),
      senderId: userId,
    };

    try {
      const response = await sendMessage(payload);
      if (response?.success) {
        setMessage(""); // clear input on success
        setGetMessages(true);
      }
    } catch (error) {
      console.log("Error sending message:", error);
      toast.error("Failed to send message. Please try again.");
    } finally {
      setIsSending(false);
    }
  };

  // Button should be disabled if sending OR message is empty
  const isButtonDisabled = isSending || !message.trim();

  useEffect(() => {
    const chatId =
      (userType !== "recruiter" &&
        selectedRecruiter?.profile?.jobsWorkingOn?.find(
          (job) => job.jobId === jobId
        )?.chatId) ||
      "";

    setChatId(chatId);
  }, [jobId, selectedRecruiter, userType]);

  useEffect(() => {
    if (newChatId) {
      setChatId(newChatId);
      return;
    }
    setChatId(idChat);
  }, [idChat, newChatId]);

  return (
    <div className="flex w-full p-6 items-center gap-6 shrink-0">
      <img
        src="/assets/icons/Iconsax.svg"
        alt="Attachment"
        className="w-6 h-6 shrink-0"
      />
      <form
        className="w-full flex-[1_0_0] flex relative"
        onSubmit={chatId ? sendMessageHandler : createNewChatHandler}
      >
        <input
          type="text"
          placeholder="Type a message"
          value={message}
          name="message"
          id="message"
          onChange={(e) => setMessage(e.target.value)}
          className="resize-none flex py-2.5 px-5 justify-between items-center rounded-xl border-2 w-full border-solid border-[#CBD4E1] bg-white"
        />
        <button
          disabled={isButtonDisabled}
          className={`absolute right-5 top-1/2 transform -translate-y-1/2 transition-opacity ${
            isButtonDisabled ? "opacity-50 cursor-not-allowed" : "opacity-100"
          }`}
        >
          <img src="/assets/icons/send.svg" alt="Send" className="w-6 h-full" />
        </button>
      </form>
    </div>
  );
};

export default InputContainer;
