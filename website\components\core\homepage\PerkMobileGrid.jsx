import React from "react";
import PerkCardMobile from "./PerkCardMobile";
import middlemanIcon from "@/public/assets/icons/homepage/middleman.svg";
import trainingIcon from "@/public/assets/icons/homepage/training.svg";
import resourcesIcon from "@/public/assets/icons/homepage/resources.svg";
import incentivesIcon from "@/public/assets/icons/homepage/incentives.svg";
import communityIcon from "@/public/assets/icons/homepage/community.svg";
import remoteIcon from "@/public/assets/icons/homepage/remote.svg";

const PerkMobileGrid = () => {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-6">
        <PerkCardMobile
          icon={middlemanIcon}
          title={"No Middlemen"}
          description={
            "Get direct access to clients and job openings, allowing you to work efficiently and earn more."
          }
        />
        <PerkCardMobile
          title={"Work From Anywhere"}
          description={
            "Get direct access to clients and job openings, allowing you to work efficiently and earn more."
          }
          icon={remoteIcon}
        />
      </div>
      <div className="flex gap-6">
        <PerkCardMobile
          title={"Supportive Community"}
          description={
            "Be part of a platform that fosters collaboration among top freelance recruiters, sharing tips, strategies, and growth opportunities."
          }
          icon={communityIcon}
        />

        <PerkCardMobile
          title={"Incentives for Top Performers"}
          description={
            "Our reward system includes exclusive bonuses and opportunities for top recruiters, motivating you to reach new heights."
          }
          icon={incentivesIcon}
        />
      </div>
      <div className="flex gap-6">
        <PerkCardMobile
          title={"Tailored Resources"}
          description={
            "From marketing materials to recruiting scripts, we provide you with everything you need to succeed."
          }
          icon={resourcesIcon}
        />
        <PerkCardMobile
          title={"Training & Development"}
          description={
            "Gain access to ongoing training resources and expert advice to keep you ahead of industry trends."
          }
          icon={trainingIcon}
        />
      </div>
    </div>
  );
};

export default PerkMobileGrid;
