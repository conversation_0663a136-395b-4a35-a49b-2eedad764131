import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { verifyEmail } from "../services/operations/authAPI";
import { useDispatch } from "react-redux";

const VerifyEmailPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [status, setStatus] = useState("verifying");

  useEffect(() => {
    const token = searchParams.get("token");
    console.log("Verification token:", token);

    const handleVerify = async () => {
      try {
        const response = await verifyEmail(token);
        setStatus("success");
        setTimeout(() => {
          navigate("/email-verified", {
            state: {
              accessToken: response.accessToken,
              refreshToken: response.refreshToken,
              user: response.userInfo,
            },
          });
        });
      } catch (error) {
        console.error("Email verification error:", error);
        setStatus("error");
      }
    };

    if (token) {
      handleVerify();
    } else {
      setStatus("error");
      navigate("/verification-failed");
    }
  }, [searchParams, navigate, dispatch]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      {status === "verifying" && <p>Verifying your email...</p>}
      {status === "success" && (
        <p className="text-green-600">
          Email verified successfully! Redirecting to dashboard...
        </p>
      )}
      {status === "error" && (
        <p className="text-red-600">Invalid or expired verification link.</p>
      )}
    </div>
  );
};

export default VerifyEmailPage;
