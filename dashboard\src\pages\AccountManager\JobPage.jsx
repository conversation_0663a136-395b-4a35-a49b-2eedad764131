import React, { useEffect, useState, useRef, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import countries from "../../data/countries.json";

// Components
import TabNav from "../../components/common/TabNav/TabNav";
import HighPriorityJobs from "../../components/common/Job/ManagerJobTables/HighPriorityJobs";
import ActiveJobs from "../../components/common/Job/ManagerJobTables/ActiveJobs";
import UnEngagedJobs from "../../components/common/Job/ManagerJobTables/UnEngagedJobs";
import AllJobs from "../../components/common/Job/ManagerJobTables/AllJobs";
import CloseJobs from "../../components/common/Job/ManagerJobTables/CloseJobs";
import EmptyState from "../../components/common/EmptyState/EmptyState";
import ThreeDotHorizontal from "../../components/common/Button/ThreeDotHorizontal";
import SearchDropdown from "../../components/common/filters/SearchDropdown";
import {
  getActiveJobs,
  getAllJobs,
  getCloseJobs,
  gethighperiorityJobs,
  getUnEngagedJobs,
  updateJobStatus,
} from "../../services/operations/jobAPI";

// SVG Icons for Filter/Sorting
const FilterSVG = () => (
  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
    <path
      d="M4 6h16M7 12h10M10 18h4"
      stroke="#22c55e"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

const SortingSVG = () => (
  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
    <path
      d="M8 9l4-4 4 4M16 15l-4 4-4-4"
      stroke="#22c55e"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

// --- Helpers to get initial filter/sort/search state from URL ---
function getInitialSelectedLocations() {
  const params = new URLSearchParams(window.location.search);
  return params.get("locations")?.split(",").filter(Boolean) || [];
}
function getInitialSelectedJobTypes() {
  const params = new URLSearchParams(window.location.search);
  return params.get("jobTypes")?.split(",").filter(Boolean) || [];
}
function getInitialSortOrder() {
  const params = new URLSearchParams(window.location.search);
  return params.get("sortOrder") || "recent";
}
function getInitialSearchText() {
  const params = new URLSearchParams(window.location.search);
  return params.get("search") || "";
}
function getInitialSearchField() {
  const params = new URLSearchParams(window.location.search);
  const fields = [
    "location",
    "jobType",
    "domain",
    "priority",
    "status",
    "jobTitle",
  ];
  return fields.find((field) => params.has(field)) || "";
}

function getInitialSelectedStatuses() {
  const params = new URLSearchParams(window.location.search);
  return params.get("statuses")?.split(",").filter(Boolean) || [];
}

function getInitialSelectedVisibilities() {
  const params = new URLSearchParams(window.location.search);
  return params.get("visibilities")?.split(",").filter(Boolean) || [];
}

const JobPages = () => {
  const { search, pathname } = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(search);

  // Initialize the active tab based on the "tabs" query-param (defaults to 0)
  const [active, setActive] = useState(() => {
    const tabFromUrl = Number(queryParams.get("tabs"));
    return isNaN(tabFromUrl) ? 0 : tabFromUrl;
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [jobData, setJobData] = useState({
    highPriority: { results: [], total: 0, totalPages: 1, page: 1 },
    activeJobs: { results: [], total: 0, totalPages: 1, page: 1 },
    unEngagedJobs: { results: [], total: 0, totalPages: 1, page: 1 },
    allJobs: { results: [], total: 0, totalPages: 1, page: 1 },
    closeJobs: { results: [], total: 0, totalPages: 1, page: 1 },
  });
  const [selectedJobs, setSelectedJobs] = useState({});
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [showSortingPanel, setShowSortingPanel] = useState(false);
  const [showLocationDropdown, setShowLocationDropdown] = useState(false);
  const [showJobTypeDropdown, setShowJobTypeDropdown] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showVisibilityDropdown, setShowVisibilityDropdown] = useState(false);
  const [locationSearch, setLocationSearch] = useState("");
  const [selectedLocations, setSelectedLocations] = useState(
    getInitialSelectedLocations()
  );
  const [selectedJobTypes, setSelectedJobTypes] = useState(
    getInitialSelectedJobTypes()
  );
  const [selectedStatus, setSelectedStatus] = useState(
    getInitialSelectedStatuses()
  );
  const [selectedVisibility, setSelectedVisibility] = useState(
    getInitialSelectedVisibilities()
  );
  const [showInlineFilterDropdown, setShowInlineFilterDropdown] =
    useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false); // Added
  const [inlineFilterJobType, setInlineFilterJobType] = useState(active !== 2);
  const [inlineFilterLocation, setInlineFilterLocation] = useState(true);
  const [inlineFilterStatus, setInlineFilterStatus] = useState(active === 2);
  const [inlineFilterVisibility, setInlineFilterVisibility] = useState(
    active === 2
  );
  const [sortOrder, setSortOrder] = useState(getInitialSortOrder());
  const [filtersAppliedTrigger, setFiltersAppliedTrigger] = useState(0);
  const [selectedSearchField, setSelectedSearchField] = useState(
    getInitialSearchField()
  );
  const [searchText, setSearchText] = useState(getInitialSearchText());
  const [multiSearchFilters, setMultiSearchFilters] = useState({});

  // --- Memoised objects for Active Jobs tab to keep stable references ---
  const activeTabFilters = useMemo(() => {
    const f = {};
    if (selectedLocations.length > 0) {
      f.location = selectedLocations.join(",");
    }
    if (selectedJobTypes.length > 0) {
      f.jobType = selectedJobTypes.map((j) => j.toLowerCase()).join(",");
    }
    return f;
  }, [selectedLocations, selectedJobTypes]);

  const activeTabSearch = useMemo(() => {
    const s = {};
    if (searchText && searchText.trim()) {
      s.searchTerm = searchText.trim();
      if (selectedSearchField && selectedSearchField !== "all") {
        s.searchField = selectedSearchField;
      }
    }
    return s;
  }, [searchText, selectedSearchField]);

  // const [filteredLocations, setFilteredLocations] = useState([]);
  // const [locationSearch, setLocationSearch] = useState("");

  const [pendingFilters, setPendingFilters] = useState({
    locations: [],
    jobTypes: [],
    statuses: [],
    visibilities: [],
    sortOrder: "recent",
    search: "",
    multiSearchFilters: {},
  });

  // --- Computed helpers to decide if the Apply button should be enabled ---
  const hasFilterSelection =
    pendingFilters.locations.length > 0 ||
    pendingFilters.jobTypes.length > 0 ||
    pendingFilters.statuses.length > 0 ||
    pendingFilters.visibilities.length > 0;

  const hasSortSelection =
    pendingFilters.sortOrder !== "" && pendingFilters.sortOrder !== "recent";

  // Create a mapping from country name to database-compatible format
  const countryMapping = countries.reduce((map, country) => {
    map[country.name] = country.name.toLowerCase().replace(/\s+/g, "-");
    return map;
  }, {});

  // Refs for click outside
  const searchBarRef = useRef(null);
  const searchDropdownTriggerRef = useRef(null);
  const locationDropdownRef = useRef(null);
  const locationDropdownTriggerRef = useRef(null);
  const jobTypeDropdownRef = useRef(null);
  const jobTypeDropdownTriggerRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const statusDropdownTriggerRef = useRef(null);
  const visibilityDropdownRef = useRef(null);
  const visibilityDropdownTriggerRef = useRef(null);
  const inlineFilterDropdownRef = useRef(null);
  const inlineFilterDropdownTriggerRef = useRef(null);
  const sortDropdownRef = useRef(null);
  const sortDropdownTriggerRef = useRef(null);
  const filterPanelRef = useRef(null);
  const sortingPanelRef = useRef(null);
  // Inline sort-field dropdown (e.g. "+ Sorting")
  const sortFieldDropdownTriggerRef = useRef(null);
  const sortFieldDropdownRef = useRef(null);

  // UI state for the inline sort selector
  const [showSortFieldDropdown, setShowSortFieldDropdown] = useState(false);

  // Tabs
  const tabs = [
    { name: <span>High Priority Jobs</span>, css: "" },
    { name: <span>Active Jobs</span>, css: "" },
    { name: <span>Un-engaged Jobs</span>, css: "" },
    { name: <span>All Jobs</span>, css: "" },
    { name: <span>Closed Jobs</span>, css: "" },
  ];

  const filtersPanelJustClosedRef = useRef(false);

  useEffect(() => {
    if (filtersPanelJustClosedRef.current) {
      setFiltersAppliedTrigger((prev) => prev + 1);
      filtersPanelJustClosedRef.current = false;
    }
  }, [currentPage]);
  // Initial tab navigation
  useEffect(() => {
    const fetchData = async () => {
      const fetcherMap = {
        0: getHighPriorityJob,
        1: getActiveJob,
        2: getUnEngagedJob,
        3: getAllJob, // ← This is the All Jobs tab we're fixing
        4: getCloseJob,
      };

      const dataSetterMap = {
        0: "highPriority",
        1: "activeJobs",
        2: "unEngagedJobs",
        3: "allJobs", // ← This is the All Jobs data we're updating
        4: "closeJobs",
      };

      if (fetcherMap[active]) {
        // ✅ Build filters only after Apply is triggered OR for initial load
        let filters = {};
        if (filtersAppliedTrigger !== 0 || active === 3) {
          // Always apply filters for All Jobs tab
          if (selectedLocations.length > 0)
            filters.locations = selectedLocations.join(",");
          if (selectedJobTypes.length > 0)
            filters.jobTypes = selectedJobTypes.join(",");

          // ✅ These are specific to All Jobs tab (tab index 3)
          if (active === 3) {
            if (selectedStatus.length > 0)
              filters.jobStatus = selectedStatus.join(",");
            if (selectedVisibility.length > 0)
              filters.visibility = selectedVisibility
                .map((v) => v.toLowerCase())
                .join(",");
          }

          if (sortOrder) filters.sortOrder = sortOrder;
          if (searchText) filters.search = searchText;
          if (selectedSearchField) filters.searchField = selectedSearchField;
        }

        // Pass filters to the fetcher function
        const data = await fetcherMap[active](currentPage, pageSize, filters);

        setJobData((prev) => ({ ...prev, [dataSetterMap[active]]: data }));
      }
    };

    fetchData();
  }, [
    active,
    currentPage,
    pageSize,
    filtersAppliedTrigger,
    selectedLocations,
    selectedJobTypes,
    selectedStatus,
    selectedVisibility,
    sortOrder,
    searchText,
    selectedSearchField,
  ]);
  // Notice `filtersAppliedTrigger` is the key here to trigger refetch

  // Update search options

  // Click outside handler
  useEffect(() => {
    function handleClickOutside(event) {
      const isInside = (...refs) =>
        refs.some((ref) => ref.current && ref.current.contains(event.target));
      if (!isInside(searchBarRef, searchDropdownTriggerRef)) {
        setShowSearchBar(false);
        // setSearchDropdown(false); // This line was removed as per the edit hint
      }
      if (
        showLocationDropdown &&
        !isInside(locationDropdownRef, locationDropdownTriggerRef)
      ) {
        setShowLocationDropdown(false);
        setLocationSearch(""); // Clear location search when dropdown is closed
      }
      if (
        showJobTypeDropdown &&
        !isInside(jobTypeDropdownRef, jobTypeDropdownTriggerRef)
      ) {
        setShowJobTypeDropdown(false);
      }
      if (
        showStatusDropdown &&
        !isInside(statusDropdownRef, statusDropdownTriggerRef)
      ) {
        setShowStatusDropdown(false);
      }
      if (
        showVisibilityDropdown &&
        !isInside(visibilityDropdownRef, visibilityDropdownTriggerRef)
      ) {
        setShowVisibilityDropdown(false);
      }
      if (
        showInlineFilterDropdown &&
        !isInside(inlineFilterDropdownRef, inlineFilterDropdownTriggerRef)
      ) {
        setShowInlineFilterDropdown(false);
      }
      if (
        showSortDropdown &&
        !isInside(sortDropdownRef, sortDropdownTriggerRef)
      ) {
        setShowSortDropdown(false);
      }
      if (
        showSortFieldDropdown &&
        !isInside(sortFieldDropdownRef, sortFieldDropdownTriggerRef)
      ) {
        setShowSortFieldDropdown(false);
      }
      if (
        showFilterPanel &&
        !isInside(
          filterPanelRef,
          locationDropdownRef,
          locationDropdownTriggerRef,
          jobTypeDropdownRef,
          jobTypeDropdownTriggerRef,
          statusDropdownRef,
          statusDropdownTriggerRef,
          visibilityDropdownRef,
          visibilityDropdownTriggerRef,
          inlineFilterDropdownRef,
          inlineFilterDropdownTriggerRef
        )
      ) {
        // Do not close the Filter panel automatically; it should stay open
        // and only be closed explicitly via "Clear all".
      }
      if (
        showSortingPanel &&
        !isInside(
          sortingPanelRef,
          sortDropdownRef,
          sortDropdownTriggerRef,
          sortFieldDropdownRef,
          sortFieldDropdownTriggerRef
        )
      ) {
        // Do not close the Sorting panel automatically; it should stay open
        // and only be closed explicitly via "Clear all".
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [
    showFilterPanel,
    showSortingPanel,
    showLocationDropdown,
    showJobTypeDropdown,
    showStatusDropdown,
    showVisibilityDropdown,
    showInlineFilterDropdown,
    showSortDropdown,
    showSortFieldDropdown, // Add this dependency
  ]);

  function updateTabs(tabIndex) {
    const params = new URLSearchParams(window.location.search);
    params.set("tabs", tabIndex);
    navigate(`${pathname}?${params.toString()}`, { replace: true });
    setActive(tabIndex);
    setCurrentPage(1);
  }

  const handleSearchSubmit = () => {
    try {
      // Build query params reflecting current filter/search state
      const params = new URLSearchParams();
      params.append("tabs", active.toString());

      if (selectedLocations.length > 0) {
        params.append("locations", selectedLocations.join(","));
      }
      if (selectedJobTypes.length > 0) {
        params.append("jobTypes", selectedJobTypes.join(","));
      }
      if (selectedStatus.length > 0) {
        params.append("statuses", selectedStatus.join(","));
      }
      if (selectedVisibility.length > 0) {
        params.append("visibilities", selectedVisibility.join(","));
      }
      if (sortOrder) {
        params.append("sortOrder", sortOrder);
      }
      // Global text search (when no specific field is selected)
      if (searchText && !selectedSearchField) {
        params.append("search", searchText);
      }
      // Field‐specific multi-search filters
      Object.entries(multiSearchFilters).forEach(([field, values]) => {
        if (Array.isArray(values) && values.length > 0) {
          params.append(field, values.join(","));
        }
      });

      // Reflect filters in the URL so that page refresh/share keeps state
      navigate(`${pathname}?${params.toString()}`, { replace: true });

      // Trigger data refetch in useEffect dependent on filtersAppliedTrigger
      setFiltersAppliedTrigger((prev) => prev + 1);
    } catch (error) {
      console.error("[JobPage] Error in handleSearchSubmit:", error);
    }
  };

  // --- Data Fetching Functions ---
  async function getHighPriorityJob(page = 1, limit = 10) {
    try {
      const filters = {};

      // 🟢 Locations (convert and join but don't double encode)
      if (selectedLocations.length > 0) {
        const normalizedLocations = selectedLocations
          .filter(Boolean)
          .map((loc) => {
            const trimmed = loc.trim();
            const mapped = countryMapping[trimmed];
            return mapped || trimmed.toLowerCase().replace(/\s+/g, "-");
          });
        filters.locations = normalizedLocations.join(",");
      }

      // 🟢 Job Types
      if (selectedJobTypes.length > 0) {
        filters.jobTypes = selectedJobTypes
          .map((j) => j.toLowerCase())
          .join(",");
      }

      // 🟢 Sort Order
      if (sortOrder) {
        filters.sortOrder = sortOrder;
      }

      // 🟢 Search and Field-specific Search
      if (searchText) {
        // For field-specific search
        if (selectedSearchField && selectedSearchField !== "all") {
          // Handle specific fields
          switch (selectedSearchField) {
            case "jobTitle":
              filters.search = searchText;
              filters.searchField = "jobTitle";
              break;
            case "jobId":
              filters.search = searchText;
              filters.searchField = "jobId";
              break;
            case "jobType":
              filters.search = searchText;
              filters.searchField = "jobType";
              break;
            case "submissionsCount":
              filters.search = searchText;
              filters.searchField = "submissionsCount";
              break;
            case "recruiterCount":
              filters.search = searchText;
              filters.searchField = "recruiterCount";
              break;
            case "location":
              // Keep existing location search logic
              filters.locations = searchText.toLowerCase().replace(/\s+/g, "-");
              break;
            default:
              // For 'all' or unspecified fields, do a general search
              filters.search = searchText;
          }
        } else {
          // For general search (all fields)
          filters.search = searchText;
        }
      }

      filters.page = page;
      filters.limit = limit;

      const response = await gethighperiorityJobs(page, limit, filters);

      if (!response?.success) throw new Error(response.message);

      const jobs =
        response.results?.map((item) => ({
          jobid: item.jobId,
          jobtitle: item.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          submissionsCount: item.submissionsCount || 0,
          recruiterCount: item.recruiterCount || 0,
        })) || [];

      return {
        results: jobs,
        total: response.total || 0,
        totalPages: response.totalPages || 1,
        page: response.page || 1,
      };
    } catch (error) {
      console.error("[getHighPriorityJob] Error:", error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getActiveJob(page = 1, limit = 10) {
    try {
      // --- Build query pieces for Active Jobs ---
      const filters = {};

      // 🟢 Location filter
      if (selectedLocations.length > 0) {
        const normalizedLocations = selectedLocations
          .filter(Boolean)
          .map((loc) => loc.trim()); // no hyphen conversion needed for backend
        filters.location = normalizedLocations.join(",");
      }

      // 🟢 Job-type filter
      if (selectedJobTypes.length > 0) {
        filters.jobType = selectedJobTypes
          .map((j) => j.toLowerCase())
          .join(",");
      }

      // 🟢 Sorting (posted date)
      const sorting = {};
      if (sortOrder) {
        // Backend expects `postedDate` value of 'recent' | 'oldest'
        sorting.postedDate = sortOrder;
      }

      // 🟢 Search
      const search = {};
      if (searchText) {
        search.searchTerm = searchText.trim();
        if (selectedSearchField && selectedSearchField !== "all") {
          search.searchField = selectedSearchField;
        }
      }

      const response = await getActiveJobs(
        page,
        limit,
        filters,
        sorting,
        search
      );

      if (!response?.success) throw new Error(response?.message);

      const jobs =
        response?.results?.map((item) => ({
          jobid: item?.jobId,
          // Ensure visibility column exists so that subsequent columns align correctly with the table header
          // Convert to boolean: true ⇒ Visible icon, false ⇒ Not-visible icon
          visibility:
            item?.visibility?.toString().toLowerCase() === "publish" ||
            item?.visibility === true,
          jobtitle: item.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item?.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          submissionsCount: item?.submissionsCount || 0,
          recruiterCount: item?.recruiterCount || 0,
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.error("[getActiveJob] Error:", error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  // eslint-disable-next-line no-unused-vars
  async function getUnEngagedJob(
    page = 1,
    limit = 10,
    filters = {},
    _unusedSorting,
    _unusedSearch
  ) {
    try {
      // --- Build Filters ---
      const finalFilters = { ...filters };

      // Prevent duplicate query params: if we will send search via `searchObj`, drop any existing
      delete finalFilters.search;
      delete finalFilters.searchField;

      // --- Normalize filter keys for backend expectations ---
      // Convert plural frontend keys to the singular ones the backend expects
      if (finalFilters.locations && !finalFilters.location) {
        finalFilters.location = finalFilters.locations;
      }
      if (finalFilters.jobTypes && !finalFilters.jobType) {
        finalFilters.jobType = finalFilters.jobTypes;
      }

      // Remove any keys that the backend does not recognise to keep the querystring clean
      delete finalFilters.locations;
      delete finalFilters.jobTypes;
      delete finalFilters.sortOrder;

      // Ensure location & jobType reflect current selections if not provided
      if (!finalFilters.location && selectedLocations.length > 0) {
        finalFilters.location = selectedLocations.join(",");
      }
      if (!finalFilters.jobType && selectedJobTypes.length > 0) {
        finalFilters.jobType = selectedJobTypes
          .map((j) => j.toLowerCase())
          .join(",");
      }

      // --- Sorting (postedDate) ---
      const sortObj =
        sortOrder && sortOrder !== "recent" ? { postedDate: sortOrder } : {};

      // --- Search params ---
      const searchObj = {};
      if (searchText && searchText.trim()) {
        searchObj.searchTerm = searchText.trim();
        if (selectedSearchField && selectedSearchField !== "all") {
          searchObj.searchField = selectedSearchField;
        }
      }

      const response = await getUnEngagedJobs(
        page,
        limit,
        finalFilters,
        sortObj,
        searchObj
      );

      if (!response?.success) throw new Error(response?.message);

      const jobs = (response?.results || []).map((item) => ({
        jobid: item?.jobId,
        jobtitle: item?.jobTitle,
        location: `${
          item?.location?.country
            ?.replace(/-/g, " ")
            .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
        }${item?.location?.state ? ", " + item?.location?.state : ""}`,
        jobType: item?.jobType
          ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
          : "",
        workOnRequestCount: item?.workOnRequestCount || 0,
      }));

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      if (error?.response) {
        console.error(
          "[getUnEngagedJob] Backend error response:",
          error.response.data
        );
      }
      console.error("[getUnEngagedJob] Error:", error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getAllJob(page = 1, limit = 10, _ignoredFilters = {}) {
    try {
      const finalFilters = {};
      void _ignoredFilters; // reference to avoid unused param warning

      // 🟢 Locations
      if (selectedLocations.length > 0) {
        const normalizedLocations = selectedLocations
          .filter(Boolean)
          .map((loc) => {
            const trimmed = loc.trim();
            const mapped = countryMapping[trimmed];
            return mapped || trimmed.toLowerCase().replace(/\s+/g, "-");
          });
        finalFilters.locations = normalizedLocations.join(",");
      }

      // 🟢 Job Types
      if (selectedJobTypes.length > 0) {
        finalFilters.jobTypes = selectedJobTypes
          .map((j) => j.toLowerCase())
          .join(",");
      }

      // 🟢 Status Filter (specific to All Jobs tab)
      if (selectedStatus.length > 0) {
        finalFilters.jobStatus = selectedStatus.join(",");
      }

      // 🟢 Visibility Filter (specific to All Jobs tab)
      if (selectedVisibility.length > 0) {
        finalFilters.visibility = selectedVisibility
          .map((v) => v.toLowerCase())
          .join(",");
      }

      // 🟢 Sort Order
      if (sortOrder) {
        finalFilters.sortOrder = sortOrder;
      }

      // 🟢 Search & Field-specific Search
      if (searchText) {
        if (selectedSearchField && selectedSearchField !== "all") {
          switch (selectedSearchField) {
            case "jobTitle":
              finalFilters.search = searchText;
              finalFilters.searchField = "jobTitle";
              break;
            case "jobId":
              finalFilters.search = searchText;
              finalFilters.searchField = "jobId";
              break;
            case "jobType":
              finalFilters.search = searchText;
              finalFilters.searchField = "jobType";
              break;
            case "location":
              finalFilters.locations = searchText
                .toLowerCase()
                .replace(/\s+/g, "-");
              break;
            default:
              finalFilters.search = searchText;
          }
        } else {
          finalFilters.search = searchText;
        }
      }

      finalFilters.page = page;
      finalFilters.limit = limit;

      const response = await getAllJobs(page, limit, finalFilters);

      if (!response?.success) throw new Error(response.message);

      const jobs =
        response?.results?.map((item) => ({
          _id: item?.jobId,
          visibility: item?.visibility,
          jobTitle: item.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item?.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          status: item?.jobStatus,
        })) || [];

      return {
        results: jobs,
        total: response?.total || 0,
        totalPages: response?.totalPages || 1,
        page: response?.page || 1,
      };
    } catch (error) {
      console.error("[getAllJob] Error:", error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function getCloseJob(page = 1, limit = 10) {
    try {
      const filters = {};

      // 🟢 Locations
      if (selectedLocations.length > 0) {
        const normalizedLocations = selectedLocations
          .filter(Boolean)
          .map((loc) => {
            const trimmed = loc.trim();
            const mapped = countryMapping[trimmed];
            return mapped || trimmed.toLowerCase().replace(/\s+/g, "-");
          });
        filters.locations = normalizedLocations.join(",");
      }

      // 🟢 Job Types
      if (selectedJobTypes.length > 0) {
        filters.jobTypes = selectedJobTypes
          .map((j) => j.toLowerCase())
          .join(",");
      }

      // 🟢 Sort Order
      if (sortOrder) {
        filters.sortOrder = sortOrder;
      }

      // 🟢 Search & Field-specific Search
      if (searchText) {
        if (selectedSearchField && selectedSearchField !== "all") {
          switch (selectedSearchField) {
            case "jobTitle":
              filters.search = searchText;
              filters.searchField = "jobTitle";
              break;
            case "jobId":
              filters.search = searchText;
              filters.searchField = "jobId";
              break;
            case "jobType":
              filters.search = searchText;
              filters.searchField = "jobType";
              break;
            case "submissionsCount":
              filters.search = searchText;
              filters.searchField = "submissionsCount";
              break;
            case "recruiterCount":
              filters.search = searchText;
              filters.searchField = "recruiterCount";
              break;
            case "location":
              filters.locations = searchText.toLowerCase().replace(/\s+/g, "-");
              break;
            default:
              filters.search = searchText;
          }
        } else {
          filters.search = searchText;
        }
      }

      filters.page = page;
      filters.limit = limit;

      const response = await getCloseJobs(page, limit, filters);

      if (!response?.success) throw new Error(response.message);

      const jobs =
        response.results?.map((item) => ({
          jobid: item.jobId,
          jobtitle: item.jobTitle,
          location: `${
            item?.location?.country
              ?.replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase()) || ""
          }${item?.location?.state ? ", " + item?.location?.state : ""}`,
          jobType: item.jobType
            ? item.jobType.charAt(0).toUpperCase() + item.jobType.slice(1)
            : "",
          submissionsCount: item.submissionsCount || 0,
          recruiterCount: item.recruiterCount || 0,
          status: item.jobStatus,
        })) || [];

      return {
        results: jobs,
        total: response.total || 0,
        totalPages: response.totalPages || 1,
        page: response.page || 1,
      };
    } catch (error) {
      console.error("[getCloseJob] Error:", error);
      return { results: [], total: 0, totalPages: 1, page: 1 };
    }
  }

  async function updateStatus(jobID, status) {
    try {
      await updateJobStatus({ jobID, status });
    } catch (error) {
      console.log(error);
    }
  }

  // --- Filter & Sorting States ---
  const statusOptions = [
    "Active",
    "Inactive",
    "On Hold",
    "Hold by Client",
    "Filled",
    "Cancelled",
    "Closed",
  ];
  const visibilityOptions = ["Publish", "Unpublish"];

  // --- Location Filter State ---
  const [filteredLocations, setFilteredLocations] = useState([]);

  // --- Location Search Filtering ---
  useEffect(() => {
    if (locationSearch.length > 0) {
      const filtered = countries
        .filter((country) =>
          country.name.toLowerCase().includes(locationSearch.toLowerCase())
        )
        .map((country) => country.name);
      setFilteredLocations(filtered);
    } else {
      setFilteredLocations([]);
    }
  }, [locationSearch]);

  // --- Clear location search when dropdown closes ---
  useEffect(() => {
    if (!showLocationDropdown) {
      setLocationSearch("");
    }
  }, [showLocationDropdown]);

  // --- Filter Handlers ---
  const handleToggleLocation = (loc) => {
    setPendingFilters((prev) => ({
      ...prev,
      locations: prev.locations.includes(loc)
        ? prev.locations.filter((l) => l !== loc)
        : [...prev.locations, loc],
    }));
    // Don't trigger API call here
  };

  const handleRemoveLocation = (loc) => {
    setPendingFilters((prev) => ({
      ...prev,
      locations: prev.locations.filter((l) => l !== loc),
    }));
  };

  const handleToggleJobType = (type) => {
    setPendingFilters((prev) => ({
      ...prev,
      jobTypes: prev.jobTypes.includes(type)
        ? prev.jobTypes.filter((t) => t !== type)
        : [...prev.jobTypes, type],
    }));
    // Don't trigger API call here
  };

  const handleToggleStatus = (status) => {
    setPendingFilters((prev) => ({
      ...prev,
      statuses: prev.statuses.includes(status)
        ? prev.statuses.filter((s) => s !== status)
        : [...prev.statuses, status],
    }));
  };

  const handleToggleVisibility = (visibility) => {
    setPendingFilters((prev) => ({
      ...prev,
      visibilities: prev.visibilities.includes(visibility)
        ? prev.visibilities.filter((v) => v !== visibility)
        : [...prev.visibilities, visibility],
    }));
  };

  const handleClearFilters = () => {
    // Reset pending filters
    setPendingFilters({
      locations: [],
      jobTypes: [],
      statuses: [],
      visibilities: [],
      sortOrder: "recent",
      search: "",
      multiSearchFilters: {},
    });

    // Reset actual filter state
    setSelectedLocations([]);
    setSelectedJobTypes([]);
    setSelectedStatus([]);
    setSelectedVisibility([]);
    setSortOrder("recent");
    setSearchText("");
    setSelectedSearchField("");
    setMultiSearchFilters({});

    // Reset URL to just the active tab (without filters)
    navigate(`${pathname}?tabs=${active}`, { replace: true });

    // Reset to first page
    setCurrentPage(1);

    // Close filter/sorting panels
    setShowFilterPanel(false);
    setShowSortingPanel(false);

    // Trigger refetch without any filters applied
    setFiltersAppliedTrigger((prev) => prev + 1); // This triggers the refetch after clearing filters
  };

  const handleApplyAllFilters = () => {
    setSelectedLocations(pendingFilters.locations);
    setSelectedJobTypes(pendingFilters.jobTypes);
    setSelectedStatus(pendingFilters.statuses);
    setSelectedVisibility(pendingFilters.visibilities);
    setSortOrder(pendingFilters.sortOrder);
    setSearchText(pendingFilters.search);
    setMultiSearchFilters(pendingFilters.multiSearchFilters);

    // update query params
    const params = new URLSearchParams();
    params.append("tabs", active.toString());
    if (pendingFilters.locations.length > 0)
      params.append("locations", pendingFilters.locations.join(","));
    if (pendingFilters.jobTypes.length > 0)
      params.append("jobTypes", pendingFilters.jobTypes.join(","));
    if (pendingFilters.statuses && pendingFilters.statuses.length > 0)
      params.append("statuses", pendingFilters.statuses.join(","));
    if (pendingFilters.visibilities && pendingFilters.visibilities.length > 0)
      params.append("visibilities", pendingFilters.visibilities.join(","));
    if (pendingFilters.sortOrder)
      params.append("sortOrder", pendingFilters.sortOrder);
    if (pendingFilters.search) params.append("search", pendingFilters.search);
    Object.entries(pendingFilters.multiSearchFilters).forEach(
      ([field, values]) => {
        if (Array.isArray(values) && values.length > 0)
          params.append(field, values.join(","));
      }
    );

    navigate(`${pathname}?${params.toString()}`, { replace: true });

    // page reset + trigger fetch
    setCurrentPage(1);
    filtersPanelJustClosedRef.current = true;
    setFiltersAppliedTrigger((prev) => prev + 1);
  };

  // Initialize filters from URL
  useEffect(() => {
    const initialFilters = {};
    const fields = [
      "location",
      "jobType",
      "domain",
      "priority",
      "status",
      "jobTitle",
    ];
    const params = new URLSearchParams(window.location.search);
    fields.forEach((field) => {
      const val = params.get(field);
      if (val) initialFilters[field] = val.split(",").filter(Boolean);
    });
    setMultiSearchFilters(initialFilters);
    setSearchText(params.get("search") || "");
  }, []);

  // Add this effect after other useEffects
  useEffect(() => {
    // Initialize pending filters from URL params
    const params = new URLSearchParams(window.location.search);
    setPendingFilters({
      locations: params.get("locations")?.split(",").filter(Boolean) || [],
      jobTypes: params.get("jobTypes")?.split(",").filter(Boolean) || [],
      statuses: params.get("statuses")?.split(",").filter(Boolean) || [],
      visibilities:
        params.get("visibilities")?.split(",").filter(Boolean) || [],
      sortOrder: params.get("sortOrder") || "recent",
      search: params.get("search") || "",
      multiSearchFilters: (() => {
        const filters = {};
        [
          "location",
          "jobType",
          "domain",
          "priority",
          "status",
          "jobTitle",
        ].forEach((field) => {
          const val = params.get(field);
          if (val) filters[field] = val.split(",").filter(Boolean);
        });
        return filters;
      })(),
    });
  }, []);

  // Restore only the panel that was open before refresh using localStorage
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const lastOpenPanel = localStorage.getItem("lastOpenPanel"); // "filter" | "sorting" | null

    const filterKeys = [
      "locations",
      "jobTypes",
      "search",
      "location",
      "jobType",
      "domain",
      "priority",
      "status",
      "jobTitle",
    ];
    const hasFilterParams = filterKeys.some((key) => params.get(key));
    const hasSortingParam = !!params.get("sortOrder");

    if (lastOpenPanel === "filter" && hasFilterParams) {
      setShowFilterPanel(true);
    } else if (lastOpenPanel === "sorting" && hasSortingParam) {
      setShowSortingPanel(true);
    }
    // If neither matches, keep panels closed by default.
  }, []);

  // Persist which panel is currently open so that we can restore it on refresh
  useEffect(() => {
    if (showFilterPanel && !showSortingPanel) {
      localStorage.setItem("lastOpenPanel", "filter");
    } else if (showSortingPanel && !showFilterPanel) {
      localStorage.setItem("lastOpenPanel", "sorting");
    } else if (!showFilterPanel && !showSortingPanel) {
      localStorage.removeItem("lastOpenPanel");
    }
  }, [showFilterPanel, showSortingPanel]);

  // Debounced search

  const getPaginationProps = () => {
    const props = {
      currentPage,
      totalPages:
        jobData[
          [
            "highPriority",
            "activeJobs",
            "unEngagedJobs",
            "allJobs",
            "closeJobs",
          ][active]
        ].totalPages,
      pageSize,
      setPageSize,
      setCurrentPage,
      totalResults:
        jobData[
          [
            "highPriority",
            "activeJobs",
            "unEngagedJobs",
            "allJobs",
            "closeJobs",
          ][active]
        ].total,
    };
    return props;
  };

  const closeAllDropdowns = (except) => {
    setShowLocationDropdown(except === "location");
    setShowJobTypeDropdown(except === "jobType");
    setShowStatusDropdown(except === "status");
    setShowVisibilityDropdown(except === "visibility");
    setShowInlineFilterDropdown(except === "inline");
  };

  const handleSortOrderChange = (order) => {
    setPendingFilters((prev) => ({
      ...prev,
      sortOrder: prev.sortOrder === order ? "" : order, // toggle behaviour
    }));
  };

  // Add useEffect to reset inline filter toggles whenever tab changes
  useEffect(() => {
    // Reset toggles based on tab context
    if (active === 2) {
      // Un-engaged Jobs ➜ Location & Job Type
      setInlineFilterJobType(true);
      setInlineFilterStatus(false);
      setInlineFilterVisibility(false);
    } else if (active === 3) {
      // All Jobs ➜ Location, Status, Visibility
      setInlineFilterJobType(false);
      setInlineFilterStatus(true);
      setInlineFilterVisibility(true);
    } else {
      // Other tabs (default)
      setInlineFilterJobType(true);
      setInlineFilterStatus(false);
      setInlineFilterVisibility(false);
    }
    // Always keep Location toggle enabled by default
    setInlineFilterLocation(true);
  }, [active]);

  return (
    <div className="flex flex-col min-h-screen">
      <TabNav
        nav={tabs}
        active={active}
        setActive={updateTabs}
        rightSidebar={
          <div className="flex gap-2 items-center">
            <div className="relative" ref={searchDropdownTriggerRef}>
              <img
                src={"/assets/icons/search.svg"}
                alt="Search"
                className="w-8 h-8 cursor-pointer"
                onClick={() => {
                  setShowSearchBar((prev) => !prev);
                  setShowFilterPanel(false);
                  setShowSortingPanel(false);
                  closeAllDropdowns();
                }}
              />
              {/* Wrapper ensures clicks inside dropdown are recognised as internal */}
              <div ref={searchBarRef}>
                <SearchDropdown
                  isOpen={showSearchBar}
                  onClose={() => setShowSearchBar(false)}
                  onSearch={(searchInputs) => {
                    if (searchInputs.searchField === "all") {
                      setSearchText(searchInputs.searchTerm);
                      setSelectedSearchField("");
                    } else {
                      setSelectedSearchField(searchInputs.searchField);
                      setSearchText(searchInputs.searchTerm);
                      setMultiSearchFilters((prev) => ({
                        ...prev,
                        [searchInputs.searchField]: [searchInputs.searchTerm],
                      }));
                    }
                    handleSearchSubmit();
                  }}
                  activeTab={active}
                />
              </div>
            </div>
            <div className="relative">
              <ThreeDotHorizontal
                dropdownSize="w-56"
                buttonDropDown={(closeDropdown) => (
                  <div className="rounded-lg shadow-md border bg-white">
                    <div className="flex items-center justify-between px-4 pt-3 pb-2 border-b">
                      <span className="font-medium text-base text-gray-900">
                        View Option
                      </span>
                      <button
                        className="text-gray-400 hover:text-black text-xl leading-none cursor-pointer"
                        onClick={closeDropdown}
                        aria-label="Close"
                      >
                        ×
                      </button>
                    </div>
                    <div className="flex flex-col py-2">
                      <button
                        onClick={() => {
                          setShowFilterPanel(true);
                          setShowSortingPanel(false);
                          closeDropdown();
                        }}
                        className="flex items-center gap-2 px-4 py-2 text-black hover:text-green-600 hover:bg-gray-100 text-sm font-medium text-left w-full transition-colors cursor-pointer"
                      >
                        <FilterSVG />
                        Filter
                      </button>
                      <button
                        onClick={() => {
                          setShowSortingPanel(true);
                          setShowFilterPanel(false);
                          closeDropdown();
                        }}
                        className="flex items-center gap-2 px-4 py-2 text-black hover:text-green-600 hover:bg-gray-100 text-sm font-medium text-left w-full transition-colors cursor-pointer"
                      >
                        <SortingSVG />
                        Sorting
                      </button>
                    </div>
                  </div>
                )}
              />
            </div>
          </div>
        }
      />
      {showFilterPanel && (
        <div className="px-4 mt-2" ref={filterPanelRef}>
          <div className="bg-white/80 shadow-none rounded-md p-4 text-sm">
            <div className="flex items-center gap-4 mb-4">
              {inlineFilterLocation && (
                <div
                  className="relative inline-block"
                  ref={locationDropdownTriggerRef}
                >
                  <button
                    className="border border-blue-200 text-blue-500 bg-white rounded-full px-3 py-1 flex items-center gap-2 min-w-[180px] text-xs font-medium shadow-sm hover:bg-blue-50"
                    onClick={() => {
                      setShowLocationDropdown((prev) => !prev);
                      setShowJobTypeDropdown(false);
                      setShowInlineFilterDropdown(false);
                      setShowStatusDropdown(false);
                      setShowVisibilityDropdown(false);
                    }}
                    type="button"
                  >
                    Location
                    {pendingFilters.locations.length > 0
                      ? ` : ${pendingFilters.locations.join(", ")}`
                      : ""}
                    <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                      <path
                        d="M6 9l6 6 6-6"
                        stroke="#3b82f6"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  </button>
                  {showLocationDropdown && (
                    <div
                      ref={locationDropdownRef}
                      className="absolute left-0 mt-2 w-[400px] bg-white border rounded shadow-lg z-40 p-4"
                      style={{
                        boxShadow: "0 4px 16px rgba(0,0,0,0.08)",
                        border: "1px solid #e5e7eb",
                      }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-700">
                          Location
                        </span>
                        <button
                          className="text-gray-400 hover:text-black text-lg"
                          onClick={() => setShowLocationDropdown(false)}
                          aria-label="Close"
                        >
                          ×
                        </button>
                      </div>
                      <input
                        type="text"
                        className="border border-gray-300 rounded px-2 py-1 w-full mb-2"
                        placeholder="Type to search..."
                        value={locationSearch}
                        onChange={(e) => setLocationSearch(e.target.value)}
                        style={{ fontSize: "15px", height: "36px" }}
                      />
                      {/* Pills for selected locations, shown just below the search bar */}
                      {pendingFilters.locations.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-2">
                          {pendingFilters.locations.map((loc, idx) => (
                            <span
                              key={loc + idx}
                              className="bg-sky-100 text-sky-700 px-3 py-1 rounded-full text-xs flex items-center gap-1"
                              style={{ borderRadius: "9999px" }}
                            >
                              {loc}
                              <button
                                onClick={() => handleRemoveLocation(loc)}
                                className="text-gray-400 hover:text-black"
                                style={{
                                  marginLeft: "4px",
                                  fontWeight: "bold",
                                  fontSize: "14px",
                                }}
                              >
                                ×
                              </button>
                            </span>
                          ))}
                        </div>
                      )}
                      <div className="grid grid-cols-2 gap-x-4 max-h-40 overflow-y-auto">
                        {locationSearch.length === 0 ? (
                          <div className="col-span-2 text-gray-400 text-center py-4 select-none">
                            Start typing to search countries...
                          </div>
                        ) : filteredLocations.length === 0 ? (
                          <div className="col-span-2 text-gray-400 text-center py-4 select-none">
                            No countries found.
                          </div>
                        ) : (
                          filteredLocations.map((loc, idx) => (
                            <label
                              key={loc + idx}
                              className="flex items-center gap-2 py-1 cursor-pointer hover:bg-gray-100 rounded px-2"
                            >
                              <input
                                type="checkbox"
                                checked={pendingFilters.locations.includes(loc)}
                                onChange={() => handleToggleLocation(loc)}
                              />
                              <span>{loc}</span>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              {(active === 0 || active === 1 || active === 2 || active === 4) &&
                inlineFilterJobType && (
                  <div
                    className="relative inline-block"
                    ref={jobTypeDropdownTriggerRef}
                  >
                    <button
                      className="border border-blue-200 text-blue-500 bg-white rounded-full px-3 py-1 flex items-center gap-2 min-w-[180px] text-xs font-medium shadow-sm hover:bg-blue-50"
                      onClick={() => {
                        setShowJobTypeDropdown((prev) => !prev);
                        setShowLocationDropdown(false);
                        setShowInlineFilterDropdown(false);
                        setShowStatusDropdown(false);
                        setShowVisibilityDropdown(false);
                      }}
                      type="button"
                    >
                      Job Type
                      {pendingFilters.jobTypes.length > 0
                        ? ` : ${pendingFilters.jobTypes.join(", ")}`
                        : ""}
                      <svg
                        width="16"
                        height="16"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M6 9l6 6 6-6"
                          stroke="#3b82f6"
                          strokeWidth="2"
                          strokeLinecap="round"
                        />
                      </svg>
                    </button>
                    {showJobTypeDropdown && (
                      <div
                        ref={jobTypeDropdownRef}
                        className="absolute left-0 mt-2 w-48 bg-white border rounded shadow-lg z-40 p-3"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-gray-700">
                            Job Type
                          </span>
                          <button
                            className="text-gray-400 hover:text-black text-lg"
                            onClick={() => setShowJobTypeDropdown(false)}
                            aria-label="Close"
                          >
                            ×
                          </button>
                        </div>
                        <div className="flex flex-col gap-2">
                          {[
                            "Full-time",
                            "Part-time",
                            "Contract",
                            "Internship",
                          ].map((type) => (
                            <label
                              key={type}
                              className="flex items-center gap-2 cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={pendingFilters.jobTypes.includes(type)}
                                onChange={() => handleToggleJobType(type)}
                              />
                              {type}
                            </label>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              {inlineFilterStatus && (
                <div
                  className="relative inline-block"
                  ref={statusDropdownTriggerRef}
                >
                  <button
                    className="border border-blue-200 text-blue-500 bg-white rounded-full px-3 py-1 flex items-center gap-2 min-w-[180px] text-xs font-medium shadow-sm hover:bg-blue-50"
                    onClick={() => {
                      setShowStatusDropdown((prev) => !prev);
                      setShowLocationDropdown(false);
                      setShowJobTypeDropdown(false);
                      setShowVisibilityDropdown(false);
                      setShowInlineFilterDropdown(false);
                    }}
                    type="button"
                  >
                    Status
                    {pendingFilters.statuses.length > 0
                      ? ` : ${pendingFilters.statuses.join(", ")}`
                      : ""}
                    <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                      <path
                        d="M6 9l6 6 6-6"
                        stroke="#3b82f6"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  </button>
                  {showStatusDropdown && (
                    <div
                      ref={statusDropdownRef}
                      className="absolute left-0 mt-2 w-48 bg-white border rounded shadow-lg z-40 p-3"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-700">
                          Status
                        </span>
                        <button
                          className="text-gray-400 hover:text-black text-lg"
                          onClick={() => setShowStatusDropdown(false)}
                          aria-label="Close"
                        >
                          ×
                        </button>
                      </div>
                      <div className="flex flex-col gap-2">
                        {statusOptions.map((option) => (
                          <label
                            key={option}
                            className="flex items-center gap-2 cursor-pointer hover:bg-gray-100 rounded px-2 py-1"
                          >
                            <input
                              type="checkbox"
                              checked={pendingFilters.statuses.includes(option)}
                              onChange={() => handleToggleStatus(option)}
                            />
                            {option}
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
              {inlineFilterVisibility && (
                <div
                  className="relative inline-block"
                  ref={visibilityDropdownTriggerRef}
                >
                  <button
                    className="border border-blue-200 text-blue-500 bg-white rounded-full px-3 py-1 flex items-center gap-2 min-w-[180px] text-xs font-medium shadow-sm hover:bg-blue-50"
                    onClick={() => {
                      setShowVisibilityDropdown((prev) => !prev);
                      setShowLocationDropdown(false);
                      setShowJobTypeDropdown(false);
                      setShowStatusDropdown(false);
                      setShowInlineFilterDropdown(false);
                    }}
                    type="button"
                  >
                    Visibility
                    {pendingFilters.visibilities.length > 0
                      ? ` : ${pendingFilters.visibilities.join(", ")}`
                      : ""}
                    <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                      <path
                        d="M6 9l6 6 6-6"
                        stroke="#3b82f6"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  </button>
                  {showVisibilityDropdown && (
                    <div
                      ref={visibilityDropdownRef}
                      className="absolute left-0 mt-2 w-48 bg-white border rounded shadow-lg z-40 p-3"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-700">
                          Visibility
                        </span>
                        <button
                          className="text-gray-400 hover:text-black text-lg"
                          onClick={() => setShowVisibilityDropdown(false)}
                          aria-label="Close"
                        >
                          ×
                        </button>
                      </div>
                      <div className="flex flex-col gap-2">
                        {visibilityOptions.map((option) => (
                          <label
                            key={option}
                            className="flex items-center gap-2 cursor-.pointer hover:bg-gray-100 rounded px-2 py-1"
                          >
                            <input
                              type="checkbox"
                              checked={pendingFilters.visibilities.includes(
                                option
                              )}
                              onChange={() => handleToggleVisibility(option)}
                            />
                            {option}
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="relative" ref={inlineFilterDropdownTriggerRef}>
                <button
                  className="text-gray-400 text-xs font-medium px-2 py-1 rounded hover:bg-gray-100 flex items-center gap-1 border border-gray-200"
                  onClick={() => {
                    setShowInlineFilterDropdown((prev) => !prev);
                    setShowLocationDropdown(false);
                    setShowJobTypeDropdown(false);
                    setShowStatusDropdown(false);
                    setShowVisibilityDropdown(false);
                  }}
                  type="button"
                >
                  {(() => {
                    // Compute count based on how many individual filter values are currently selected
                    let count = 0;
                    count += pendingFilters.locations.length;
                    count += pendingFilters.jobTypes.length;
                    count += pendingFilters.statuses.length;
                    count += pendingFilters.visibilities.length;
                    if (count === 0) {
                      return "+ Filter";
                    }
                    // Render badge with count inside a blue circle
                    return (
                      <span className="flex items-center gap-1">
                        Filter
                        <span className="bg-blue-100 text-blue-600 rounded-full px-1.5 py-0.5 text-[11px] font-semibold">
                          {count}
                        </span>
                      </span>
                    );
                  })()}
                  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
                    <path
                      d="M6 9l6 6 6-6"
                      stroke="#888"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </button>
                {showInlineFilterDropdown && (
                  <div
                    ref={inlineFilterDropdownRef}
                    className="absolute left-0 mt-2 w-56 bg-white border rounded shadow-lg z-40 p-3"
                  >
                    <div className="flex flex-col gap-2">
                      {active !== 3 && (
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={pendingFilters.jobTypes.length > 0}
                            onChange={() => {
                              if (pendingFilters.jobTypes.length > 0) {
                                // Clear selected job types but keep the category visible
                                setPendingFilters((p) => ({
                                  ...p,
                                  jobTypes: [],
                                }));
                                setSelectedJobTypes([]);
                              }
                            }}
                          />
                          Job Type
                        </label>
                      )}
                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={pendingFilters.locations.length > 0}
                          onChange={() => {
                            if (pendingFilters.locations.length > 0) {
                              setSelectedLocations([]);
                              setPendingFilters((p) => ({
                                ...p,
                                locations: [],
                              }));
                            }
                          }}
                        />
                        Location
                      </label>
                      {active === 3 && (
                        <>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={pendingFilters.statuses.length > 0}
                              onChange={() => {
                                if (pendingFilters.statuses.length > 0) {
                                  setSelectedStatus([]);
                                  setPendingFilters((p) => ({
                                    ...p,
                                    statuses: [],
                                  }));
                                }
                              }}
                            />
                            Status
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={pendingFilters.visibilities.length > 0}
                              onChange={() => {
                                if (pendingFilters.visibilities.length > 0) {
                                  setSelectedVisibility([]);
                                  setPendingFilters((p) => ({
                                    ...p,
                                    visibilities: [],
                                  }));
                                }
                              }}
                            />
                            Visibility
                          </label>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-2 flex justify-end space-x-2">
              <button
                onClick={handleClearFilters}
                className="px-4 py-1 rounded hover:bg-gray-100 cursor-pointer text-gray-500 border border-gray-200"
              >
                Clear all
              </button>
              <button
                disabled={!hasFilterSelection}
                className={`px-4 py-1 rounded transition-colors ${
                  hasFilterSelection
                    ? "bg-green-500 text-white hover:bg-green-600 cursor-pointer"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
                onClick={handleApplyAllFilters}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
      {showSortingPanel && (
        <div className="px-4 mt-2" ref={sortingPanelRef}>
          <div
            className="bg-white/80 shadow-none rounded-md p-4 text-sm"
            style={{ boxShadow: "none", border: "none" }}
          >
            <div className="flex items-center gap-4 mb-2">
              <div className="relative" ref={sortDropdownTriggerRef}>
                <button
                  className={`border border-blue-200 text-blue-500 bg-white rounded-full px-3 py-1 flex items-center gap-2 text-xs font-medium shadow-sm ${
                    showSortDropdown ? "ring-2 ring-blue-200" : ""
                  }`}
                  onClick={() => {
                    setShowSortDropdown((prev) => !prev);
                  }}
                  type="button"
                >
                  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
                    <path
                      d="M4 6h16"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                    <path
                      d="M7 12h10"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                    <path
                      d="M10 18h4"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                  Posted Date:{" "}
                  {sortOrder === "recent"
                    ? "Recent"
                    : sortOrder === "oldest"
                    ? "Oldest"
                    : "Select"}
                  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
                    <path
                      d="M6 9l6 6 6-6"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </button>
                {showSortDropdown && (
                  <div
                    ref={sortDropdownRef}
                    className="absolute left-0 mt-2 w-48 bg-white border rounded shadow-lg z-40 p-3"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-700">
                        Posted Date
                      </span>
                      <button
                        className="text-gray-400 hover:text-black text-lg"
                        onClick={() => setShowSortDropdown(false)}
                        aria-label="Close"
                      >
                        ×
                      </button>
                    </div>
                    <div className="flex flex-col gap-2">
                      <label className="flex items-center gap-2 cursor-pointer select-none">
                        <input
                          type="checkbox"
                          checked={pendingFilters.sortOrder === "recent"}
                          onChange={() => handleSortOrderChange("recent")}
                        />
                        Recent
                      </label>
                      <label className="flex items-center gap-2 cursor-pointer select-none">
                        <input
                          type="checkbox"
                          checked={pendingFilters.sortOrder === "oldest"}
                          onChange={() => handleSortOrderChange("oldest")}
                        />
                        Oldest
                      </label>
                    </div>
                  </div>
                )}
              </div>
              {/* + Sorting inline button */}
              <div className="relative">
                <button
                  ref={sortFieldDropdownTriggerRef}
                  className="text-gray-400 text-xs font-medium px-2 py-1 rounded hover:bg-gray-100 flex items-center gap-1 border border-gray-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSortFieldDropdown((prev) => !prev);
                    // ensure the main sort dropdown closes
                    setShowSortDropdown(false);
                  }}
                  type="button"
                >
                  {`+ Sorting (+1)`}
                  <svg width="14" height="14" fill="none" viewBox="0 0 24 24">
                    <path
                      d="M6 9l6 6 6-6"
                      stroke="#888"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </button>
                {showSortFieldDropdown && (
                  <div
                    ref={sortFieldDropdownRef}
                    className="absolute left-0 mt-2 w-48 bg-white border rounded shadow-lg z-40 p-3"
                  >
                    <span className="block text-gray-700 text-sm mb-1">
                      Sort By
                    </span>
                    <label className="flex items-center gap-2 cursor-pointer select-none">
                      <input type="checkbox" checked readOnly />
                      Posted Date
                    </label>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-2 flex justify-end space-x-2">
              <button
                onClick={handleClearFilters}
                className="px-4 py-1 rounded hover:bg-gray-100 cursor-pointer text-gray-500 border border-gray-200"
              >
                Clear all
              </button>
              <button
                disabled={!hasSortSelection}
                className={`px-4 py-1 rounded transition-colors ${
                  hasSortSelection
                    ? "bg-green-500 text-white hover:bg-green-600 cursor-pointer"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
                onClick={handleApplyAllFilters}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="flex-1 overflow-x-auto rounded-lg mt-2 border border-gray-200 shadow-sm job-table-container">
        {active === 0 && (
          <>
            {jobData.highPriority.results.length === 0 ? (
              <EmptyState />
            ) : (
              <HighPriorityJobs
                getJob={getHighPriorityJob}
                paginationProps={getPaginationProps()}
                jobs={jobData.highPriority.results}
              />
            )}
          </>
        )}
        {active === 1 && (
          <>
            {jobData.activeJobs.results.length === 0 ? (
              <EmptyState />
            ) : (
              <ActiveJobs
                getJob={getActiveJob}
                paginationProps={getPaginationProps()}
                jobs={jobData.activeJobs.results}
                filters={activeTabFilters}
                sorting={sortOrder}
                search={activeTabSearch}
                setSelectedJobs={setSelectedJobs}
                selectedJobs={selectedJobs}
              />
            )}
          </>
        )}
        {active === 2 && (
          <>
            {jobData.unEngagedJobs.results.length === 0 ? (
              <EmptyState />
            ) : (
              <UnEngagedJobs
                getJob={getUnEngagedJob}
                filters={activeTabFilters}
                sorting={sortOrder}
                search={activeTabSearch}
                paginationProps={getPaginationProps()}
                jobs={jobData.unEngagedJobs.results}
                setSelectedJobs={setSelectedJobs}
                selectedJobs={selectedJobs}
              />
            )}
          </>
        )}
        {active === 3 && (
          <>
            {jobData.allJobs.results.length === 0 ? (
              <EmptyState />
            ) : (
              <AllJobs
                getJob={getAllJob}
                updateStatus={updateStatus}
                paginationProps={getPaginationProps()}
                jobs={jobData.allJobs.results}
              />
            )}
          </>
        )}
        {active === 4 && (
          <>
            {jobData.closeJobs.results.length === 0 ? (
              <EmptyState />
            ) : (
              <CloseJobs
                getJob={getCloseJob}
                updateStatus={updateStatus}
                paginationProps={getPaginationProps()}
                jobs={jobData.closeJobs.results}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default JobPages;
