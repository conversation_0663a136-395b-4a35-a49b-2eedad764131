import { useEffect, useState } from "react";
import { createAvatar } from "@dicebear/core";
import { initials } from "@dicebear/collection";

const ChatHeader = ({ job }) => {
  const [avatarUri, setAvatarUri] = useState("");
  const [amName, setAMName] = useState("");

  useEffect(() => {
    const generateAvatar = async () => {
      const avatar = await createAvatar(initials, {
        seed: amName,
        backgroundColor: ["3E9900"],
      }).toDataUri();
      setAvatarUri(avatar);
    };

    generateAvatar();
  }, [amName]);

  useEffect(() => {
    setAMName(
      `${job?.accountManager?.name?.firstName} ${
        job?.accountManager?.name?.middleName
          ? job?.accountManager?.name?.middleName + " "
          : ""
      }${job?.accountManager?.name?.lastName}`
    );
  }, [job]);

  return (
    <div className="flex py-6 justify-between items-center self-stretch border-b text-[#CBD4E1]">
      <div className="flex gap-4">
        {avatarUri && (
          <img
            src={avatarUri}
            alt="Avatar"
            className="w-10 h-10 rounded-[1.25rem]"
          />
        )}
        <div className="flex justify-center flex-col">
          <span className=" text-[#27364B] font-semibold text-[1.125rem] leading-[1.75rem]">
            {amName}
          </span>
          {/* <div className="w-fit flex items-center gap-2 ">
            <span className="w-[0.625rem] h-[0.625rem] bg-[#419E6A] rounded-full"></span>
            <span className=" text-[#94A3B8] text-[0.75rem] font-medium">
              Online
            </span>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
