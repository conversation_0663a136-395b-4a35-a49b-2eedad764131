import { apiConnector } from "../apiConnector";
import { payoutEndpoints } from "../apis";

const { GET_PAYOUT_API, ADD_PAYOUT_API, CREATE_PAYOUT_API, UPDATE_PAYOUT_API } =
  payoutEndpoints;

export const getPayoutDetails = async (page, limit) => {
  const response = await apiConnector(
    "GET",
    GET_PAYOUT_API,
    {},
    {},
    { page, limit }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const createPayout = async (payoutData) => {
  const response = await apiConnector("POST", CREATE_PAYOUT_API, payoutData);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message || "Failed to create payout");
  }
  return response.data;
};

export const updatePayoutDetails = async (payoutId, updateData) => {
  const response = await apiConnector("PUT", UPDATE_PAYOUT_API, {
    payoutId,
    ...updateData,
  });
  if (!response?.data?.success) {
    throw new Error(
      response?.data?.message || "Failed to update payout details"
    );
  }
  return response.data;
};

export const addPayout = async (payoutData) => {
  const response = await apiConnector("POST", ADD_PAYOUT_API, payoutData);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message || "Payout creation failed");
  }
  return response.data;
};
