import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import JobOverviewCard from "../../../common/Job/JobOverviewCard";
import { getJobByID } from "../../../../services/operations/jobAPI";
import { getcandidateWithoutSubmission } from "../../../../services/operations/candidateAPI";
import TableHeader from "../../../common/Table/TableHeader";
import PaginationFooter from "../../../common/Table/TableFooter";

const columns = [
  { key: "candidateID", label: "Candidate ID" },
  { key: "name", label: "Name" },
  { key: "emailAddress", label: "Email" },
  { key: "experience", label: "Experience" },
  { key: "submitted", label: "Currently Submitted On" },
];

const SelectExistingCandidate = () => {
  const [searchParams] = useSearchParams();
  const jobId = searchParams.get("jobId");
  const [job, setJob] = useState();
  const [searchQuery, setSearchQuery] = useState("");
  const [candidates, setCandidates] = useState([]);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const navigate = useNavigate();

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    if (jobId) {
      getJobByID(jobId).then((data) => {
        if (Array.isArray(data?.data) && data.data.length > 0) {
          setJob(data.data[0]);
        } else if (data?.data) {
          setJob(data.data);
        } else {
          setJob(data);
        }
      });

      getcandidateWithoutSubmission(jobId).then((response) => {
        const candidateRows = (response?.data || []).map((item) => {
          const relExp = item?.skillsAndExperience?.relevantExperience;
          let experience = "-";
          if (relExp && (relExp.year || relExp.month)) {
            experience = `${relExp.year || 0} Years${
              relExp.month ? ` ${relExp.month} Months` : ""
            }`;
          }
          return {
            candidateID: item?.candidateID,
            name: `${item?.personalDetails?.firstName ?? ""} ${
              item?.personalDetails?.lastName ?? ""
            }`,
            emailAddress: item?.personalDetails.emailAddress ?? "-",
            experience,
            submitted: item?.submittedJobsCount ?? "-",
          };
        });
        setCandidates(candidateRows);
        setFilteredCandidates(candidateRows);
      });
    }
  }, [jobId]);

  useEffect(() => {
    const filtered = candidates.filter((candidate) =>
      candidate.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredCandidates(filtered);
    setCurrentPage(1);
  }, [searchQuery, candidates]);

  // Pagination logic
  const paginatedCandidates = filteredCandidates.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );
  const totalResults = filteredCandidates.length;
  const totalPages = Math.ceil(totalResults / pageSize);

  return (
    <div className="p-6">
      <JobOverviewCard job={job} />

      {/* Search Bar */}
      <div className="mt-6 mb-4 relative w-full max-w-xs">
        <input
          type="text"
          className="pl-7 pr-3 py-2 border border-gray-300 rounded w-full focus:outline-none focus:ring-1 focus:ring-gray-500"
          placeholder="Search Existing Candidates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <img
          src="/assets/icons/search.svg"
          alt="Search Icon"
          className="w-7 h-8 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        />
      </div>

      {/* Table */}
      <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full bg-white">
          <TableHeader columns={columns.map((col) => col.label)} />
          <tbody>
            {paginatedCandidates?.map((candidate) => (
              <tr
                key={candidate.candidateID}
                className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                onClick={() =>
                  navigate(
                    `/candidate/addcandidate?step=0&jobId=${jobId}&candidateID=${candidate?.candidateID}&isInstantSubmit=true`
                  )
                }
              >
                {columns?.map((col) => (
                  <td key={col.key} className="py-2 px-4 ">
                    {candidate[col.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        <PaginationFooter
          totalResults={totalResults}
          pageSize={pageSize}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          setPageSize={setPageSize}
          totalPages={totalPages}
        />
      </div>
    </div>
  );
};

export default SelectExistingCandidate;
