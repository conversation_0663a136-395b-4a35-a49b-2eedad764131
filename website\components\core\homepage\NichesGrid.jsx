import engineeringIcon from "@/public/assets/icons/homepage/engineering.svg";
import healthcareIcon from "@/public/assets/icons/homepage/healthcare.svg";
import financeIcon from "@/public/assets/icons/homepage/finance.svg";
import pharmacyIcon from "@/public/assets/icons/homepage/pharmacy.svg";
import telecomIcon from "@/public/assets/icons/homepage/telecom.svg";
import techIcon from "@/public/assets/icons/homepage/tech.svg";
import manufacturingIcon from "@/public/assets/icons/homepage/manufacturing.svg";

import Image from "next/image";

const NichesGrid = () => {
  return (
    <div>
      <div className="flex flex-col lg:flex-row  w-full">
        <div className="flex  gap-4 w-full lg:w-[45%] self-stretch bg-[#F3FFEB] p-8 ">
          <Image height={80} width={80} src={techIcon} alt="tech" />
          <div className="flex flex-col gap-2">
            <h3 className="font-semibold text-[20px]">Tech</h3>
            <p>
              From software engineers to AI specialists, place the best talent
              in the rapidly evolving tech industry.
            </p>
          </div>
        </div>
        <div className="flex gap-4 w-full lg:w-[55%] self-stretch bg-[#A9FF70]  p-8">
          <Image height={80} width={80} src={financeIcon} alt="healthcare" />
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-[20px]">Banking & Finance</p>
            <p>
              Source top-tier finance experts for banks, fintech companies, and
              global financial institutions.
            </p>
          </div>
        </div>
      </div>
      <div className="flex flex-col lg:flex-row w-full">
        <div className="flex flex-col w-full lg:w-[50%] self-stretch">
          <div className="flex gap-4 bg-[#A9FF70] p-8">
            <Image height={80} width={80} src={pharmacyIcon} alt="tech" />
            <div className="flex flex-col gap-2">
              <h3 className="font-semibold text-[20px]">
                Life Science / Pharmacy
              </h3>
              <p>
                Recruit specialized talent for biotech, pharmaceutical, and life
                sciences organizations.
              </p>
            </div>
          </div>
          <div className="flex gap-4 bg-[#CEFFAD] p-8">
            <Image height={80} width={80} src={telecomIcon} alt="tech" />
            <div className="flex flex-col gap-2">
              <h3 className="font-semibold text-[20px]">Telecom</h3>
              <p>
                Support telecom companies by placing experts in network
                engineering, cybersecurity, and 5G technology.
              </p>
            </div>
          </div>
        </div>
        <div className="flex gap-4 w-full lg:w-[50%] self-stretch items-center justify-center bg-[#F3FFEB]  p-8">
          <Image height={80} width={80} src={healthcareIcon} alt="healthcare" />
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-[20px]">Healthcare</p>
            <p>
              Connect healthcare professionals with the best hospitals and
              medical institutions worldwide.
            </p>
          </div>
        </div>
      </div>
      <div className="flex flex-col lg:flex-row  w-full">
        <div className="flex gap-4 w-full lg:w-[55%] self-stretch bg-[#F3FFEB] p-8 ">
          <Image height={80} width={80} src={engineeringIcon} alt="tech" />
          <div className="flex flex-col gap-2">
            <h3 className="font-semibold text-[20px]">Engineering</h3>
            <p>
              Place mechanical, civil, electrical, and software engineers across
              industries that power infrastructure and innovation.
            </p>
          </div>
        </div>
        <div className="flex gap-4 w-full lg:w-[45%] self-stretch bg-[#A9FF70]  p-8">
          <Image
            height={80}
            width={80}
            src={manufacturingIcon}
            alt="healthcare"
          />
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-[20px]">Manufacturing</p>
            <p>
              Connect experienced professionals with manufacturers to support
              operations, production, and supply chain efficiency.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NichesGrid;
