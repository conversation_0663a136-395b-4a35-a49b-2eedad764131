const JobButton = ({ variant, icon, label,onClick }) => {
  return (
    <button
    onClick={onClick}
      className={`flex py-2 px-3 justify-center items-center gap-2 self-stretch rounded-md font-semibold ${
        variant === "primary"
          ? "bg-[#3E9900] text-white cursor-pointer"
          : variant === "danger"
          ? "border border-[#FC9595] bg-white text-[#FC9595]  cursor-pointer"
          : variant == "danger-500"
          ? "border border-[#D83232] bg-white text-[#D83232]  cursor-pointer"
          : "border border-[#3E9900] bg-white text-[#3E9900]  cursor-pointer"
      }`}
    >
      <span>{label}</span>
      {icon && <img src={icon} alt="" />}
    </button>
  );
};

export default JobButton;
