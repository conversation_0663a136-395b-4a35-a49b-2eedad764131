// validators/jobFieldValidators.js

const createJobValidationConfig = {
  jobTitle: { required: true },
  industry: { required: true },
  jobId: { required: true },
  externalJobId: { required: true },
  "commission.amount": { required: true },
  "location.country": { required: true },
  "experience.min": { required: true },
  "experience.max": { required: true },
  jobType: { required: true },
  requiredHoursPerWeek: { required: true },
  email: { required: true },
  priority: { required: true },
  openings: { required: true },
  "client.name": { required: true },
  "client.billRate": { required: true },
  "client.payRate": { required: true },
  primarySkills: { required: true },
  jobDescription: { required: true },
  "accountManager._id": { required: true },
  "accountManager.name": { required: true },
  //recruiters: { required: true }, // assuming non-empty
  //   "recruiters[]._id": { required: true },
  //   "recruiters[].name": { required: true },
  //   "resume_submitted[]._id": { required: true },
  //   "resume_submitted[].name": { required: true },
  //   "selected_resume[]._id": { required: true },
  //   "selected_resume[].name": { required: true },
  //   "jobRequestedToWork[]._id": { required: true },
  //   "jobRequestedToWork[].name": { required: true },
};

const getSingleJobValidateConfig = {
  jobId: { required: true },
};

const removeJobValidateConfig = {
  jobId: { required: true },
};

const jobSubmissionCreationValidateConfig = {
  "job._id": { required: true },
  "job.name": { required: true },
  "submittedBy._id": { required: true },
  "submittedBy.name": { required: true },
  "candidate.fullname": { required: true },
  "candidate.email": { required: true },
  "candidate.phone": { required: true },
  "candidate.resumeUrl": { required: true },
};

const getSingleJobSubmissionValidateConfig = {
  submissionId: { required: true },
};

module.exports = {
  createJobValidationConfig,
  getSingleJobValidateConfig,
  removeJobValidateConfig,
  jobSubmissionCreationValidateConfig,
  getSingleJobSubmissionValidateConfig,
};
