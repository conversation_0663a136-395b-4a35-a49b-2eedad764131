const mongoose = require("mongoose");

const AuditLogSchema = new mongoose.Schema(
  {
    currentJobStatus: {
      type: String,
      required: true,
    },
    previousJobStatus: {
      type: String,
      required: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    JobId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Job",
      required: true,
    },
    updateFields: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
    visibleTo: {
      type: String,
      enum: ["internal", "external"],
      default: "external",
    },
  },
  {
    timestamps: true,
  }
);

const auditLog = mongoose.model("auditlog", AuditLogSchema);
module.exports = { auditLog };
