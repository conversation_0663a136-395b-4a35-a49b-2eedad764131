import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { getCandidate } from "../../services/operations/candidateAPI";
import { Download } from "lucide-react";

const docLabels = {
  resume: "Resume",
  coverLetter: "Cover Letter",
  licenseCopy: "License Copy",
  blsCertificate: "BLS Certificate",
  aclsPalsNalsCertificate: "ACLS/PALS/NALS Certificate",
  fluVaccinationProof: "Flu Vaccination",
  covid19VaccinationProof: "COVID-19 Vaccination Certificate",
};

const columns = [
  { label: "S.No", key: "id" },
  { label: "Document Name", key: "documentName" },
  { label: "Action", key: "actions" },
];

const Documents = () => {
  const [searchParams] = useSearchParams();
  const candidateId = searchParams.get("candidateId");
  const [attachments, setAttachments] = useState(null);

  useEffect(() => {
    const fetchCandidate = async () => {
      if (!candidateId) return;
      try {
        const data = await getCandidate(candidateId);

        if (data[0]?.documentAttachments) {
          setAttachments(data[0].documentAttachments);
        } else {
          setAttachments({});
        }
      } catch {
        setAttachments({});
      }
    };
    fetchCandidate();
  }, [candidateId]);

  const rows = attachments
    ? Object.entries(attachments)
        .filter(([_, url]) => url)
        .map(([key, url], index) => ({
          id: index + 1,
          documentName: docLabels[key] || key,
          actions: (
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-700 hover:text-blue-800 inline-flex items-center justify-start gap-1"
            >
              <Download className="w-4 h-4" />
            </a>
          ),
        }))
    : [];

  return (
    <div className="overflow-x-auto mt-4 rounded-lg border border-gray-200 shadow-sm bg-white">
      <table className="min-w-full">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((col) => (
              <th
                key={col.key}
                className="px-6 py-3 text-left text-sm font-semibold text-gray-700"
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.length > 0 ? (
            rows.map((row, i) => (
              <tr
                key={i}
                className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
              >
                {columns.map((col) => (
                  <td className="px-6 py-3 align-middle" key={col.key}>
                    {row[col.key] || "—"}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={columns.length}
                className="px-6 py-10 text-center text-gray-500"
              >
                No documents available
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default Documents;
