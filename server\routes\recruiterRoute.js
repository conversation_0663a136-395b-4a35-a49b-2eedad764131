const express = require("express");
const router = express.Router();
const { auth } = require("../middlewares/auth");
const {
  getMatchedJobs,
  selectJobToWorkUpon,
  getWorkJobs,
  getWorkOnRequestJobs,
  getAllJobs,
  addToBookMark,
  getAllSaveJobs,
  unMappedJob,
  removeFromBookMark,
  updateWorkOnRequest,
  dashboardStats,
  getRecruiterDetails,
  getAllRecruiters,
  getRecruitersByJobId,
} = require("../controllers/Recruiter");

const { getCoinHistory } = require("../controllers/Coin");

router.get("/coinHistory", auth, getCoinHistory);

router.post("/select-job-to-work-upon", auth, selectJobToWorkUpon);

// working on jobs
router.get("/work-upon-jobs", auth, getWorkJobs);

// mapped jobs
router.get("/matched-jobs", auth, getMatchedJobs);

// work on request
router.get("/work-on-request-jobs", auth, getWorkOnRequestJobs);

// get all jobs
router.get("/get-all-jobs", auth, getAllJobs);

// get all save jobs
router.get("/get-all-save-jobs", auth, getAllSaveJobs);

router.get("/get-recruiters-by-job-id/:jobId", auth, getRecruitersByJobId);

// bookmark
router.post("/add-to-bookmark", auth, addToBookMark);

// remove form bookmark
router.delete("/remove-to-bookmark", auth, removeFromBookMark);

//  upmap jobs
router.post("/unmap-job", auth, unMappedJob);

// work-on-request-status-update
router.post("/work-on-request-status-update", auth, updateWorkOnRequest);

router.get("/dashboard-stats", auth, dashboardStats);

//* get recruiter details
router.get("/recruiter-details", auth, getRecruiterDetails);

//* get all recruiters
router.get("/get-all-recruiters", auth, getAllRecruiters);

module.exports = router;
