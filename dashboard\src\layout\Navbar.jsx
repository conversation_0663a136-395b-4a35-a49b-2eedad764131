import React, { useEffect, useRef, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { logOut } from "../redux/reducer/auth.slice";
import { clearCandidateID } from "../redux/reducer/candidate.slice";
import Notification from "../components/common/Notification/Notification";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const userInfo = useSelector((state) => state.auth?.user);
  const isRecruiter = userInfo?.role === "recruiter";

  // Close dropdown if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  function Logout() {
    dispatch(logOut());
    navigate("/login");
  }

  useEffect(() => {
    if (pathname.includes("jobs/jobdetails")) {
      dispatch(clearCandidateID());
    }
  }, [pathname, dispatch]);

  return (
    <>
      <nav className="top-0 sticky z-50 w-full bg-white border-b border-gray-200">
        <div className="px-3 py-2 lg:px-5 lg:pl-3">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-[16px] font-bold">Dashboard</div>
              <div className="flex items-center ">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="#54aad1"
                  className="cursor-pointer"
                  onClick={() => {
                    navigate("/");
                  }}
                >
                  <path
                    d="M4.16406 15.8313V8.58792C4.16406 8.37458 4.21184 8.17264 4.3074 7.98208C4.40295 7.79153 4.53462 7.63458 4.7024 7.51125L9.1899 4.11292C9.4249 3.93347 9.69323 3.84375 9.9949 3.84375C10.2966 3.84375 10.5666 3.93347 10.8049 4.11292L15.2924 7.51042C15.4607 7.63375 15.5924 7.79097 15.6874 7.98208C15.783 8.17264 15.8307 8.37458 15.8307 8.58792V15.8313C15.8307 16.0546 15.7477 16.2493 15.5816 16.4154C15.4155 16.5815 15.2207 16.6646 14.9974 16.6646H12.1774C11.9863 16.6646 11.8263 16.6001 11.6974 16.4712C11.5685 16.3418 11.5041 16.1818 11.5041 15.9912V12.0171C11.5041 11.8265 11.4396 11.6668 11.3107 11.5379C11.1813 11.4085 11.0213 11.3438 10.8307 11.3438H9.16406C8.97351 11.3438 8.81379 11.4085 8.6849 11.5379C8.55545 11.6668 8.49073 11.8265 8.49073 12.0171V15.9921C8.49073 16.1826 8.42628 16.3424 8.2974 16.4712C8.16851 16.6001 8.00879 16.6646 7.81823 16.6646H4.9974C4.77406 16.6646 4.57934 16.5815 4.41323 16.4154C4.24712 16.2493 4.16406 16.0546 4.16406 15.8313Z"
                    fill="#94A3B8"
                  />
                </svg>
                {pathname
                  ?.split("/")
                  ?.filter((item) => item)
                  ?.map((item, key, array) => {
                    if (array.length == key + 1) {
                      return (
                        <div>
                          {">"}{" "}
                          <span>
                            {item?.slice(0, 1)?.toUpperCase() +
                              item?.slice(1).toLowerCase()}
                          </span>{" "}
                        </div>
                      );
                    }
                    return (
                      <Link to={item}>
                        {">"}{" "}
                        <span className={"text-blue-400"}>
                          {item?.slice(0, 1)?.toUpperCase() +
                            item?.slice(1).toLowerCase()}
                        </span>{" "}
                      </Link>
                    );
                  })}
              </div>
            </div>
            <div className="flex items-center">
              <div className="flex items-center justify-between gap-2">
                {isRecruiter && (
                  <button
                    type="button"
                    className=" flex items-center px-3 py-1 rounded-[8px] border border-white bg-[#FCE7F6] text-[#EE46BC] font-medium text-sm focus:ring-1 focus:ring-gray-300"
                  >
                    <span className="sr-only">slot icon</span>
                    <img
                      className="w-5 h-5 rounded-full"
                      src="/assets/icons/slot_empty.svg"
                      alt="slot icon"
                    />
                    <span className="ml-2 ">
                      {10 - (userInfo?.emptySlot || 0)} Slot Remaining
                    </span>
                  </button>
                )}
                {isRecruiter && (
                  <Link to={"/coin-history"}>
                    <button
                      type="button"
                      className=" cursor-pointer flex items-center px-2 py-1 rounded-[8px] border border-white bg-[#FEEFC7] text-[#F79009] font-medium text-sm focus:ring-1 focus:ring-gray-300"
                    >
                      <span className="sr-only">coins icon</span>
                      <img
                        className="w-5 h-5 rounded-full"
                        src="/assets/icons/coins.svg"
                        alt="coins icon"
                      />
                      <span className="ml-2 ">
                        {userInfo?.coinBalance} Coins
                      </span>
                    </button>
                  </Link>
                )}
                {["recruiter", "accountManager"].includes(userInfo?.role) && (
                  <Notification />
                )}

                <button
                  type="button"
                  className="flex text-sm items-center rounded-full focus:ring-1 focus:ring-gray-300 "
                  aria-expanded={isOpen}
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <span className="sr-only">Open user menu</span>
                  <img
                    className="w-8 h-8 rounded-full"
                    src={"/assets/icons/user_default.svg"}
                    alt="user photo"
                  />
                  <img
                    className="w-4 h-5"
                    src={"/assets/icons/arrow.svg"}
                    alt="user photo"
                  />
                </button>
              </div>
              {isOpen && (
                <div
                  ref={dropdownRef}
                  className="absolute right-0 top-10 z-50 my-3 me-3 w-56 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-lg"
                >
                  <div className="px-4 py-3" role="none">
                    <p className="text-sm text-gray-900 " role="none">
                      {userInfo?.firstName?.slice(0, 1)?.toUpperCase() +
                        userInfo?.firstName?.slice(1)?.toLowerCase()}{" "}
                      {userInfo?.lastName?.slice(0, 1)?.toUpperCase() +
                        userInfo?.lastName?.slice(1)?.toLowerCase()}
                    </p>
                    <p
                      className="text-sm font-medium text-gray-900 truncate "
                      role="none"
                    >
                      {userInfo?.email}
                    </p>
                  </div>
                  <ul className="py-0" role="none">
                    <li>
                      <Link
                        to={"/user-profile"}
                        className="flex items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                      >
                        <img
                          src="/assets/icons/recruiter.svg"
                          alt="my profile"
                          className="w-5 h-5"
                        />{" "}
                        My Profile
                      </Link>
                    </li>

                    <li>
                      <div
                        className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                        onClick={Logout}
                      >
                        <img
                          src="/assets/icons/logout.svg"
                          alt="logout"
                          className="w-5 h-5"
                        />{" "}
                        Logout
                      </div>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
