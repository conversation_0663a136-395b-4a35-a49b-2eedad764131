import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";
import SelectField from "../../../../common/Input/SelectionField";
import Select from "react-select";
import countries from "../../../../../data/countries.json";
import PhoneInput from "react-phone-input-2";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";
import "react-phone-input-2/lib/style.css";

const yesNoOptions = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
];

const PersonalDetailsForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    setFieldTouched,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  const handleCountryChange = (e) => {
    setFieldValue("personalDetails.country", e.value);
    setFieldValue("personalDetails.state", "");
    setFieldValue("personalDetails.city", "");
  };

  const handleStateChange = (e) => {
    setFieldValue("personalDetails.state", e.value);
    setFieldValue("personalDetails.city", "");
  };

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
      autoComplete="off"
    >
      {/* First Name */}
      <div className="w-full">
        <InputField
          label="First Name"
          name="personalDetails.firstName"
          value={values.personalDetails.firstName || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Enter first name"
          errorMessage={
            touched.personalDetails?.firstName &&
            errors.personalDetails?.firstName
          }
        />
      </div>
      {/* Last Name */}
      <div className="w-full">
        <InputField
          label="Last Name"
          name="personalDetails.lastName"
          value={values.personalDetails.lastName || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Enter last name"
          errorMessage={
            touched.personalDetails?.lastName &&
            errors.personalDetails?.lastName
          }
        />
      </div>
      {/* Phone Number */}
      <div className="w-full">
        <label
          htmlFor="personalDetails.phoneNumber"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number <span className="text-red-500">*</span>
        </label>
        <PhoneInput
          country="us"
          value={values.personalDetails.phoneNumber || ""}
          onChange={(value, country, event, formattedValue) => {
            setFieldValue("personalDetails.phoneNumber", formattedValue);
            setFieldValue("personalDetails.phoneCountryCode", country.dialCode);
          }}
          onBlur={() => setFieldTouched("personalDetails.phoneNumber", true)}
          enableSearch
          inputProps={{
            name: "personalDetails.phoneNumber",
            required: true,
            autoFocus: false,
            id: "personalDetails.phoneNumber",
          }}
          containerClass="w-full"
          inputClass="!h-[2.6rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          buttonClass="!h-[2.6rem] !border-r border-gray-300"
          placeholder="Enter your phone number"
        />
        {touched.personalDetails?.phoneNumber &&
          errors.personalDetails?.phoneNumber && (
            <div className="text-xs text-red-600 mt-1">
              {errors.personalDetails.phoneNumber}
            </div>
          )}
      </div>
      {/* Email Address */}
      <div className="w-full">
        <InputField
          label="Email Address"
          name="personalDetails.emailAddress"
          value={values.personalDetails.emailAddress || ""}
          onChange={handleChange}
          // onBlur={handleBlur}
          required
          placeholder="Enter Email Address"
          errorMessage={
            touched.personalDetails?.emailAddress &&
            errors.personalDetails?.emailAddress
          }
        />
      </div>
      {/* Current Address */}
      <div className="w-full">
        <InputField
          label="Current Address"
          name="personalDetails.currentAddress"
          value={values.personalDetails.currentAddress || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Enter address"
          errorMessage={
            touched.personalDetails?.currentAddress &&
            errors.personalDetails?.currentAddress
          }
        />
      </div>
      {/* Country */}
      <div className="w-full">
        <label htmlFor="country" className="text-sm font-medium text-[#374151]">
          Country {<span className="text-red-800">*</span>}
        </label>
        <Select
          options={countries.map((country) => ({
            value: country.name,
            label: country.name,
          }))}
          onChange={handleCountryChange}
          onBlur={handleBlur}
          value={
            values.personalDetails.country
              ? {
                  value: values.personalDetails.country,
                  label: values.personalDetails.country,
                }
              : "" || ""
          }
        />
        {touched.personalDetails?.country &&
          errors.personalDetails?.country && (
            <div className="text-red-500 text-xs mt-1">
              {touched.personalDetails?.country &&
                errors.personalDetails?.country}
            </div>
          )}
      </div>
      {/* State */}
      <div className="w-full">
        <label htmlFor="state" className="text-sm font-medium text-[#374151]">
          State {<span className="text-red-800">*</span>}
        </label>
        <Select
          name="personalDetails.state"
          options={
            countries
              .find(
                (country) => country.name === values.personalDetails.country
              )
              ?.states.map((state) => ({
                value: state.name,
                label: state.name,
              })) || []
          }
          onChange={handleStateChange}
          onBlur={handleBlur}
          value={
            values.personalDetails.state
              ? {
                  value: values.personalDetails.state,
                  label: values.personalDetails.state,
                }
              : null
          }
          isDisabled={!values.personalDetails.country}
        />
        {touched.personalDetails?.state && errors.personalDetails?.state && (
          <div className="text-red-500 text-xs mt-1">
            {touched.personalDetails?.state && errors.personalDetails?.state}
          </div>
        )}
      </div>
      {/* City */}
      <div className="w-full">
        <InputField
          label="City"
          name="personalDetails.city"
          value={values.personalDetails.city || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Enter City"
          errorMessage={
            touched.personalDetails?.city && errors.personalDetails?.city
          }
        />
      </div>
      {/* Zip Code */}
      <div className="w-full">
        <InputField
          label="Zip Code"
          name="personalDetails.zipcode"
          value={values.personalDetails.zipcode || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Enter zip code"
          errorMessage={
            touched.personalDetails?.zipcode && errors.personalDetails?.zipcode
          }
        />
      </div>
      {/* Relocation Willingness */}
      <div className="w-full">
        <SelectField
          label="Relocation Willingness"
          name="personalDetails.relocationWillingness"
          value={values.personalDetails.relocationWillingness || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          // required
          placeholder="Select"
          options={yesNoOptions}
          errorMessage={
            touched.personalDetails?.relocationWillingness &&
            errors.personalDetails?.relocationWillingness
          }
        />
      </div>
      {/* Work Authorization Status */}
      <div className="w-full">
        <SelectField
          label="Work Authorization Status"
          name="personalDetails.workAuthorizationStatus"
          value={values.personalDetails.workAuthorizationStatus}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Select"
          options={[
            { label: "US Citizen", value: "US Citizen" },
            { label: "Green Card", value: "Green Card" },
            { label: "H1B", value: "H1B" },
            { label: "EAD", value: "EAD" },
          ]}
          errorMessage={
            touched.personalDetails?.workAuthorizationStatus &&
            errors.personalDetails?.workAuthorizationStatus
          }
        />
      </div>
      {/* SSN */}
      <div className="w-full">
        <InputField
          label="SSN (Last 4 digits)"
          name="personalDetails.ssnLast4Digit"
          value={values.personalDetails.ssnLast4Digit || ""}
          onChange={(e) => {
            const onlyDigits = e.target.value.replace(/\D/g, "");
            if (onlyDigits.length <= 4) {
              handleChange({
                target: {
                  name: "personalDetails.ssnLast4Digit",
                  value: onlyDigits,
                },
              });
            }
          }}
          // onBlur={handleBlur}
          // required
          placeholder="1234"
          // errorMessage={
          //   touched.personalDetails?.ssnLast4Digit &&
          //   errors.personalDetails?.ssnLast4Digit
          // }
        />
      </div>

      {/* Available Start Date */}
      <div className="w-full">
        <CalendarDropdownField
          label="Available Start Date"
          name="personalDetails.availableStartDate"
          required
          minDate={new Date()}
          errorMessage={
            touched.personalDetails?.availableStartDate &&
            errors.personalDetails?.availableStartDate
          }
        />
      </div>
    </form>
  );
};

export default PersonalDetailsForm;
