import React, { useEffect, useState } from "react";
import StatsCard1 from "../../components/common/Dashboard/StatsCard1";
import TabNav from "../../components/common/TabNav/TabNav";
import { useSearchParams } from "react-router-dom";
import PaginationFooter from "../../components/common/Table/TableFooter";
import { getAccountByID } from "../../services/operations/headAccountAPI";
import TableHeader from "../../components/common/Table/TableHeader";
import ThreeDot from "../../components/common/Button/ThreeDot";
import { getAccountManagerSubmissionStats } from "../../services/operations/dashboardAPI";
import { useNavigate } from "react-router-dom";
import ColorSelectionInput from "../../components/common/Input/ColorSelectionInput";
import Pills from "../../components/common/Job/Pills";
import CandidateCard from "../../components/common/cards/CandidateCard";

const ManagersDetails = ({ data }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // Define tabs with keys (use simple strings without spaces)
  const tabKeys = ["active", "all"];
  const tabLabels = ["Active Jobs", "All Jobs"];
  const defaultTab = "active";

  // State management
  const [error, setError] = useState(null);
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [job, setJob] = useState(null);
  const [stat, setStat] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("all"); // New state for status filter

  // Get URL parameters - use consistent naming
  const managerId = searchParams.get("managerid");
  const currentTab = searchParams.get("tab");

  // Always ensure tab param is present in URL
  useEffect(() => {
    if (!currentTab && managerId) {
      setSearchParams(
        { tab: defaultTab, managerid: managerId },
        { replace: true }
      );
    }
  }, [currentTab, setSearchParams, managerId]);

  // Set active tab based on URL
  useEffect(() => {
    const tab = searchParams.get("tab") || defaultTab;
    const idx = tabKeys.indexOf(tab);
    setActiveTab(idx === -1 ? 0 : idx);
  }, [searchParams]);

  // Handle tab changes
  const handleTabChange = (index) => {
    setSearchParams({ tab: tabKeys[index], managerid: managerId });
    setActiveTab(index);
    // Reset status filter when switching tabs
    setSelectedStatus("all");
  };

  // Fetch manager details
  useEffect(() => {
    const fetchManagerDetails = async () => {
      if (!managerId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await getAccountByID(managerId);
        if (response?.success) {
          setJob(
            Array.isArray(response.data) ? response.data[0] : response.data
          );
        } else {
          setError(response?.message || "Failed to fetch manager details.");
        }
      } catch (error) {
        setError("Error fetching manager details.");
        console.error("Error fetching manager details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchManagerDetails();
  }, [managerId]);

  async function callAPI() {
    const response = await getAccountManagerSubmissionStats(managerId);
    setStat(response.data);
  }

  useEffect(() => {
    callAPI();
  }, [managerId]);

  // Prepare tab navigation
  const tabs = tabLabels.map((label) => ({
    name: <span>{label}</span>,
    css: "",
  }));

  // Transform job data for table display
  const transformJobData = (jobs) => {
    if (!Array.isArray(jobs)) return [];

    return jobs.map((jobItem) => ({
      jobId: jobItem?.jobId || "—",
      jobTitle: jobItem?.jobTitle || "—",
      location: jobItem?.location
        ? `${jobItem.location.city || ""}${
            jobItem.location.city && jobItem.location.state ? ", " : ""
          }${jobItem.location.state || ""}${
            jobItem.location.state && jobItem.location.country ? ", " : ""
          }${jobItem.location.country || ""}`
            .trim()
            .replace(/^,\s*|,\s*$/g, "") || "—"
        : "—",
      jobType: jobItem?.jobType || "—",
      totalSubmission: jobItem?.candidateSubmissionCount || 0,
      recruiter: jobItem?.recruterCount,
      jobStatus: jobItem?.jobStatus || "—",
      _id: jobItem?._id,
    }));
  };

  // Render table rows for jobs
  const renderJobTableRows = (jobs) => {
    const transformedData = transformJobData(jobs);

    return transformedData.map((jobItem, index) => (
      <tr
        className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800"
        key={jobItem._id || index}
      >
        {/* Job ID */}
        <td className="px-4 py-3 text-start">{jobItem.jobId}</td>

        {/* Job Title */}
        <td className="px-4 py-3 text-start">{jobItem.jobTitle}</td>

        {/* Location */}
        <td className="px-4 py-3 text-start">{jobItem.location}</td>

        {/* Job Type */}
        <td className="px-4 py-3 text-start">
          <span className="capitalize">{jobItem.jobType}</span>
        </td>

        {/* Total Submission */}
        <td className="px-4 py-3 text-start">{jobItem.totalSubmission}</td>

        {/* Recruiter */}
        <td className="px-4 py-3 text-start">{jobItem.recruiter}</td>

        {/* Action column for currently selected jobItem */}
        <td className="px-4 py-3 text-start">
          <ThreeDot
            dropdownSize="w-32"
            buttonDropDown={
              <ul className="py-0" role="none">
                <li>
                  <div
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(
                        `/jobs/jobdetails?jobId=${jobItem.jobId}&tab=jobdetails`
                      );
                    }}
                    className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    role="menuitem"
                  >
                    <img src={"/assets/icons/view.svg"} alt="View" />
                    View
                  </div>
                </li>
              </ul>
            }
          />
        </td>
      </tr>
    ));
  };

  // Filter jobs based on active tab and status
  const getFilteredJobs = () => {
    if (!job?.jobs || !Array.isArray(job.jobs)) return [];

    let filteredJobs = [];

    if (activeTab === 0) {
      // Active jobs - only show jobs with status "active" (case insensitive)
      filteredJobs = job.jobs.filter((jobItem) => {
        const status = jobItem.jobStatus?.toLowerCase();
        return status === "active";
      });
    } else {
      // All jobs - show all jobs, then filter by selected status
      filteredJobs = job.jobs;

      // Apply status filter if not "all"
      if (selectedStatus !== "all") {
        filteredJobs = filteredJobs.filter(
          (jobItem) =>
            jobItem.jobStatus?.toLowerCase() === selectedStatus.toLowerCase()
        );
      }
    }

    return filteredJobs;
  };
  // Get table columns based on active tab
  const getTableColumns = () => {
    const baseColumns = [
      "JobId",
      "Job Title",
      "Location",
      "Job Type",
      "Total Submission",
      "Recruiter Working",
    ];
    baseColumns.push("");

    return baseColumns;
  };

  return (
    <>
      {/* User Profile Card */}
      <CandidateCard
        name={job?.name?.firstName + job?.name?.lastName}
        domain={job?.profile?.domain}
        email={job?.email}
        template={
          <>
            <div className="mt-1 mb-2 text-sm text-gray-500 px-4">
              Date of Joining:{" "}
              {job?.createdAt
                ? new Date(job.createdAt).toLocaleDateString("en-GB") // 'en-GB' gives DD/MM/YYYY format
                : "N/A"}
            </div>
            <div className="bg-[#FAFAFA] rounded-b-xl p-4">
              <h2 className="text-lg font-medium text-gray-900  mb-2">
                Active Job Stats
              </h2>

              <div className="grid grid-cols-5 gap-5">
                {[
                  {
                    key: "Active Assigned Jobs",
                    value: job?.activeJobCount || 0,
                  },
                  { key: "Job Covered", value: job?.coverJobCount || 0 },
                  {
                    key: "Total Submission",
                    value: job?.totalsubmittionCount || 0,
                  },
                  {
                    key: "Unreviewed",
                    value: job?.totalsubmittionCount - job?.reviewedCount || 0,
                  },
                  {
                    key: "Reviewed",
                    value: job?.reviewedCount || 0,
                  },
                  {
                    key: "Submitted to Client",
                    value: job?.submittedToClientCount || 0,
                  },
                  {
                    key: "Interviewing Candidates",
                    value: job?.interviewingCount || 0,
                  },
                  {
                    key: "Offer Released",
                    value: job?.offerReleasedCount || 0,
                  },
                  {
                    key: "Offer Accepted",
                    value: job?.offerAcceptedCount || 0,
                  },
                  {
                    key: "Offer Rejected",
                    value: job?.offerRejectedCount || 0,
                  },
                  { key: "Rejected", value: job?.rejectedCount || 0 },
                  {
                    key: "Hired Under Guarantee Period",
                    value: job?.hiredUnderGuaranteePeriodCount || 0,
                  },
                  {
                    key: "Guarantee Period Completed",
                    value: job?.guaranteePeriodCompletedCount || 0,
                  },
                  {
                    key: "Guarantee Period Not Completed",
                    value: job?.guaranteePeriodNotCompletedCount || 0,
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className={
                      [0, 5, 10].includes(index)
                        ? "mb-2"
                        : `pl-2 border-l-2 border-gray-300 mb-2`
                    }
                  >
                    <div className="text-2xl font-semibold text-gray-900 mb-2">
                      {item.value}
                    </div>

                    <div className="text-sm text-gray-600 whitespace-nowrap">
                      {item.key}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        }
      />

      {/* Tab Navigation */}
      <TabNav nav={tabs} active={activeTab} setActive={handleTabChange} />

      {/* Content Based on Active Tab */}
      <div className="mt-2">
        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">Loading jobs...</div>
          </div>
        )}

        {error && (
          <div className="flex justify-center items-center py-8">
            <div className="text-red-500">{error}</div>
          </div>
        )}

        {!loading && !error && (
          <div className="p-0">
            <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
              <table className="min-w-full divide-y divide-gray-200 text-sm text-left">
                <TableHeader columns={getTableColumns()} />
                <tbody>
                  {getFilteredJobs().length > 0 ? (
                    renderJobTableRows(getFilteredJobs())
                  ) : (
                    <tr>
                      <td
                        colSpan={getTableColumns().length}
                        className="px-4 py-8 text-center text-gray-500"
                      >
                        No{" "}
                        {activeTab === 0
                          ? "active"
                          : selectedStatus !== "all"
                          ? selectedStatus
                          : ""}{" "}
                        jobs found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Pagination Footer */}
        {!loading && (
          <div className="mt-4">
            <PaginationFooter
              currentPage={currentPage}
              totalPages={totalPages}
              pageSize={pageSize}
              setPageSize={setPageSize}
              setCurrentPage={setCurrentPage}
              totalResults={getFilteredJobs().length}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default ManagersDetails;
