const mongoose = require("mongoose");

const bookmarkSchema = new mongoose.Schema(
  {
    jobId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Job",
      required: true,
    },
    recruiterId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const bookMark = mongoose.model("jobbookmark", bookmarkSchema);

module.exports = { bookMark };
