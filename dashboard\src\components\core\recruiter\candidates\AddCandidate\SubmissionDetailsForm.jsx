import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";
import SelectField from "../../../../common/Input/SelectionField";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";

const yesNoOptions = [
  { label: "Yes", value: "Yes" },
  { label: "No", value: "No" },
];

const rateExpectationTypeOptions = [
  { label: "Hourly", value: "Hourly" },
  { label: "Salary", value: "Salary" },
];

function toDatetimeLocal(value) {
  if (!value) return "";
  // Remove 'Z' if present, then slice to "YYYY-MM-DDTHH:mm"
  return value.replace("Z", "").slice(0, 16);
}

const SubmissionDetailsForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <SelectField
        label="Rate Expectation Type"
        name="submissionDetails.rateExpectation"
        value={values.submissionDetails.rateExpectation}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select type"
        options={rateExpectationTypeOptions}
        errorMessage={
          touched.submissionDetails?.rateExpectation &&
          errors.submissionDetails?.rateExpectation
        }
      />
      <SelectField
        label="References Provided"
        name="submissionDetails.referenceProvider"
        value={values.submissionDetails.referenceProvider}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={yesNoOptions}
        errorMessage={
          touched.submissionDetails?.referenceProvider &&
          errors.submissionDetails?.referenceProvider
        }
      />
      
      <CalendarDropdownField
        label="Candidate Availability for Interview"
        name="submissionDetails.candidateAvailabilityForInterview"
        required
        placeholder="Select date and time"
        minDate={new Date()}
        showTimeSelect
        errorMessage={
          touched.submissionDetails?.candidateAvailabilityForInterview &&
          errors.submissionDetails?.candidateAvailabilityForInterview
        }
      />

      <InputField
        label="Additional Notes"
        name="submissionDetails.additionalNote"
        value={values.submissionDetails.additionalNote}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder="Enter any additional notes"
        multiline
      />
    </form>
  );
};

export default SubmissionDetailsForm;
