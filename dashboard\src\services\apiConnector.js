import axios from "axios";
import { genrateAccessToken } from "./operations/authAPI";
import { loginToken, logOut } from "../redux/reducer/auth.slice";
import { Store } from "../redux/Store";
import { setLoading } from "../redux/reducer/loader.slice";

const getAccessToken = () => Store.getState().auth.token?.accessToken;
const getRefreshToken = () => Store.getState().auth.token?.refreshToken;
const setToken = (accessToken, refreshToken) => {
  Store.dispatch(
    loginToken({ accessToken: accessToken, refreshToken: refreshToken })
  );
};

const clearTokens = () => {
  Store.dispatch(logOut());
};

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) prom.reject(error);
    else prom.resolve(token);
  });
  failedQueue = [];
};

export const axiosInstance = axios.create({});

axiosInstance.interceptors.request.use(
  (config) => {
    // Check for custom flag to skip token
    Store.dispatch(setLoading(true));
    if (!config.skipAuth) {
      const token = getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    Store.dispatch(setLoading(false));
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.skipAuth
    ) {
      originalRequest._retry = true;

      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          })
          .finally(() => {
            Store.dispatch(setLoading(false));
          });
      }

      isRefreshing = true;

      try {
        const refreshToken = getRefreshToken();

        const res = await genrateAccessToken({ refreshToken });
        const newAccessToken = res.accessToken;
        const newRefreshToken = res.refreshToken;
        setToken(newAccessToken, newRefreshToken);

        processQueue(null, newAccessToken);

        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return axiosInstance(originalRequest);
      } catch (err) {
        processQueue(err, null);
        clearTokens();
        window.location.href = "/login";
        return Promise.reject(err);
      } finally {
        isRefreshing = false;
        Store.dispatch(setLoading(false));
      }
    }
    Store.dispatch(setLoading(false));
    return Promise.reject(error);
  }
);

export const apiConnector = (
  method,
  url,
  bodyData,
  headers,
  params,
  skipAuth = false
) => {
  return axiosInstance({
    method: `${method}`,
    url: `${url}`,
    data: bodyData ? bodyData : null,
    headers: headers ? headers : null,
    params: params ? params : null,
    skipAuth,
  });
};
