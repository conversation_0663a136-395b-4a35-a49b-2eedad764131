{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --port 5173"}, "dependencies": {"@dicebear/collection": "^9.2.2", "@dicebear/core": "^9.2.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.5", "apexcharts": "^4.7.0", "axios": "^1.9.0", "dompurify": "^3.2.6", "formik": "^2.4.6", "lucide-react": "^0.513.0", "quill": "^2.0.3", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-quill-new": "^3.4.6", "react-redux": "^9.2.0", "react-router": "^7.5.3", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.5", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rollup-plugin-visualizer": "^6.0.3", "vite": "^6.3.1"}}