import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import searchImg from "../../assets/icons/search.svg";
import PaginationFooter from "../../components/common/Table/TableFooter";
import TableHeader from "../../components/common/Table/TableHeader";
import ColorSelectionInput from "../../components/common/Input/ColorSelectionInput";
import { getAllSubmissions } from "../../services/operations/headAccountAPI";
import ThreeDotHorizontal from "../../components/common/Button/ThreeDotHorizontal";
import ThreeDot from "../../components/common/Button/ThreeDot";
import viewImg from "../../assets/icons/view.svg";
import EmptyState from "../../components/common/EmptyState/EmptyState";
import Footer from "../../components/common/Footer/Footer";
import { useSelector } from "react-redux";
import { submissionsStatusAndTimeline } from "../../services/operations/candidateAPI";
// (ThreeDot component is already imported above)
import FiltersPanel from "../../components/common/filter/FiltersPanel";
import SortingPanel from "../../components/common/filter/SortingPanel";
import { useURLState } from "../../components/common/filter/useURLState";
import SearchDropdown from "../../components/common/filters/SearchDropdown";

// Inline SVG icons (same as JobPage) - placed after all imports to keep import statements grouped at the top
const FilterSVG = () => (
  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
    <path
      d="M4 6h16M7 12h10M10 18h4"
      stroke="#22c55e"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

const SortingSVG = () => (
  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
    <path
      d="M8 9l4-4 4 4M16 15l-4 4-4-4"
      stroke="#22c55e"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

const AllSubmission = () => {
  const [activeTab, setActiveTab] = useState(0);
  const tab = [{ name: "AllSubmissions" }];
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [submissions, setSubmissions] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  // Ref to detect clicks outside the search icon + dropdown
  const searchDropdownTriggerRef = useRef(null);
  
  // URL State Management
  const { parseURLParams, updateURLParams } = useURLState();
  const initialURLState = parseURLParams();
  
  // Search and Filter States
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const [searchData, setSearchData] = useState(initialURLState.search || {
    searchTerm: "",
    searchField: "all"
  });
  const [active, setActive] = useState(initialURLState.activeTab || 0);
  
  // View Options and Filter States - Updated to persist filter and sorting panel state
  const [showFilters, setShowFilters] = useState(initialURLState.showFilters || false);
  const [showSorting, setShowSorting] = useState(initialURLState.showSorting || false);
  
  // Filter and Sorting States
  const [filters, setFilters] = useState(initialURLState.filters || {
    status: [],
    recruiterName: [],
    jobTitle: [],
  });
  
  const [sorting, setSorting] = useState(() => {
    // Convert URL sorting back to UI format
    if (initialURLState.sorting.submissionDate) {
      return initialURLState.sorting.submissionDate === "recent" ? "Recent" : "Oldest";
    } else if (initialURLState.sorting.sortBy) {
      const { sortBy, sortOrder } = initialURLState.sorting;
      if (sortBy === "name") {
        return sortOrder === "asc" ? "A to Z" : "Z to A";
      } else if (sortBy === "candidateID") {
        return sortOrder === "asc" ? "Low to High" : "High to Low";
      } else {
        return `${sortBy}_${sortOrder}`;
      }
    }
    // Default to "Recent" sorting
    return "Recent";
  });

  // Track data refresh trigger
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Available filters and sort options for submissions
  const availableFilters = ["status"];
  const availableSortOptions = ["submissionDate"];

  const columns = [
    { label: "Candidate ID", key: "candidateID" },
    { label: "Name", key: "name" },
    { label: "Recruiter Name", key: "recruiterName" },
    { label: "Job ID", key: "jobId" },
    { label: "Job Title", key: "jobTitle" },
    { label: "Status", key: "status" },
    { label: "", key: "actions" },
  ];

  const statusOptions = [
    {
      value: "submitted",
      label: "Submitted",
      color: "text-blue-600 bg-blue-100",
    },
    {
      value: "reviewing",
      label: "Reviewing",
      color: "text-yellow-600 bg-yellow-100",
    },
    {
      value: "submitted to client",
      label: "Submitted to Client",
      color: "text-indigo-600 bg-indigo-100",
    },
    {
      value: "selected",
      label: "Selected",
      color: "text-green-600 bg-green-100",
    },
    {
      value: "awaiting offer",
      label: "Awaiting Offer",
      color: "text-orange-600 bg-orange-100",
    },
    {
      value: "accepted",
      label: "Accepted",
      color: "text-green-700 bg-green-200",
    },
    { value: "rejected", label: "Rejected", color: "text-red-600 bg-red-100" },
    {
      value: "interviewing",
      label: "Interviewing",
      color: "text-purple-600 bg-purple-100",
    },
    {
      value: "offer accepted",
      label: "Offer Accepted",
      color: "text-green-700 bg-green-200",
    },
    {
      value: "offer rejected",
      label: "Offer Rejected",
      color: "text-red-700 bg-red-200",
    },
    {
      value: "offer released",
      label: "Offer Released",
      color: "text-cyan-600 bg-cyan-100",
    },
    {
      value: "hired-under guarantee period",
      label: "Hired Under Guarantee Period",
      color: "text-teal-600 bg-teal-100",
    },
    {
      value: "guarantee period not completed",
      label: "Guarantee Period Not Completed",
      color: "text-gray-600 bg-gray-100",
    },
    {
      value: "guarantee period completed",
      label: "Guarantee Period Completed",
      color: "text-gray-800 bg-gray-200",
    },
  ];

  // Clean filters to remove empty arrays
  const cleanFilters = (filtersObj) => {
    const cleaned = {};
    Object.keys(filtersObj).forEach(key => {
      const value = filtersObj[key];
      if (Array.isArray(value) && value.length > 0) {
        cleaned[key] = value;
      } else if (typeof value === 'string' && value.trim()) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  };

  const updateSubmissionStatus = async (submissionId, newStatus) => {
    try {
      // Make API call here if needed
      await submissionsStatusAndTimeline({
        newstatus: newStatus,
        submissionId: submissionId,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  // Main API call function - Updated to include filters, sorting, and search
  const getData = async (
    page = 1, 
    limit = 10, 
    appliedFilters = {}, 
    appliedSorting = "",
    appliedSearch = {}
  ) => {
    try {
      setLoading(true);
      
      // Convert sorting string to params object
      let sortingParams = {};
      if (appliedSorting) {
        switch (appliedSorting) {
          case "Recent":
            sortingParams = { submissionDate: "recent" }; // ✅ CHANGED from postedDate
            break;
          case "Oldest":
            sortingParams = { submissionDate: "oldest" }; // ✅ CHANGED from postedDate
            break;
          case "A to Z":
            sortingParams = { sortBy: "name", sortOrder: "asc" };
            break;
          case "Z to A":
            sortingParams = { sortBy: "name", sortOrder: "desc" };
            break;
          case "Low to High":
            sortingParams = { sortBy: "candidateID", sortOrder: "asc" };
            break;
          case "High to Low":
            sortingParams = { sortBy: "candidateID", sortOrder: "desc" };
            break;
          case "name_asc":
            sortingParams = { sortBy: "name", sortOrder: "asc" };
            break;
          case "name_desc":
            sortingParams = { sortBy: "name", sortOrder: "desc" };
            break;
          case "candidateID_asc":
            sortingParams = { sortBy: "candidateID", sortOrder: "asc" };
            break;
          case "candidateID_desc":
            sortingParams = { sortBy: "candidateID", sortOrder: "desc" };
            break;
          case "jobTitle_asc":
            sortingParams = { sortBy: "jobTitle", sortOrder: "asc" };
            break;
          case "jobTitle_desc":
            sortingParams = { sortBy: "jobTitle", sortOrder: "desc" };
            break;
          default:
            if (appliedSorting.includes('_')) {
              const [sortBy, sortOrder] = appliedSorting.split('_');
              sortingParams = { sortBy, sortOrder };
            }
        }
      }
      
      // Clean filters before sending
      const cleanedFilters = cleanFilters(appliedFilters);
      
      
      // Note: You might need to update the getAllSubmissions API call to accept these new parameters
      const response = await getAllSubmissions(page, limit, user.role, cleanedFilters, sortingParams, appliedSearch);
      const results = response?.results || [];

      const mapped = results.map((submission) => ({
        id: submission._id,
        candidateID: submission.candidate?.candidateID || "—",
        name:
          `${submission.candidate?.personalDetails?.firstName || ""} ${
            submission.candidate?.personalDetails?.lastName || ""
          }`.trim() || "—",
        recruiterName:
          `${submission.recruiter?.name?.firstName || ""} ${
            submission.recruiter?.name?.lastName || ""
          }`.trim() || "—",
        jobId: submission.job?.jobId || "—",
        jobTitle: submission.job?.jobTitle || "—",
        status: submission.status || "submitted",
        actions: (
          <ThreeDot
            dropdownSize="w-32"
            buttonDropDown={
              <ul className="py-0" role="none">
                <li>
                  <div
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(
                        `/submissions/submissionDetails?candidateId=${submission?.candidate?.candidateID}&submissionId=${submission?.submissionId}`
                      );
                    }}
                    className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                    role="menuitem"
                  >
                    <img src={viewImg} alt="View" />
                    View
                  </div>
                </li>
              </ul>
            }
          />
        ),
      }));

      setSubmissions(mapped); // Uncomment this line to see real data
      setTotalResults(response?.totalSubmissions || 0);
      setTotalPages(response?.totalPages || 1);
    } catch (err) {
      console.error("❌ API Error:", err);
      setSubmissions([]);
      setTotalResults(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Search handlers
  const handleSearchClose = () => {
    setShowSearchDropdown(false);
  };

  const handleSearch = (searchInputs) => {
    setSearchData(searchInputs);
    setCurrentPage(1);
    setRefreshTrigger(prev => prev + 1);
  };

  // Filter and Sorting handlers
  const handleFilterChange = (filterType, value) => {
    
    // Update filters state immediately and correctly
    setFilters(prevFilters => {
      const newFilters = {
        ...prevFilters,
        [filterType]: value,
      };
      return newFilters;
    });
  };

  const handleSortingChange = (sortValue) => {
    setSorting(sortValue);
  };

  const handleClearAllFilters = () => {
    const clearedFilters = { status: [], recruiterName: [], jobTitle: [] };
    const clearedSearch = { searchTerm: "", searchField: "all" };
    
    setFilters(clearedFilters);
    setSorting("");
    setSearchData(clearedSearch);
    setCurrentPage(1);
    
    // Don't close panels here - let individual panel clear buttons handle their own closing
    // This function is called from individual panels, so they'll handle their own state
    
    setRefreshTrigger(prev => prev + 1);
  };

  // Apply filters with state synchronization - Modified to NOT close panels
  const handleApplyFilters = async () => {
    
    // Only close the search dropdown
    setShowSearchDropdown(false);
    
    // Use a small delay to ensure all state updates are complete
    setTimeout(() => {
      setRefreshTrigger(prev => prev + 1);
    }, 50); // Small delay to ensure state is updated
  };

  // Effect to fetch data
  useEffect(() => {
    getData(currentPage, pageSize, filters, sorting, searchData);
  }, [currentPage, pageSize, refreshTrigger]);
  
  // Set default sorting to "Recent" on initial load
  useEffect(() => {
    if (!initialURLState.sorting.submissionDate && sorting === "Recent") {
      setRefreshTrigger(prev => prev + 1);
    }
  }, []);

  // Update URL when state changes - Modified to include showFilters state
  useEffect(() => {
    const sortingForURL = {};
    if (sorting) {
      if (sorting === "Recent") {
        sortingForURL.submissionDate = "recent"; // ✅ CHANGED from postedDate
      } else if (sorting === "Oldest") {
        sortingForURL.submissionDate = "oldest"; // ✅ CHANGED from postedDate
      } else if (sorting.includes("_")) {
        const [sortBy, sortOrder] = sorting.split("_");
        sortingForURL.sortBy = sortBy;
        sortingForURL.sortOrder = sortOrder;
      }
    }

    updateURLParams({
      activeTab: active,
      filters: filters,
      sorting: sortingForURL,
      search: searchData.searchTerm ? searchData : {},
      showFilters: showFilters, // Add this to persist filter panel state
      showSorting: showSorting, // Add this to persist sorting panel state
    });
  }, [active, filters, sorting, searchData, showFilters, showSorting, updateURLParams]);

  // Close the search dropdown when clicking anywhere outside the trigger / dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        showSearchDropdown &&
        searchDropdownTriggerRef.current &&
        !searchDropdownTriggerRef.current.contains(event.target)
      ) {
        setShowSearchDropdown(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showSearchDropdown]);

  // Handle tab switching - Keep filters open if they were open
  const handleTabChange = (newActiveTab) => {
    setActiveTab(newActiveTab);
    setActive(newActiveTab);
    // Don't reset showFilters here - let it persist
  };

  return (
    <div className="flex flex-col min-h-screen pb-12">
      <TabNav
        nav={tab}
        active={activeTab}
        setActive={handleTabChange} // Use the new handler
        rightSidebar={
          <div className="flex gap-2 items-center">
            {/* Search Icon */}
            <div className="relative" ref={searchDropdownTriggerRef}>
              <img
                src={searchImg}
                alt="Search"
                className="w-8 h-8 cursor-pointer"
                onClick={() => {
                  setShowSearchDropdown((prev) => !prev);
                  // We intentionally do NOT close the filter / sorting panels here
                }}
              />
              <SearchDropdown
                isOpen={showSearchDropdown}
                onClose={handleSearchClose}
                onSearch={handleSearch}
                activeTab="submissions"
              />
            </div>

            {/* ThreeDot dropdown copied from JobPage */}
            <div className="relative">
              <ThreeDotHorizontal
                dropdownSize="w-56"
                buttonDropDown={(closeDropdown) => (
                  <div className="rounded-lg shadow-md border bg-white">
                    <div className="flex items-center justify-between px-4 pt-3 pb-2 border-b">
                      <span className="font-medium text-base text-gray-900">View Option</span>
                      <button
                        className="text-gray-400 hover:text-black text-xl leading-none cursor-pointer"
                        onClick={closeDropdown}
                        aria-label="Close"
                      >
                        ×
                      </button>
                    </div>
                    <div className="flex flex-col py-2">
                      <button
                        onClick={() => {
                          setShowFilters(true);
                          setShowSorting(false);
                          setShowSearchDropdown(false); // close search dropdown when opening filter panel
                          closeDropdown();
                        }}
                        className="flex items-center gap-2 px-4 py-2 text-black hover:text-green-600 hover:bg-gray-100 text-sm font-medium text-left w-full transition-colors cursor-pointer"
                      >
                        <FilterSVG />
                        Filter
                      </button>
                      <button
                        onClick={() => {
                          setShowSorting(true);
                          setShowFilters(false);
                          setShowSearchDropdown(false); // close search dropdown when opening sorting panel
                          closeDropdown();
                        }}
                        className="flex items-center gap-2 px-4 py-2 text-black hover:text-green-600 hover:bg-gray-100 text-sm font-medium text-left w-full transition-colors cursor-pointer"
                      >
                        <SortingSVG />
                        Sorting
                      </button>
                    </div>
                  </div>
                )}
                iconOverride={true}
              />
            </div>
          </div>
        }
      />

      <FiltersPanel
        showFilters={showFilters}
        setShowFilters={setShowFilters} // Add this prop
        filters={filters}
        handleFilterChange={handleFilterChange}
        handleClearAllFilters={handleClearAllFilters}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableFilters={availableFilters}
      />

      <SortingPanel
        showSorting={showSorting}
        setShowSorting={setShowSorting} // Add this prop
        sorting={sorting}
        setSorting={handleSortingChange}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableSortOptions={availableSortOptions}
      />

      {/* Data validation - show table if data exists, otherwise show EmptyState */}
      {submissions.length > 0 ? (
        <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
          {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="text-gray-500">Loading...</div>
            </div>
          )}
          
          <table className="min-w-full bg-white">
            <TableHeader columns={columns.map((col) => col.label)} />
            <tbody>
              {submissions?.map((sub, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
                >
                  {columns.map((col) => {
                    if (col.key === "status") {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          <ColorSelectionInput
                            value={sub.status}
                            onChange={(newStatus) => {
                              const updated = [...submissions];
                              updated[index] = {
                                ...updated[index],
                                status: newStatus,
                              };
                              setSubmissions(updated);
                              updateSubmissionStatus(sub.id, newStatus);
                            }}
                            options={statusOptions}
                            disabled={loading}
                            width="w-44"
                          />
                        </td>
                      );
                    } else {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {sub[col.key] || "—"}
                        </td>
                      );
                    }
                  })}
                </tr>
              ))}
            </tbody>
          </table>

          <PaginationFooter
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            setPageSize={setPageSize}
            setCurrentPage={setCurrentPage}
            totalResults={totalResults}
          />
        </div>
      ) : (
        <div className="mt-2">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="text-gray-500">Loading...</div>
            </div>
          ) : (
            <EmptyState
              title="No Submissions Found"
              description="There are no candidate submissions to display at the moment. Submissions will appear here once candidates are submitted to jobs."
            />
          )}
        </div>
      )}
    </div>
  );
};

export default AllSubmission;