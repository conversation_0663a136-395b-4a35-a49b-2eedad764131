import React, { useEffect, useRef, useState } from "react";

const ThreeDot = ({ buttonDropDown = "", dropdownSize = "w-56" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <div ref={dropdownRef}>
        <div className="flex items-center gap-2">
          <img
            aria-expanded={isOpen}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setIsOpen(!isOpen);
            }}
            src={"/assets/icons/threedot.svg"}
            className="w-7 h-5 cursor-pointer object-contain block flex-none"
          />
        </div>

        {isOpen && (
          <div
            className={`absolute right-0 z-50 my-1 me-3 ${dropdownSize} text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-md`}
          >
            {buttonDropDown}
          </div>
        )}
      </div>
    </>
  );
};

export default ThreeDot;
