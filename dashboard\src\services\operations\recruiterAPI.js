import { apiConnector } from "../apiConnector";
import { recruiterEndpoints } from "../apis";

const { GET_RECRUITER_DETAILS, GET_ALL_RECRUITERS, GET_RECRUITER_BY_JOBID } =
  recruiterEndpoints;

export const getRecruiterCoinHistory = async (page = 1, limit = 10) => {
  const response = await apiConnector(
    "GET",
    recruiterEndpoints.GET_COIN_HISTORY,
    {},
    {},
    { page, limit }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const getRecruitersByJobId = async (jobId) => {
  const response = await apiConnector(
    "GET",
    `${GET_RECRUITER_BY_JOBID}/${jobId}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const getRecruiterDetails = async (recruiterID, page, limit) => {
  const response = await apiConnector(
    "GET",
    GET_RECRUITER_DETAILS,
    {},
    {},
    { recruiterID, page, limit }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const getAllRecruiters = async (page = 1, limit = 10) => {
  const response = await apiConnector(
    "GET",
    GET_ALL_RECRUITERS,
    {},
    {},
    { page, limit }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};
