import React from "react";

const StatsCard1 = ({ lable = "", value = "", width = "w-full", onClick }) => {
  return (
    <>
      <div
        onClick={onClick}
        className={`bg-white rounded-2xl shadow-sm p-4 cursor-pointer  border border-white  hover:border-[#3E9900]  ${width}`}
      >
        <div>
          <p className="text-gray-900 text-2xl font-bold">{value}</p>
          <p className="text-gray-500 text-sm font-medium">{lable}</p>
        </div>
      </div>
    </>
  );
};

export default StatsCard1;
