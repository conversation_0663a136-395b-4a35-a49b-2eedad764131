import ReactDOM from "react-dom";

const Modal = ({
  isOpen,
  onClose,
  children,
  width = "p-6 w-full max-w-md",
}) => {
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="fixed inset-0  backdrop-blur-[4px]  flex items-center justify-end z-50">
      <div
        className={`bg-white rounded-xl min-h-screen  ${width} shadow-lg relative`}
        style={{
          opacity: "100%",
        }}
      >
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 cursor-pointer"
          onClick={onClose}
        >
          &#10005;
        </button>
        {children}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
