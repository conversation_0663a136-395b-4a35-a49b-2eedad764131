import { useFormik } from "formik";
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { login } from "../../../../services/operations/authAPI";
import { useDispatch } from "react-redux";
import { loginToken, userInfo } from "../../../../redux/reducer/auth.slice";
import { toast } from "react-toastify";

const LoginSections = () => {
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const Login = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validate: (data) => {
      let errors = {};

      if (!data.email) {
        errors.email = "Email is required.";
      } else if (
        !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(data.email)
      ) {
        errors.email = "Invalid email address. E.g. <EMAIL>";
      }

      if (!data.password) {
        errors.password = "Password is required.";
      }

      return errors;
    },
    onSubmit: (data) => {
      handleLogin(data);
    },
  });

  async function handleLogin(data) {
    try {
      setLoading(true);
      const response = await login({
        ...data,
        email: data?.email?.trim()?.toLowerCase(),
      });
      if (response?.user.role === "recruiter") {
        if (response?.user?.emailVerified === false) {
          navigate("/verification-failed?userId=" + response?.user?.userId);
          setLoading(false);
          return;
        } else {
          if (response?.accessToken) {
            dispatch(
              loginToken({
                accessToken: response?.accessToken,
                refreshToken: response?.refreshToken,
              })
            );
            dispatch(userInfo({ ...response.user }));
            navigate(`/`);
          } else {
            toast.error(response?.message || "Invalid credentials.");
          }
        }
      } else {
        if (response?.accessToken) {
          dispatch(
            loginToken({
              accessToken: response?.accessToken,
              refreshToken: response?.refreshToken,
            })
          );
          dispatch(userInfo({ ...response.user }));
          navigate(`/`);
        } else {
          toast.error(response?.message || "Invalid credentials.");
        }
      }
      setLoading(false);
    } catch (error) {
      toast.error(
        error?.response?.data?.message ||
          error?.message ||
          "Login failed. Please try again."
      );
      setLoading(false);
    }
  }

  const isFormFieldValid = (name) =>
    !!(Login.touched[name] && Login.errors[name]);
  const getFormErrorMessage = (name) => {
    return (
      isFormFieldValid(name) && (
        <small className="text-red-500">{Login.errors[name]}</small>
      )
    );
  };

  const isFormValid =
    Object.values(Login.values).filter((item) => item !== "").length > 0 &&
    Object.keys(Login.errors).length === 0;

  return (
    <section className=" w-[100%] md:w-[60%] lg:w-[50%] py-15    flex  items-center justify-center">
      <div className="w-[80%] md:w-[60%] lg:w-[45%] mx-auto flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          {/* <img src={logo} alt="logo" width={100} height={100} /> */}
          <img
            src={"/assets/images/icon.png"}
            alt="icon"
            width={50}
            height={100}
          />
          <h2 className="text-4xl font-bold">Login</h2>
          <p className="text-gray-600 text-sm leading-5">
            Lets get you started
          </p>
        </div>
        <form
          onSubmit={Login.handleSubmit}
          className="flex flex-col items-center justify-center gap-6"
        >
          <div className="flex flex-col w-full gap-2">
            <label
              htmlFor="email"
              className="text-sm font-medium text-[#374151]"
            >
              Email Address
            </label>
            <input
              type="email"
              name="email"
              id="email"
              value={Login.values.email}
              onChange={Login.handleChange}
              className="border border-[#D1D5DB] py-2 px-3 rounded-md  focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none "
              placeholder="<EMAIL>"
              required
            />
            {getFormErrorMessage("email")}
          </div>
          <div className="flex flex-col w-full gap-2">
            <label
              htmlFor="password"
              className="text-sm font-medium text-[#374151]"
            >
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                id="password"
                value={Login.values.password}
                onChange={Login.handleChange}
                className="border border-[#D1D5DB] py-2 px-3 rounded-md focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none w-full"
                placeholder="Password"
                required
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2"
                onClick={() => setShowPassword((prev) => !prev)}
                tabIndex={-1}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                <img
                  src={
                    showPassword
                      ? "/assets/icons/vis.svg"
                      : "/assets/icons/not_vis.svg"
                  }
                  alt={showPassword ? "Hide password" : "Show password"}
                  className="w-5 h-5"
                />
              </button>
              {getFormErrorMessage("password")}
            </div>
          </div>
          <div className="w-full flex justify-end">
            <Link
              to={"/forgot-password"}
              className="text-sm text-blue-600 underline hover:text-blue-800 "
            >
              Forgot your password?
            </Link>
          </div>

          <button
            type="submit"
            className={`w-full py-2 px-3 rounded-md ${
              isFormValid
                ? "bg-[#65FF00] text-[#102108] font-medium cursor-pointer"
                : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
            disabled={!isFormValid || loading}
          >
            {loading ? "Logging in..." : "Login"}
          </button>
          <div className="w-full flex justify-center items-center gap-1">
            Don't have an account?
            <Link
              to={"/signup"}
              className="text-sm text-blue-600 underline hover:text-blue-800 "
            >
              Sign Up
            </Link>
          </div>
        </form>
      </div>
    </section>
  );
};

export default LoginSections;
