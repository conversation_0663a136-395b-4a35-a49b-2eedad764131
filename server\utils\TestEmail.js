const mailSender = require("./mailSender");

const TestEmail = async () => {
  try {
    // Send test email
    const info = await mailSender(
      "<EMAIL>",
      "testing email",
      "This is a test email"
    );
    console.log("Email test completed");

    return info;
  } catch (error) {
    console.error("Error sending test email:", error.message);
    throw new Error("Failed to send test email");
  }
};
module.exports = TestEmail;
