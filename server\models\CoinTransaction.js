const mongoose = require("mongoose");

const coinTransactionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    relatedJobId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Job",
      required: function () {
        return (
          this.transactionType === "spent" || this.transactionType === "earned"
        );
      },
    },
    transactionType: {
      type: String,
      enum: ["earned", "spent", "purchased", "refunded", "bonus", "penalty"],
      required: true,
    },
    spendType: {
      type: String,
      enum: ["workOn", "instantSubmit"],
      required: function () {
        return this.transactionType === "spent";
      },
    },
    earnedType: {
      type: String,
      enum: ["Interviewing", "Hired"],
      required: function () {
        return this.transactionType === "earned";
      },
    },
    quantity: {
      type: Number,
      required: true,
      min: 0,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    balanceBefore: {
      type: Number,
      required: true,
      min: 0,
    },
    balanceAfter: {
      type: Number,
      required: true,
      min: 0,
    },

    relatedSubmissionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "JobSubmission",
      required: function () {
        return (
          (this.transactionType === "spent" ||
            this.transactionType === "earned") &&
          this.spendType === "instantSubmit"
        );
      },
    },
    description: {
      type: String,
    },
    metadata: {
      refundReason: String, // for refunds
      bonusReason: String, // for bonus coins
    },
    status: {
      type: String,
      enum: ["pending", "completed", "failed", "cancelled"],
      default: "completed",
    },
    createdAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("CoinTransaction", coinTransactionSchema);
