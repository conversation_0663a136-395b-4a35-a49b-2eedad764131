const countries = require("./countries.json"); // This should point to server-side copy

// Normalize all valid country names to lowercase + space
const validCountries = countries.map(c =>
  c.name.toLowerCase().replace(/-/g, " ")
);

// Takes input like "united-states" and returns normalized "united states" if valid
function normalizeCountry(input) {
  const cleaned = input.toLowerCase().replace(/-/g, " ").trim();
  return validCountries.includes(cleaned) ? cleaned : null;
}

module.exports = {
  normalizeCountry,
  validCountries,
};
