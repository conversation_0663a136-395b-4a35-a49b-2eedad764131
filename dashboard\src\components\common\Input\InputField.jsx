import React from "react";
import DOMPurify from "dompurify";

const formatDate = (value) => {
  if (!value) return "";
  if (/^\d{4}-\d{2}-\d{2}$/.test(value)) return value;
  const date = new Date(value);
  return !isNaN(date) ? date.toISOString().slice(0, 10) : value;
};

const InputField = ({
  label,
  required = false,
  placeholder,
  disable = false,
  value,
  onChange,
  name,
  id,
  errorMessage,
  onBlur,
  readOnly,
  disabled = false,
  type = "text",
  minmax,
  css = "border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm placeholder-gray-400",
  iconRight, // Optional icon like dropdown arrow
}) => {
  let min, max;

  if (minmax && /^[0-9]+-[0-9]+$/.test(minmax)) {
    [min, max] = minmax.split("-").map(Number);
  }

  const sanitizeInput = (value) => {
    return DOMPurify.sanitize(value, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
    });
  };

  const handleInputChange = (e) => {
    const val = e.target.value;

    const sanitizedValue = sanitizeInput(val);

    const newEvent = {
      ...e,
      target: {
        ...e.target,
        name: e.target.name,
        value: sanitizedValue,
      },
    };
    if (minmax) {
      const num = Number(sanitizedValue);
      if (sanitizedValue === "" || (num >= min && num <= max)) {
        onChange(newEvent);
      }
    } else {
      onChange(newEvent);
    }
  };
  return (
    <div className="flex flex-col space-y-1">
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-800">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          id={id}
          type={type}
          name={name}
          value={readOnly ? formatDate(value) : value || ""}
          disabled={disabled}
          placeholder={placeholder}
          onChange={!readOnly ? handleInputChange : undefined}
          onBlur={onBlur}
          readOnly={readOnly}
          className={`${css} w-full pr-8`}
        />
        {iconRight && (
          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
            {iconRight}
          </span>
        )}
      </div>
      {errorMessage && (
        <span className="text-xs text-red-600">{errorMessage}</span>
      )}
    </div>
  );
};

export default InputField;
