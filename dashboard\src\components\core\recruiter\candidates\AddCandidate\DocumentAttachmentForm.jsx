import React, { useEffect, useState, useCallback } from "react";
import { useFormikContext } from "formik";
import FileInput from "../../../../common/Input/FileInput";
import { toast } from "react-toastify";

const DocumentForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    setFieldValue,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  const [previewFile, setPreviewFile] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewError, setPreviewError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  const handleFileChange = (field) => (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file size (e.g., 10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        setFieldValue(field, null);
        toast.error(
          "File size exceeds 10MB limit. Please choose a smaller file."
        );
        return;
      }

      // Validate file type
      const allowedTypes = [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/gif",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain",
      ];

      if (!allowedTypes.includes(file.type)) {
        setFieldValue(field, null);
        toast.error(
          "File type not supported. Please upload PDF, Word, or image files."
        );
        return;
      }

      setFieldValue(field, file);
    }
  };

  const handleFileRemove = (field) => () => {
    setFieldValue(field, null);
  };

  // Enhanced function to get clean display name for files
  const getFileName = (file) => {
    if (!file) return "";

    // Handle existing files (objects with metadata)
    if (file && typeof file === "object" && file.isExisting && file.name) {
      return cleanFileName(file.name);
    }

    // Handle new File objects
    if (file instanceof File) {
      return file.name;
    }

    // Handle S3 URLs - extract filename from URL
    if (typeof file === "string" && isS3Url(file)) {
      const urlParts = file.split("/");
      const fileName = urlParts[urlParts.length - 1];
      return cleanFileName(fileName);
    }

    // Handle legacy string paths (fallback)
    if (typeof file === "string") {
      return cleanFileName(file);
    }

    return "";
  };

  // Function to clean filename by removing timestamp prefix
  const cleanFileName = (fileName) => {
    if (!fileName) return "";

    // Remove timestamp prefix pattern like "1750758616591-"
    const timestampPattern = /^\d+-(.+)$/;
    const match = fileName.match(timestampPattern);

    if (match) {
      return match[1]; // Return filename without timestamp
    }

    // If no timestamp pattern, extract filename from URL/path
    return fileName.split("/").pop() || fileName;
  };

  // Check if a file is an S3 URL
  const isS3Url = (url) => {
    if (typeof url !== "string") return false;
    return url.includes("s3.") && url.includes("amazonaws.com");
  };

  // Check if a file exists (either new or existing)
  const hasFile = (file) => {
    if (!file) return false;
    if (file instanceof File) return true;
    if (file && typeof file === "object" && file.isExisting) return true;
    if (typeof file === "string" && file.trim()) return true;
    return false;
  };

  // Get file URL for preview with better error handling
  const getFileUrl = useCallback((file) => {
    if (!file) return null;

    try {
      // New file - create object URL
      if (file instanceof File) {
        return URL.createObjectURL(file);
      }

      // Existing file object - use the URL or construct it
      if (file && typeof file === "object" && file.isExisting) {
        if (file.url) {
          return file.url;
        }
        if (file.path) {
          return file.path;
        }
        if (file.name) {
          return `/uploads/${file.name}`;
        }
      }

      // Handle S3 URLs directly
      if (typeof file === "string" && isS3Url(file)) {
        return file;
      }

      // Legacy string (fallback)
      if (typeof file === "string") {
        return file;
      }
    } catch (error) {
      console.error("Error creating file URL:", error);
      return null;
    }

    return null;
  }, []);

  // Enhanced file type detection
  const getFileTypeFromName = (fileName) => {
    if (!fileName) return "application/octet-stream";

    const extension = fileName.split(".").pop()?.toLowerCase() || "";

    const typeMap = {
      // Images
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      bmp: "image/bmp",
      webp: "image/webp",
      svg: "image/svg+xml",

      // Documents
      pdf: "application/pdf",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      txt: "text/plain",
      rtf: "application/rtf",
    };

    return typeMap[extension] || "application/octet-stream";
  };

  // Get file type from file object
  const getFileType = (file) => {
    if (!file) return "application/octet-stream";

    if (file instanceof File && file.type) {
      return file.type;
    }

    if (file && typeof file === "object" && file.isExisting) {
      if (file.type) {
        return file.type;
      }
      if (file.name) {
        return getFileTypeFromName(file.name);
      }
    }

    // For S3 URLs, extract filename and determine type
    if (typeof file === "string" && isS3Url(file)) {
      const fileName = getFileName(file);
      return getFileTypeFromName(fileName);
    }

    const fileName = getFileName(file);
    return getFileTypeFromName(fileName);
  };

  // Enhanced preview function with better error handling
  const handlePreview = async (file) => {
    if (!hasFile(file)) {
      console.warn("No file to preview");
      return;
    }

    setIsLoading(true);
    setPreviewError(null);

    try {
      const fileName = getFileName(file);
      const fileUrl = getFileUrl(file);
      const fileType = getFileType(file);

      if (!fileUrl) {
        throw new Error("No file URL available for preview");
      }

      if (
        (file && typeof file === "object" && file.isExisting) ||
        isS3Url(file)
      ) {
        if (!/^https?:\/\//.test(fileUrl)) {
          try {
            const response = await fetch(fileUrl, { method: "HEAD" });
            if (!response.ok) {
              throw new Error(
                `File not accessible: ${response.status} ${response.statusText}`
              );
            }
          } catch (fetchError) {
            throw new Error(`Unable to access file: ${fetchError.message}`);
          }
        }
      }

      setPreviewFile({
        name: fileName,
        type: fileType,
        url: fileUrl,
        isNewFile: file instanceof File,
        isExisting:
          (file && typeof file === "object" && file.isExisting) ||
          isS3Url(file),
      });
      setShowPreview(true);
    } catch (error) {
      console.error("Preview error:", error);
      setPreviewError(error.message);
      // Still show the modal with error message
      setPreviewFile({
        name: getFileName(file),
        type: getFileType(file),
        url: null,
        isNewFile: file instanceof File,
        isExisting:
          (file && typeof file === "object" && file.isExisting) ||
          isS3Url(file),
        error: error.message,
      });
      setShowPreview(true);
    } finally {
      setIsLoading(false);
    }
  };

  const closePreview = () => {
    // Only revoke URL if it was created for a new file
    if (previewFile?.isNewFile && previewFile?.url) {
      URL.revokeObjectURL(previewFile.url);
    }
    setPreviewFile(null);
    setShowPreview(false);
    setPreviewError(null);
  };

  // Cleanup object URLs when component unmounts
  useEffect(() => {
    return () => {
      if (previewFile?.isNewFile && previewFile?.url) {
        URL.revokeObjectURL(previewFile.url);
      }
    };
  }, [previewFile]);

  const renderPreviewModal = () => {
    if (!showPreview || !previewFile) return null;

    const isImage = previewFile.type?.startsWith("image/");
    const isPDF = previewFile.type === "application/pdf";
    const hasError = previewFile.error || previewError;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] w-full overflow-hidden">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="text-lg font-medium text-gray-900">
              {previewFile.name}
            </h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">{previewFile.type}</span>
              {previewFile.isExisting && (
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                  Existing File
                </span>
              )}
              {previewFile.isNewFile && (
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  New File
                </span>
              )}
              {hasError && (
                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                  Error
                </span>
              )}
              <button
                onClick={closePreview}
                className="text-gray-400 hover:text-gray-600"
                type="button"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div className="p-4 overflow-auto max-h-[calc(90vh-120px)]">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                <p className="text-gray-500 mt-4">Loading preview...</p>
              </div>
            ) : hasError ? (
              <div className="text-center py-8">
                <svg
                  className="w-16 h-16 text-red-400 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-red-500 font-medium mb-2">Preview Error</p>
                <p className="text-sm text-gray-600 mb-4">{hasError}</p>
                <p className="text-xs text-gray-400">
                  File: {previewFile.name}
                </p>
                {previewFile.url && (
                  <a
                    href={previewFile.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-4 text-blue-500 hover:underline"
                  >
                    Try Direct Download
                  </a>
                )}
              </div>
            ) : isImage && previewFile.url ? (
              <div className="text-center">
                <img
                  src={previewFile.url}
                  alt={previewFile.name}
                  className="max-w-full h-auto mx-auto"
                  onLoad={() => console.log("Image loaded successfully")}
                  onError={(e) => {
                    console.error("Image load error:", previewFile.url);
                    setPreviewError("Unable to load image preview");
                  }}
                />
              </div>
            ) : isPDF && previewFile.url ? (
              <div>
                <iframe
                  src={previewFile.url}
                  className="w-full h-96"
                  title={previewFile.name}
                  onLoad={() => console.log("PDF loaded successfully")}
                  onError={() => {
                    console.error("PDF preview failed for:", previewFile.name);
                    setPreviewError("PDF preview not available");
                  }}
                />
              </div>
            ) : (
              <div className="text-center py-8">
                <svg
                  className="w-16 h-16 text-gray-400 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p className="text-gray-500">
                  Preview not available for this file type.
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  File: {previewFile.name}
                </p>
                <p className="text-xs text-gray-400">
                  Type: {previewFile.type}
                </p>
                {previewFile.url && (
                  <a
                    href={previewFile.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-4 text-blue-500 hover:underline"
                  >
                    Download File
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const isYes = (val) => val && val.toLowerCase() === "yes";

  const showBLS = isYes(values.certification?.blsCertification);
  const showACLS = isYes(values.certification?.aclsPalsNals);
  const showFlu = isYes(values.healthAndCompliance?.fluVaccination);
  const showCovid = values.healthAndCompliance?.covid19Status === "fully";

  return (
    <>
      <form
        onSubmit={handleSubmit}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        <div>
          <FileInput
            label="Resume"
            name="documentAttachments.resume"
            value={values.documentAttachments.resume}
            onChange={handleFileChange("documentAttachments.resume")}
            onRemove={handleFileRemove("documentAttachments.resume")}
            onPreview={() => handlePreview(values.documentAttachments.resume)}
            required
            errorMessage={
              touched.documentAttachments?.resume &&
              errors.documentAttachments?.resume
            }
            initialFileName={getFileName(values.documentAttachments.resume)}
            hasFile={hasFile(values.documentAttachments.resume)}
          />
        </div>

        <div>
          <FileInput
            label="Cover Letter"
            name="documentAttachments.coverLetter"
            value={values.documentAttachments.coverLetter}
            onChange={handleFileChange("documentAttachments.coverLetter")}
            onRemove={handleFileRemove("documentAttachments.coverLetter")}
            onPreview={() =>
              handlePreview(values.documentAttachments.coverLetter)
            }
            required
            errorMessage={
              touched.documentAttachments?.coverLetter &&
              errors.documentAttachments?.coverLetter
            }
            initialFileName={getFileName(
              values.documentAttachments.coverLetter
            )}
            hasFile={hasFile(values.documentAttachments.coverLetter)}
          />
        </div>

        <div>
          <FileInput
            label="License Copy"
            name="documentAttachments.licenseCopy"
            value={values.documentAttachments.licenseCopy}
            onChange={handleFileChange("documentAttachments.licenseCopy")}
            onRemove={handleFileRemove("documentAttachments.licenseCopy")}
            // required
            onPreview={() =>
              handlePreview(values.documentAttachments.licenseCopy)
            }
            initialFileName={getFileName(
              values.documentAttachments.licenseCopy
            )}
            hasFile={hasFile(values.documentAttachments.licenseCopy)}
          />
        </div>

        {showBLS && (
          <div>
            <FileInput
              label="BLS Certification"
              name="documentAttachments.blsCertificate"
              value={values.documentAttachments.blsCertificate}
              onChange={handleFileChange("documentAttachments.blsCertificate")}
              onRemove={handleFileRemove("documentAttachments.blsCertificate")}
              // required
              onPreview={() =>
                handlePreview(values.documentAttachments.blsCertificate)
              }
              initialFileName={getFileName(
                values.documentAttachments.blsCertificate
              )}
              hasFile={hasFile(values.documentAttachments.blsCertificate)}
            />
          </div>
        )}
        {showACLS && (
          <div>
            <FileInput
              label="CLS / PALS / NALS (as applicable)"
              name="documentAttachments.aclsPalsNalsCertificate"
              value={values.documentAttachments.aclsPalsNalsCertificate}
              // required
              onChange={handleFileChange(
                "documentAttachments.aclsPalsNalsCertificate"
              )}
              onRemove={handleFileRemove(
                "documentAttachments.aclsPalsNalsCertificate"
              )}
              onPreview={() =>
                handlePreview(
                  values.documentAttachments.aclsPalsNalsCertificate
                )
              }
              initialFileName={getFileName(
                values.documentAttachments.aclsPalsNalsCertificate
              )}
              hasFile={hasFile(
                values.documentAttachments.aclsPalsNalsCertificate
              )}
            />
          </div>
        )}
        {showFlu && (
          <div>
            <FileInput
              label="Flu Vaccination"
              name="documentAttachments.fluVaccinationProof"
              value={values.documentAttachments.fluVaccinationProof}
              // required
              onChange={handleFileChange(
                "documentAttachments.fluVaccinationProof"
              )}
              onRemove={handleFileRemove(
                "documentAttachments.fluVaccinationProof"
              )}
              onPreview={() =>
                handlePreview(values.documentAttachments.fluVaccinationProof)
              }
              initialFileName={getFileName(
                values.documentAttachments.fluVaccinationProof
              )}
              hasFile={hasFile(values.documentAttachments.fluVaccinationProof)}
            />
          </div>
        )}
        {showCovid && (
          <div>
            <FileInput
              label="COVID Vaccination"
              name="documentAttachments.covid19VaccinationProof"
              value={values.documentAttachments.covid19VaccinationProof}
              // required
              onChange={handleFileChange(
                "documentAttachments.covid19VaccinationProof"
              )}
              onRemove={handleFileRemove(
                "documentAttachments.covid19VaccinationProof"
              )}
              onPreview={() =>
                handlePreview(
                  values.documentAttachments.covid19VaccinationProof
                )
              }
              initialFileName={getFileName(
                values.documentAttachments.covid19VaccinationProof
              )}
              hasFile={hasFile(
                values.documentAttachments.covid19VaccinationProof
              )}
            />
          </div>
        )}
        <div>
          <FileInput
            label="Recent Skills Checklist"
            name="documentAttachments.recentSkillChecklist"
            value={values.documentAttachments.recentSkillChecklist}
            // required
            onChange={handleFileChange(
              "documentAttachments.recentSkillChecklist"
            )}
            onRemove={handleFileRemove(
              "documentAttachments.recentSkillChecklist"
            )}
            onPreview={() =>
              handlePreview(values.documentAttachments.recentSkillChecklist)
            }
            initialFileName={getFileName(
              values.documentAttachments.recentSkillChecklist
            )}
            hasFile={hasFile(values.documentAttachments.recentSkillChecklist)}
          />
        </div>
      </form>

      {renderPreviewModal()}
    </>
  );
};

export default DocumentForm;
