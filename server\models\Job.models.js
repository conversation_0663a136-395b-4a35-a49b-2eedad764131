const mongoose = require("mongoose");
const { auditLog } = require("./AuditLog.models");
const { createNotification } = require("../services/notificationService");
const recruiterProfile = require("./RecruiterProfile.models");

const jobSchema = new mongoose.Schema(
  {
    jobTitle: {
      type: String,
      required: true,
    },
    industry: {
      type: String,
      required: true,
    },
    jobId: {
      type: String,
    },
    externalJobId: {
      type: String,
    },
    visibility: {
      type: Boolean,
      default: false,
    },

    commission: {
      amount: {
        type: Number,
        required: true,
      },
      currency: {
        type: String,
        default: "USD",
      },
    },

    location: {
      country: {
        type: String,
        required: true,
      },
      state: {
        type: String,
      },
      city: {
        type: String,
      },
      zipCode: {
        type: String,
      },
    },

    experience: {
      min: {
        type: Number,
        required: true,
      },
      max: {
        type: Number,
        required: true,
      },
      unit: {
        type: String,
        enum: ["years", "months"],
        default: "years",
      },
    },

    jobStatus: {
      type: String,
      enum: [
        "Active",
        "Inactive",
        "Onhold",
        "Holdbyclient",
        "Filled",
        "Cancelled",
        "Closed",
      ],
      default: "Active",
    },

    jobType: {
      type: String,
      enum: ["full-time", "contract"],
      required: true,
    },

    // if contract then it required
    payRate: {
      type: String,
      enum: ["Hourly", "Bi Weekly", "Monthly", "Yearly"],
    },

    salary: {
      min: { type: Number },
      max: { type: Number },
      currency: { type: String, default: "USD" },
    },

    remote: {
      type: Boolean,
      default: false,
    },
    requiredHoursPerWeek: {
      type: Number,
    },

    priority: {
      type: String,
      enum: ["low priority", "medium priority", "high priority"],
      required: true,
    },
    guaranteePeriod: {
      type: Number,
    },
    openings: {
      type: Number,
      required: true,
    },

    clientname: {
      type: String,
    },
    jobProfile: {
      type: String,
    },
    primarySkills: [
      {
        type: String,
        required: true,
      },
    ],
    benefits: [
      {
        type: String,
      },
    ],
    jobDescription: {
      type: String,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    accountManager: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      userID: {
        type: String,
      },
      assignAt: {
        type: Date,
      },
    },
    updateOn: {
      type: [
        {
          _id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
          },
          userID: {
            type: String,
          },
          updateDate: {
            type: Date,
            default: Date.now,
          },
        },
      ],
      default: undefined,
    },
  },
  {
    timestamps: true,
  }
);

// create audit log
async function trackChanges(context, next) {
  try {
    if (context instanceof mongoose.Document) {
      // save
      if (!context.isNew) {
        const originalDocs = await context.constructor
          .findById(context._id)
          .lean();
        const updatedFields = context.modifiedPaths();

        let updateOnField = null;
        if ("updateOn" in updatedFields) {
          updateOnField = updatedFields.updateOn;
          delete updatedFields.updateOn;
        }

        for (const [key, newValue] of Object.entries(updatedFields)) {
          const originalValue = originalDocs?.[key];
          const isDifferent =
            JSON.stringify(originalValue) !== JSON.stringify(newValue);
          if (!isDifferent) {
            delete updatedFields[key];
          }
        }

        context._changes = {
          update: updatedFields,
          originalDocs: originalDocs,
          updateBy: updateOnField,
        };
      }
    } else {
      // updateOne, findOneAndUpdate, updateMany
      const update = context.getUpdate();
      const updatedFields = {
        ...(update.$set || {}),
        ...(update.$push || {}),
        ...(update.$addToSet || {}),
        ...(update.$inc || {}),
      };
      let updateOnField = null;
      if ("updateOn" in updatedFields) {
        updateOnField = updatedFields.updateOn;
        delete updatedFields.updateOn;
      }

      if (!updatedFields) return next();
      const originalDocs = await context.model
        .findOne(context.getQuery())
        .lean();

      for (const [key, newValue] of Object.entries(updatedFields)) {
        const originalValue = originalDocs?.[key];
        const isDifferent =
          JSON.stringify(originalValue) !== JSON.stringify(newValue);
        if (!isDifferent) {
          delete updatedFields[key];
        }
      }
      context._changes = {
        update: updatedFields,
        originalDocs,
        updateBy: updateOnField,
      };
    }

    const updateFields = context._changes;

    function visibilityToCalculate(item, updateFields) {
      const isFieldForInternal = [];
      if (!item?.visibility) {
        return "internal";
      } else if (
        isFieldForInternal?.filter((incItem) =>
          updateFields.includes(incItem)
        ) > 0
      ) {
        return "internal";
      } else {
        return "external";
      }
    }

    if (Object.keys(updateFields?.update)?.length > 0) {
      // update field------------
      const visibility = visibilityToCalculate(
        updateFields?.originalDocs,
        Object.keys(updateFields?.update)
      );
      const auditLogInstance = new auditLog({
        currentJobStatus: updateFields?.update?.jobStatus
          ? updateFields?.update?.jobStatus
          : updateFields?.originalDocs?.jobStatus,
        previousJobStatus: updateFields?.originalDocs?.jobStatus,
        userId: updateFields?.updateBy?._id,
        JobId: updateFields?.originalDocs?._id,
        updateFields: updateFields?.update,
        visibleTo: visibility,
      });

      await auditLogInstance.save();

      let recipients = [updateFields?.originalDocs?.accountManager?._id];

      if (visibility == "external") {
        const profile = await recruiterProfile.find({
          "jobsWorkingOn.jobId": new mongoose.Types.ObjectId(
            updateFields?.originalDocs?._id
          ),
          "jobsWorkingOn.isActive": true,
          "jobsWorkingOn.status": "assigned",
        });
        profile?.map((profileItem) => {
          recipients.push(profileItem?.user?._id);
        });
      }

      await createNotification({
        recipients: [...new Set(recipients)],
        type: "job-update",
        relatedJobId: [updateFields?.originalDocs?.jobId],
      });
    }

    next();
  } catch (err) {
    console.log(err);

    next(err);
  }
}

// check validation for contract rate
jobSchema.pre(
  ["save", "findOneAndUpdate", "updateOne", "updateMany"],
  async function (next) {
    if (this.isNew) {
      if (this.jobType === "contract" && !this.payRate) {
        return next(new Error("Contract pay rate is required."));
      }
    } else {
      trackChanges(this, next);
    }
    next();
  }
);

const job = mongoose.model("Job", jobSchema);

module.exports = job;
