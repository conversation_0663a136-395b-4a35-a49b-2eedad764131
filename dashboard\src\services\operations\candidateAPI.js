import { apiConnector } from "../apiConnector";
import { candidateEndpoints } from "../apis";

const {
  CREATE_CANDIDATE_API,
  UPDATE_CANDIDATE_API,
  GET_ALL_CANDIDATE,
  LICENSING_UPDATE_API,
  EDUCATION_UPDATE_API,
  SKILLSANDEXPERIENCE_UPDATE_API,
  WORKHISTORTY_UPDATE_API,
  CERTIFICATION_UPDATE_API,
  HEALTHANDCOMPLIANCE_UPDATE_API,
  SUBMISSIONDETAILS_UPDATE_API,
  DOCUMENTATTACHMENT_UPDATE_API,
  GET_CANDIDATE_API,
  GET_SUBMISSION,
  GET_CANDIDATE_JOB_SUBMISSIONS_API,
  INSTANT_SUBMIT_API,
  UPDATE_SUBMISSION_STATUS_AND_TIMELINE,
  GET_CANDIDATE_TIMELINE,
  GET_CANDIDATE_WITHOUT_JOB_SUBMISSIONS,
  GET_SUBMISSION_DETAILS,
  GET_CANDIDATES_BY_JOB_RECRUITER,
GET_MAX_EXPERIENCE_API,
} = candidateEndpoints;

export const createCandidate = async (data) => {
  const response = await apiConnector("POST", CREATE_CANDIDATE_API, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getCandidatesByJobAndRecruiter = async (jobId, recruiterId) => {
  const response = await apiConnector(
    "GET",
    `${GET_CANDIDATES_BY_JOB_RECRUITER}/${jobId}/${recruiterId}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const submitCandidateToJob = async (candidateId, jobId) => {
  const response = await apiConnector(
    "POST",
    `${GET_CANDIDATE_JOB_SUBMISSIONS_API}`,
    { candidateID: candidateId, jobID: jobId }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const instantSubmitCandidate = async (candidateId, jobId) => {
  const response = await apiConnector("POST", `${INSTANT_SUBMIT_API}`, {
    candidateID: candidateId,
    jobID: jobId,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const updateCandidate = async (candidateId, data, updateType = "") => {
  const endpoint =
    updateType === "licensing"
      ? `${LICENSING_UPDATE_API}/${candidateId}`
      : updateType === "education"
      ? `${EDUCATION_UPDATE_API}/${candidateId}`
      : updateType === "skillsAndExperience"
      ? `${SKILLSANDEXPERIENCE_UPDATE_API}/${candidateId}`
      : updateType === "workHistory"
      ? `${WORKHISTORTY_UPDATE_API}/${candidateId}`
      : updateType === "certification"
      ? `${CERTIFICATION_UPDATE_API}/${candidateId}`
      : updateType === "healthAndCompliance"
      ? `${HEALTHANDCOMPLIANCE_UPDATE_API}/${candidateId}`
      : updateType === "submissionDetails"
      ? `${SUBMISSIONDETAILS_UPDATE_API}/${candidateId}`
      : updateType === "documentAttachment"
      ? `${DOCUMENTATTACHMENT_UPDATE_API}/${candidateId}`
      : `${UPDATE_CANDIDATE_API}/${candidateId}`;

  const response = await apiConnector("PATCH", endpoint, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getCandidate = async (candidateId, submissionID) => {
  const response = await apiConnector(
    "GET",
    `${GET_CANDIDATE_API}/${candidateId}`,
    {},
    {},
    { submissionID }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data.data;
};

export const getAllCandidate = async () => {
  const response = await apiConnector("GET", GET_ALL_CANDIDATE);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getSubmission = async (searchQuery, page, limit, filters = {}) => {
  // Build query parameters with proper array handling
  const queryParams = {
    submissionType: searchQuery,
    page,
    limit,
    ...filters // Spread filter parameters (location, status, experienceLevel, searchTerm, sortBy)
  };

  // Handle array parameters properly for query string
  if (queryParams.location && Array.isArray(queryParams.location)) {
    // Convert array to comma-separated string for query parameter
    queryParams.location = queryParams.location.join(',');
  }

  if (queryParams.status && Array.isArray(queryParams.status)) {
    queryParams.status = queryParams.status.join(',');
  }

  if (queryParams.jobType && Array.isArray(queryParams.jobType)) {
    queryParams.jobType = queryParams.jobType.join(',');
  }

  if (queryParams.experienceLevel && Array.isArray(queryParams.experienceLevel)) {
    queryParams.experienceLevel = queryParams.experienceLevel.join(',');
  }



  const response = await apiConnector(
    "GET",
    GET_SUBMISSION,
    {},
    {},
    queryParams
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const submissionsStatusAndTimeline = async (data) => {
  const response = await apiConnector(
    "POST",
    UPDATE_SUBMISSION_STATUS_AND_TIMELINE,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getCandidateTimeline = async (submissionID) => {
  const response = await apiConnector(
    "GET",
    `${GET_CANDIDATE_TIMELINE}/${submissionID}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getcandidateWithoutSubmission = async (jobId) => {
  const response = await apiConnector(
    "GET",
    `${GET_CANDIDATE_WITHOUT_JOB_SUBMISSIONS}/${jobId}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getSubmissionDetails = async (submissionId) => {
  const response = await apiConnector(
    "GET",
    `${GET_SUBMISSION_DETAILS}`,
    {},
    {},
    { submissionId }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getMaxExperience = async () => {
  const response = await apiConnector("GET", GET_MAX_EXPERIENCE_API);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
