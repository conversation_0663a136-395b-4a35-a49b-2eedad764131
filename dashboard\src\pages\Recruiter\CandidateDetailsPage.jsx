import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import DetailsCard from "../../components/common/cards/DetailsCard";
import { ChevronDown, ChevronUp } from "lucide-react";
import { getCandidate } from "../../services/operations/candidateAPI";
import CandidateCard from "../../components/common/cards/CandidateCard";
import { useSelector } from "react-redux";

const DocumentIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M6.9849 10.2567H13.0099V9.42333H6.9849V10.2567ZM6.9849 12.5642H13.0099V11.7308H6.9849V12.5642ZM6.9849 14.8725H10.5099V14.0392H6.9849V14.8725ZM5.5099 17.5C5.12656 17.5 4.80656 17.3717 4.5499 17.115C4.29323 16.8583 4.16462 16.5381 4.16406 16.1542V3.84583C4.16406 3.4625 4.29267 3.1425 4.5499 2.88583C4.80712 2.62917 5.1274 2.50056 5.51073 2.5H12.0807L15.8307 6.25V16.1542C15.8307 16.5375 15.7024 16.8578 15.4457 17.115C15.1891 17.3722 14.8685 17.5006 14.4841 17.5H5.5099ZM11.6641 6.66667H14.9974L11.6641 3.33333V6.66667Z"
      fill="#27364B"
    />
  </svg>
);

const stepCards = [
  { key: "personalDetails", title: "Personal Details" },
  { key: "licensing", title: "Licensing (Only for Contractual)" },
  { key: "certification", title: "Certifications" },
  { key: "education", title: "Education" },
  { key: "workHistory", title: "Work History (For Contractual)" },
  { key: "skillsAndExperience", title: "Skill & Experience" },
  { key: "healthAndCompliance", title: "Health & Compliance" },
  { key: "submissionDetails", title: "Submission Details" },
  { key: "documentAttachments", title: "Document Attachment" },
];

function formatDate(dateString) {
  if (!dateString) return "-";
  const date = new Date(dateString);
  if (isNaN(date)) return dateString;
  return date.toLocaleDateString("en-IN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

function formatDateTime(dateString) {
  if (!dateString) return "-";
  const date = new Date(dateString);
  if (isNaN(date)) return dateString;
  return date.toLocaleString("en-IN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
}

const docLabels = {
  resume: "Resume",
  coverLetter: "Cover Letter",
  licenseCopy: "License Copy",
  blsCertificate: "BLS Certification",
  aclsPalsNalsCertificate: "ACLS/PALS/NALS Certificate",
  fluVaccinationProof: "Flu Vaccination Proof",
  covid19VaccinationProof: "COVID-19 Vaccination Proof",
  recentSkillChecklist: "Recent Skills Checklist",
};

// Helper to render clickable file links for documentAttachments
const renderDocumentLinks = (attachments) => {
  if (!attachments || typeof attachments !== "object") return <span>-</span>;
  const keys = Object.keys(attachments);
  if (keys.length === 0) return <span>-</span>;

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
      {keys.map((key) => {
        const url = attachments[key];
        if (!url) return null;

        let fileName = url.split("/").pop() || "";
        if (fileName.includes("-")) {
          fileName = fileName.substring(fileName.indexOf("-") + 1);
        }

        return (
          <div key={key} className="flex flex-col min-w-[140px]">
            <span className="text-sm text-gray-500 truncate">
              {docLabels[key] || key}
            </span>
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm font-medium text-blue-600 hover:text-blue-800 truncate"
              title={fileName}
            >
              {fileName}
            </a>
          </div>
        );
      })}
    </div>
  );
};

const CandidateDetailsPage = ({ getData = () => {} }) => {
  const [searchParams] = useSearchParams();
  const candidateId = searchParams.get("candidateId");
  const submissionId = searchParams.get("submissionId");
  const [details, setDetails] = useState(null);
  const [submission, setSubmission] = useState(null);
  const [openStates, setOpenStates] = useState(() => {
    const initialOpenStates = {};
    stepCards.forEach(({ key }) => {
      initialOpenStates[key] = true;
    });
    return initialOpenStates;
  });
  const userInfo = useSelector((item) => item.auth.user);

  const formattedDetails = details && {
    ...details,
    personalDetails: {
      ...details.personalDetails,
      availableStartDate: formatDate(
        details.personalDetails?.availableStartDate
      ),
    },
    licensing: {
      ...details.licensing,
      licenseExpireDate: formatDate(details.licensing?.licenseExpireDate),
    },
    certification: {
      ...details.certification,
      blsExpiration: formatDate(details.certification?.blsExpiration),
      aclsPalsNalsExpiration: formatDate(
        details.certification?.aclsPalsNalsExpiration
      ),
    },
    healthAndCompliance: {
      ...details.healthAndCompliance,
      dateOfLastCovid19Dose: formatDate(
        details.healthAndCompliance?.dateOfLastCovid19Dose
      ),
    },
    education: Array.isArray(details.education)
      ? details.education.map((edu) => ({
          ...edu,
          graduationYear: formatDate(edu.graduationYear),
        }))
      : details.education,
    workHistory: {
      ...details.workHistory,
      employmentDate: formatDate(details.workHistory?.employmentDate),
    },
    submissionDetails: {
      ...details.submissionDetails,
      candidateAvailabilityForInterview: formatDateTime(
        details.submissionDetails?.candidateAvailabilityForInterview
      ),
    },
  };

  useEffect(() => {
    const fetchData = async () => {
      if (candidateId) {
        try {
          const data = await getCandidate(candidateId, submissionId);
          const res = data[0];
          setDetails(res);
          setSubmission(data?.submissions);
          getData(data);
        } catch (err) {
          console.log(err);
          setDetails(null);
        }
      }
    };
    fetchData();
  }, [candidateId, submissionId]);

  if (!details) return <div className="p-8">Loading...</div>;

  const handleToggle = (key) => {
    setOpenStates((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // Only for education: render as grid, not as a table box
  const renderEducationGrid = (arr) => {
    if (!Array.isArray(arr) || arr.length === 0) return <span>-</span>;
    const edu = arr[0];
    const eduArray = Object.entries(edu).map(([label, value]) => ({
      label: label
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase()),
      value: value || "-",
    }));

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
          {eduArray.map((item) => (
            <div key={item.label} className="flex flex-col min-w-[140px]">
              <span className="text-sm text-gray-500 truncate">
                {item.label}
              </span>
              <span className="text-sm font-medium text-gray-800 truncate">
                {item.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      {userInfo?.role == "recruiter" && (
        <CandidateCard
          name={`${formattedDetails?.personalDetails?.firstName} ${formattedDetails?.personalDetails?.lastName}`}
          email={formattedDetails?.personalDetails?.emailAddress}
          // candidateStatus={}
          candidateStatus={
            submission?.status ? submission?.status : "Talent Pool"
          }
        />
      )}

      {stepCards?.map(
        ({ key, title }) =>
          formattedDetails[key] && (
            <div key={key} className="w-full mb-4 last:mb-0">
              <div
                className="flex items-center w-full bg-[#F1F4F9] px-4 py-2 rounded-t-lg cursor-pointer select-none"
                onClick={() => handleToggle(key)}
              >
                <DocumentIcon className="mr-2" />
                <span className="text-sm font-semibold text-[#27364B] flex-1">
                  {title}
                </span>
                {openStates[key] ? (
                  <ChevronUp className="w-5 h-5 text-gray-600" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-600" />
                )}
              </div>
              {openStates[key] && (
                <div className="bg-white shadow-md rounded-b-lg border border-gray-200 px-4 py-4">
                  {key === "education" ? (
                    renderEducationGrid(formattedDetails[key])
                  ) : key === "documentAttachments" ? (
                    renderDocumentLinks(formattedDetails[key])
                  ) : (
                    <DetailsCard
                      details={formattedDetails[key]}
                      open
                      columns={key === "workHistory" ? 4 : 5}
                    />
                  )}
                </div>
              )}
            </div>
          )
      )}
    </div>
  );
};

export default CandidateDetailsPage;
