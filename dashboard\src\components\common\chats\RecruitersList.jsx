import { useEffect } from "react";
import { getAllRecruitersWorkingOnJob } from "../../../services/operations/jobAPI";
import RecruiterCard from "./RecruiterCard";
import { useState } from "react";

const RecruitersList = ({ jobId, setSelectedRecruiter }) => {
  const [recruiters, setRecruiters] = useState([]);

  const getRecruiters = async () => {
    try {
      const response = await getAllRecruitersWorkingOnJob(jobId);
      if (response?.success) {
        setRecruiters(response?.data?.recruiters || []);
        setSelectedRecruiter(response?.data?.recruiters[0] || null);
      }
    } catch (error) {
      console.error("Error fetching recruiters:", error);
    }
  };

  useEffect(() => {
    jobId && getRecruiters();
  }, [jobId]);

  return (
    <div className="flex flex-col w-[30%]">
      {recruiters?.map((recruiter) => (
        <div
          className="cursor-pointer"
          onClick={() => setSelectedRecruiter(recruiter)}
          key={recruiter.id}
        >
          <RecruiterCard recruiter={recruiter} />
        </div>
      ))}
    </div>
  );
};

export default RecruitersList;
