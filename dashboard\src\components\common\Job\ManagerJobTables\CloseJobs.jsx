import React, { useEffect, useState, useRef } from "react";
import PaginationFooter from "../../Table/TableFooter";
import ThreeDot from "../../Button/ThreeDot";
import TableHeader from "../../Table/TableHeader";
// import {
//   updateJobStatus,
// } from "../../services/operations/jobAPI";
import ColorSelectionInput from "../../Input/ColorSelectionInput";
import { useNavigate } from "react-router-dom";
import EmptyState from "../../EmptyState";

import { useSelector } from "react-redux";

const CloseJobs = ({
  getJob,
  updateStatus,
  filters = {},
  sorting = "",
  search = {},
  // For HeadAccountManager pattern (when jobs are passed from parent)
  jobs,
  paginationProps,
}) => {
  const navigate = useNavigate();
  const userInfo = useSelector((item) => item.auth.user);
  // Refs to track previous values and prevent unnecessary calls
  const prevFiltersRef = useRef("");
  const prevSortingRef = useRef("");
  const prevSearchRef = useRef("");
  const isInitialMount = useRef(true);
  const fetchTimeoutRef = useRef(null);

  const columns = [
    "Job ID",
    "Job Title",
    "Location",
    "Job Type",
    "Total Submission",
    "Recruiter Working",
    "Status",
    "",
  ];

  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [allJobs, setAllJobs] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);

  // If jobs are passed from parent (HeadAccountManager pattern), use them
  useEffect(() => {
    if (jobs && Array.isArray(jobs)) {
      setAllJobs(jobs);
      setLoading(false);

      if (paginationProps) {
        setTotalResults(paginationProps.totalResults || 0);
        setTotalPages(paginationProps.totalPages || 1);
        setCurrentPage(paginationProps.currentPage || 1);
        setPageSize(paginationProps.pageSize || 10);
      }
      return;
    }
  }, [jobs, paginationProps]);

  // Function to fetch data with debouncing
  const fetchData = async (
    page,
    size,
    currentFilters,
    currentSorting,
    currentSearch
  ) => {
    if (!getJob) return;

    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Debounce the API call
    fetchTimeoutRef.current = setTimeout(async () => {
      setLoading(true);
      try {
        // Check if getJob accepts filters (AccountManager pattern) or not (original pattern)
        let res;
        if (
          currentFilters ||
          currentSorting ||
          (currentSearch && currentSearch.searchTerm)
        ) {
          // AccountManager pattern - pass filters
          res = await getJob(
            page,
            size,
            currentFilters,
            currentSorting,
            currentSearch
          );
        } else {
          // Original pattern - just pagination
          res = await getJob(page, size);
        }

        setAllJobs(res?.results || []);
        setTotalResults(res?.total || 0);
        setTotalPages(res?.totalPages || 1);
      } catch (err) {
        setAllJobs([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms debounce
  };

  // Effect for pagination changes
  useEffect(() => {
    // Don't fetch if jobs are provided by parent
    if (jobs && Array.isArray(jobs)) return;

    fetchData(currentPage, pageSize, filters, sorting, search);
  }, [currentPage, pageSize]);

  // Effect for filters, sorting, search changes
  useEffect(() => {
    // Don't fetch if jobs are provided by parent
    if (jobs && Array.isArray(jobs)) return;

    const filtersStr = JSON.stringify(filters);
    const searchStr = JSON.stringify(search);

    // Check if values actually changed
    const filtersChanged = filtersStr !== prevFiltersRef.current;
    const sortingChanged = sorting !== prevSortingRef.current;
    const searchChanged = searchStr !== prevSearchRef.current;

    // Skip if nothing changed and not initial mount
    if (
      !isInitialMount.current &&
      !filtersChanged &&
      !sortingChanged &&
      !searchChanged
    ) {
      return;
    }

    // Update refs
    prevFiltersRef.current = filtersStr;
    prevSortingRef.current = sorting;
    prevSearchRef.current = searchStr;

    // Reset to page 1 when filters change (but not on initial mount)
    if (
      !isInitialMount.current &&
      (filtersChanged || sortingChanged || searchChanged)
    ) {
      setCurrentPage(1);
      fetchData(1, pageSize, filters, sorting, search);
    } else if (isInitialMount.current) {
      // Initial fetch
      fetchData(currentPage, pageSize, filters, sorting, search);
      isInitialMount.current = false;
    }
  }, [filters, sorting, search, pageSize, getJob, jobs]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  // Handle pagination changes
  const handlePageChange = (newPage) => {
    if (paginationProps && paginationProps.setCurrentPage) {
      paginationProps.setCurrentPage(newPage);
    } else {
      setCurrentPage(newPage);
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    if (paginationProps && paginationProps.setPageSize) {
      paginationProps.setPageSize(newPageSize);
    } else {
      setPageSize(newPageSize);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2 text-gray-600">Loading closed jobs...</span>
      </div>
    );
  }

  // Check if filters are applied
  const hasActiveFilters = Object.values(filters).some((filterValue) =>
    Array.isArray(filterValue) ? filterValue.length > 0 : !!filterValue
  );

  const hasActiveSearch =
    search && search.searchTerm && search.searchTerm.trim();

  // Data validation - show table if data exists, otherwise show EmptyState
  return (
    <div className="flex flex-col mb-16">
      {allJobs.length > 0 ? (
        <div className="flex flex-col">
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <TableHeader columns={columns} />
              <tbody>
                {allJobs?.map((job, index) => (
                  <tr
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(
                        `/jobs/jobdetails?jobId=${job?.jobid}&tab=jobdetails`
                      );
                    }}
                    className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                  >
                    {Object.keys(job)?.map((item) => {
                      return (
                        <td
                          className="px-4 py-3 text-start"
                          onClick={(e) => {
                            if (item === "visibility" || item === "status") {
                              e.stopPropagation();
                            }
                          }}
                        >
                          {item == "visibility" ? (
                            job[item] ? (
                              <img
                                src="/assets/icons/visible.svg"
                                className="w-5 h-5 cursor-pointer"
                              />
                            ) : (
                              <img
                                src="/assets/icons/not_visible.svg"
                                className="w-5 h-5 cursor-pointer"
                              />
                            )
                          ) : item == "status" ? (
                            <>
                              <ColorSelectionInput
                                value={job[item]}
                                onChange={(newStatus) => {
                                  const updated = [...allJobs];
                                  updated[index] = {
                                    ...updated[index],
                                    status: newStatus,
                                  };
                                  setAllJobs(updated);
                                  updateStatus(job.jobid, newStatus);
                                }}
                                options={[
                                  {
                                    label: "Active",
                                    value: "Active",
                                    color: "text-[#419E6A] bg-[#E8FCF1]",
                                  },
                                  {
                                    label: "Inactive",
                                    value: "Inactive",
                                    color: "text-[#EFB008] bg-[#FFF5D5]",
                                  },
                                  {
                                    label: "On Hold",
                                    value: "Onhold",
                                    color: "text-[#EFB008] bg-[#FFF5D5]",
                                  },
                                  {
                                    label: "Hold by Client",
                                    value: "Holdbyclient",
                                    color: "text-[#EFB008] bg-[#FFF5D5]",
                                  },
                                  {
                                    label: "Filled",
                                    value: "Filled",
                                    color: "text-[#4D82F3] bg-[#D3E1FE]",
                                  },
                                  {
                                    label: "Cancelled",
                                    value: "Cancelled",
                                    color: "text-[#D83232] bg-[#FFEBEB]",
                                  },
                                  {
                                    label: "Closed",
                                    value: "Closed",
                                    color: "text-[#D83232] bg-[#FFEBEB]",
                                  },
                                ]}
                              />
                            </>
                          ) : (
                            job[item]
                          )}
                        </td>
                      );
                    })}
                    <td className="px-4 py-3 text-start">
                      <ThreeDot
                        dropdownSize="w-32"
                        buttonDropDown={
                          <>
                            <ul className="py-0" role="none">
                              {userInfo?.role == "accountManager" ? (
                                ""
                              ) : (
                                <li>
                                  <div
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      navigate(
                                        `/jobs/editjob?jobID=${job?.jobid}`
                                      );
                                    }}
                                    className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                                    role="menuitem"
                                  >
                                    <img
                                      src={"/assets/icons/edit.svg"}
                                      alt="Edit Job"
                                    />
                                    Edit Job
                                  </div>
                                </li>
                              )}
                              <li>
                                <div
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    navigate(
                                      `/jobs/jobdetails?jobId=${job.jobid}&tab=jobdetails`
                                    );
                                  }}
                                  className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                                  role="menuitem"
                                >
                                  <img
                                    src="/assets/icons/view.svg"
                                    alt="View Job"
                                  />
                                  View Job
                                </div>
                              </li>
                            </ul>
                          </>
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="bg-white">
            <PaginationFooter
              currentPage={paginationProps?.currentPage || currentPage}
              totalPages={paginationProps?.totalPages || totalPages}
              pageSize={paginationProps?.pageSize || pageSize}
              setPageSize={paginationProps?.setPageSize || handlePageSizeChange}
              setCurrentPage={
                paginationProps?.setCurrentPage || handlePageChange
              }
              totalResults={paginationProps?.totalResults || totalResults}
            />
          </div>
        </div>
      ) : (
        <EmptyState
          title={
            hasActiveFilters || hasActiveSearch
              ? "No Jobs Match Your Criteria"
              : "No Closed Jobs Found"
          }
          description={
            hasActiveFilters || hasActiveSearch
              ? "Try adjusting your filters or search terms to see more results."
              : "There are no closed jobs at the moment."
          }
        />
      )}
    </div>
  );
};

export default CloseJobs;
