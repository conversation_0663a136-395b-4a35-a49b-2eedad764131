exports.recruiterDetailTemplate = (userData, isProfileUpdate = false) => {
  const { user, userProfile: profile } = userData;

  // Create email content
  let emailContent = `
      <h2>${
        isProfileUpdate
          ? "Profile Update Notification"
          : "New User Registration"
      }</h2>
      <h3>User Details:</h3>
      <p><strong>Name:</strong> ${user.name.firstName} ${
    user.name.middleName
  } ${user.name.lastName}</p>
      <p><strong>User ID:</strong> ${user.userId}</p>
      <p><strong>Email:</strong> ${user.email}</p>
      <p><strong>Phone:</strong> ${user.phone.countryCode} ${
    user.phone.number
  }</p>
      <p><strong>User Type:</strong> ${user.userType}</p>
      <p><strong>LinkedIn URL:</strong> ${profile.linkedin}</p>
      <p><strong>Signup Source:</strong> ${user.source.name}</p>
      <p><strong>Do you have minimum 1 year of experience in Healthcare or IT
          recruitment?</strong> ${
            profile.isUserEligibileForITAndHealthcare ? "Yes" : "No"
          }</p>
      ${
        user.source.campaign
          ? `<p><strong>Campaign:</strong> ${user.source.campaign}</p>`
          : ""
      }
    `;

  // Add profile details if it's a profile update
  if (isProfileUpdate && profile) {
    emailContent += `
        <h3>Profile Details:</h3>
        <p><strong>Country:</strong> ${profile.country}</p>
        <p><strong>State:</strong> ${profile.state}</p>
        <p><strong>Domain:</strong> ${profile.domain}</p>
        <p><strong>Specialization:</strong> ${profile.specialization}</p>
        <p><strong>About:</strong> ${profile.about}</p>
      `;
  }

  return emailContent;
};
