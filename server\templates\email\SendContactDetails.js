exports.recruiterDetailTemplate = (contactInfo) => {
  // Create email content
  let emailContent = `
      <h2>New Contact form Submission</h2>
      <h3>User Details:</h3>
      <p><strong>Name:</strong> ${contactInfo?.name}</p>
      <p><strong>Email:</strong> ${contactInfo?.email}</p>
      <p><strong>Phone:</strong> ${contactInfo?.countryCode} - ${
    contactInfo?.phone
  }</p>
  <p><strong>Contact Person Type:</strong> ${contactInfo?.contactPersonType}</p>

  ${
    (contactInfo?.contactPersonType === "Client" ||
      contactInfo?.contactPersonType === "MSP/MSSP") &&
    contactInfo?.companyName
      ? `<p><strong>Company Name:</strong> ${contactInfo.companyName}</p>`
      : ""
  }
      <p><strong>Message:</strong> ${contactInfo?.message}</p>
      <p><strong>Source:</strong> ${contactInfo?.source}</p>
      <p><strong>Country:</strong> ${contactInfo?.country}</p>

      <p><strong>Submission Date:</strong> ${new Date().toLocaleString()}</p>
    `;

  return emailContent;
};
