import React, { useEffect, useState } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import RecruitersStatsCard from "../../components/common/cards/RecruitersStatsCard";
import CandidateCard from "../../components/common/cards/CandidateCard";
import TableHeader from "../../components/common/Table/TableHeader";
import ThreeDot from "../../components/common/Button/ThreeDot";
import PaginationFooter from "../../components/common/Table/TableFooter";
import EmptyState from "../../components/common/EmptyState/EmptyState";

import { getRecruiterDetails } from "../../services/operations/recruiterAPI";

const tabNavItems = [
  { name: "Active working Jobs" },
  { name: "All Jobs" },
  { name: "Submissions" },
];

const RecruiterDetails = () => {
  const { recruiterID: paramID } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const queryID = new URLSearchParams(location.search).get("recruiterID");
  const recruiterID = paramID || queryID;

  const [activeTab, setActiveTab] = useState(0);

  // Pagination state per tab
  const [currentPage, setCurrentPage] = useState([1, 1, 1]);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState([0, 0, 0]);
  const [totalPages, setTotalPages] = useState([1, 1, 1]);

  // Data per tab
  const [tabData, setTabData] = useState([[], [], []]);
  const [recruiter, setRecruiter] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch recruiter details for the current tab and page
  const fetchTabData = async (tabIdx, page) => {
    if (!recruiterID) {
      setError("No recruiter ID provided");
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      // Pass recruiterID, page, limit, and tab to backend
      const res = await getRecruiterDetails(
        recruiterID,
        page,
        pageSize,
        tabIdx
      );
      if (res?.success && Array.isArray(res.data) && res.data.length > 0) {
        const data = res.data[0];
        setRecruiter(data);

        // Use only the paginated array for the current tab
        let activeJobs = data.workonrequests || [];
        let allJobs = data.totalJob || [];
        let submissions = data.submissions || [];
        const newTabData = [activeJobs, allJobs, submissions];
        setTabData((prev) =>
          prev.map((d, idx) => (idx === tabIdx ? newTabData[tabIdx] : d))
        );

        // Update pagination for the current tab
        setTotal((prev) =>
          prev.map((t, idx) => (idx === tabIdx ? res.total || 0 : t))
        );
        setTotalPages((prev) =>
          prev.map((t, idx) => (idx === tabIdx ? res.totalPages || 1 : t))
        );
      } else {
        setRecruiter(null);
        setTabData((prev) => prev.map((d, idx) => (idx === tabIdx ? [] : d)));
        setTotal((prev) => prev.map((t, idx) => (idx === tabIdx ? 0 : t)));
        setTotalPages((prev) => prev.map((t, idx) => (idx === tabIdx ? 1 : t)));
      }
    } catch (err) {
      setError("Failed to fetch recruiter details");
      setRecruiter(null);
      setTabData((prev) => prev.map((d, idx) => (idx === tabIdx ? [] : d)));
      setTotal((prev) => prev.map((t, idx) => (idx === tabIdx ? 0 : t)));
      setTotalPages((prev) => prev.map((t, idx) => (idx === tabIdx ? 1 : t)));
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when recruiterID, activeTab, or page changes
  useEffect(() => {
    fetchTabData(activeTab, currentPage[activeTab]);
    // eslint-disable-next-line
  }, [recruiterID, activeTab, currentPage[activeTab], pageSize]);

  // Handle tab change
  const handleTabChange = (tabIdx) => {
    setActiveTab(tabIdx);
    // Reset page to 1 for the new tab
    setCurrentPage((prev) => prev.map((p, idx) => (idx === tabIdx ? 1 : p)));
  };

  // Handle page change for a tab
  const handlePageChange = (tabIdx, page) => {
    setCurrentPage((prev) => prev.map((p, idx) => (idx === tabIdx ? page : p)));
  };

  const getFullName = (r) => {
    const n = r?.name || {};
    return `${n.firstName || ""} ${n.middleName || ""} ${
      n.lastName || ""
    }`.trim();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "requestToWork":
      case "selected":
        return "bg-green-100 text-green-800";
      case "interviewing":
        return "bg-blue-100 text-blue-800";
      case "submitted to client":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const tableColumns = {
    0: ["Job ID", "Job Title", "Location", "Job Type", "Submissions", "Status"],
    1: ["Job ID", "Job Title", "Location", "Job Type", "Submissions", "Status"],
    2: ["Candidate ID", "Candidate Name", "Job ID", "Job Title", "Status"],
  };

  const getRowAction = (item, tab) => {
    if (tab === 0 || tab === 1) {
      const job = item.job || item.jobData;
      return () =>
        navigate(
          `/jobs/jobdetails?jobId=${job?.jobId || job?.jobID}&tab=jobdetails`
        );
    } else {
      const candidate = item.candidate || {};
      return () =>
        navigate(
          `/submissions/submissionDetails?candidateId=${
            candidate.candidateID
          }&submissionId=${item.submissionId || item._id}`
        );
    }
  };

  const renderRow = (item, tab) => {
    if (tab === 0 || tab === 1) {
      const job = item.job || item.jobData;
      return {
        "Job ID": job?.jobId || job?.jobID || "",
        "Job Title": job?.jobTitle || "",
        Location: `${job?.location?.city || ""}, ${job?.location?.state || ""}`,
        "Job Type": job?.jobType || "",
        Submissions: item.submissionsCount || 0,
        Status: item.status,
      };
    } else {
      const candidate = item.candidate || {};
      const personal = candidate.personalDetails || {};
      return {
        "Candidate ID": candidate.candidateID || candidate.candidateId,
        "Candidate Name": `${personal.firstName || ""} ${
          personal.middleName || ""
        } ${personal.lastName || ""}`.trim(),
        "Job ID": item.job?.jobId || item.job?.jobID,
        "Job Title": item.job?.jobTitle,
        Status: item.status,
      };
    }
  };

  const renderTable = (tab) => {
    const rawData = tabData[tab] || [];
    const page = currentPage[tab];
    if (!rawData.length) return <EmptyState />;
    return (
      <div className="p-0">
        <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200 text-sm text-left">
            <TableHeader columns={[...tableColumns[tab], ""]} />
            <tbody>
              {rawData.map((item, i) => {
                const row = renderRow(item, tab);
                const rowAction = getRowAction(item, tab);
                return (
                  <tr
                    key={i}
                    className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                    onClick={rowAction}
                  >
                    {tableColumns[tab].map((key) => (
                      <td className="px-4 py-3 text-start" key={key}>
                        {key === "Status" ? (
                          <span
                            className={`inline-block px-2 py-1 text-xs rounded ${getStatusColor(
                              row[key]
                            )}`}
                          >
                            {row[key]}
                          </span>
                        ) : (
                          row[key]
                        )}
                      </td>
                    ))}
                    <td
                      className="px-4 py-3 text-start"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <ThreeDot
                        dropdownSize="w-32"
                        buttonDropDown={
                          <ul className="py-0" role="none">
                            <li>
                              <div
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  rowAction();
                                }}
                                className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                role="menuitem"
                              >
                                <img
                                  src={"/assets/icons/view.svg"}
                                  alt="View"
                                />
                                View
                              </div>
                            </li>
                          </ul>
                        }
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <PaginationFooter
          currentPage={currentPage[tab]}
          totalPages={totalPages[tab]}
          setCurrentPage={(p) => handlePageChange(tab, p)}
          pageSize={pageSize}
          setPageSize={setPageSize}
          totalResults={total[tab]}
        />
      </div>
    );
  };

  const calculateStats = () => {
    const subs = recruiter?.submissions || [];
    const active =
      recruiter?.workonrequests?.filter((r) => r.status === "accepted") || [];
    return {
      jobsWorkingOn: active.length,
      totalSubmission: subs.length,
      jobCovered: recruiter?.totalJob?.length || 0,
      submissionToInterview: subs.filter((s) =>
        ["interviewing", "selected"].includes(s.status)
      ).length,
      interviewToOffer: subs.filter((s) => s.status === "selected").length,
    };
  };

  return (
    <div className="w-full mx-auto p-2 space-y-6">
      <CandidateCard
        name={getFullName(recruiter)}
        email={recruiter?.email || ""}
        candidateStatus={recruiter?.profile?.status || "Active"}
      />
      <RecruitersStatsCard stats={calculateStats()} />
      <TabNav
        nav={tabNavItems}
        active={activeTab}
        setActive={handleTabChange}
      />
      {loading ? (
        <div className="text-center py-10">Loading...</div>
      ) : (
        renderTable(activeTab)
      )}
      {error && <div className="text-red-500 text-center">{error}</div>}
    </div>
  );
};

export default RecruiterDetails;
