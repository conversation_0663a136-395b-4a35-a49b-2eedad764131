import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import TableHeader from "../../components/common/Table/TableHeader";
import { getAllRecruiters } from "../../services/operations/recruiterAPI";

import ThreeDot from "../../components/common/Button/ThreeDot";

import PaginationFooter from "../../components/common/Table/TableFooter";

const AllRecruiters = () => {
  const [recruiters, setRecruiters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const navigate = useNavigate();

  const tableColumns = [
    { label: "RECRUITER ID", key: "userId" },
    { label: "Name", key: "name" },
    { label: "SPECIALIZATION", key: "specialization" },
    { label: "JOBS WORKING ON", key: "jobsWorkingOn" },
    { label: "TOTAL SUBMISSION", key: "totalSubmission" },
    { label: "STATUS", key: "status" },
    { label: "", key: "action" },
  ];

  const tab = [{ name: "All Recruiters" }];
  const [activeTab, setActiveTab] = useState(0);

  const fetchRecruiters = async (page, limit) => {
    setLoading(true);
    try {
      const response = await getAllRecruiters(page, limit); // <-- FIXED HERE
      if (response?.success) {
        setRecruiters(response?.data || []);
        setTotal(response?.total || 0);
        setTotalPages(response?.totalPages || 1);
      } else {
        setRecruiters([]);
        setTotal(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Error fetching recruiters:", error);
      setRecruiters([]);
      setTotal(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchRecruiters(currentPage, pageSize);
    // eslint-disable-next-line
  }, [currentPage, pageSize]);

  return (
    <div>
      <TabNav nav={tab} active={activeTab} setActive={setActiveTab} />
      <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full bg-white">
          <TableHeader columns={tableColumns.map((col) => col.label)} />
          {recruiters?.length > 0 && (
            <tbody>
              {recruiters.map((recruiter, recruiterIndex) => (
                <tr
                  key={recruiterIndex}
                  className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                  onClick={() =>
                    navigate(
                      `/recruiter/recruiterDetails?recruiterID=${recruiter.userId}`
                    )
                  }
                >
                  {tableColumns.map((col) => {
                    if (col.key === "status") {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {recruiter?.profile?.status}
                        </td>
                      );
                    } else if (col.key === "name") {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {`${recruiter.name?.firstName || ""} ${
                            recruiter.name?.middleName
                              ? recruiter.name.middleName + " "
                              : ""
                          }${recruiter.name?.lastName || ""}`.trim()}
                        </td>
                      );
                    } else if (col.key === "totalSubmission") {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {recruiter.submissionsCount}
                        </td>
                      );
                    } else if (col.key === "jobsWorkingOn") {
                      const jobsWorkingOn = recruiter.workonrequests
                        ? recruiter?.workonrequests?.filter(
                            (r) => r.status === "accepted"
                          ).length
                        : recruiter.workonrequestsCount || 0;
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {jobsWorkingOn}
                        </td>
                      );
                    } else if (col.key === "specialization") {
                      return (
                        <td
                          className="px-4 py-3 flex flex-wrap gap-y-2 max-w-[12rem]"
                          key={col.key}
                        >
                          {recruiter.profile?.candidateRole?.length > 0
                            ? recruiter.profile.candidateRole.map(
                                (role, index) => (
                                  <span
                                    key={index}
                                    className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-1.5 py-0.5 rounded"
                                  >
                                    {role}
                                  </span>
                                )
                              )
                            : "N/A"}
                        </td>
                      );
                    } else if (col.key === "action") {
                      return (
                        <td
                          className="px-4 py-3"
                          key={col.key}
                          onClick={(e) => e.stopPropagation()} // prevent row click
                        >
                          <ThreeDot
                            dropdownSize="w-32"
                            onClick={(e) => e.stopPropagation()}
                            buttonDropDown={
                              <ul
                                className="py-0"
                                role="none"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <li>
                                  <div
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigate(
                                        `/recruiter/recruiterDetails?recruiterID=${recruiter.userId}`
                                      );
                                    }}
                                    className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem"
                                  >
                                    <img
                                      src="/assets/icons/view.svg"
                                      alt="View"
                                    />
                                    View
                                  </div>
                                </li>
                              </ul>
                            }
                          />
                        </td>
                      );
                    } else {
                      return (
                        <td className="px-4 py-3" key={col.key}>
                          {recruiter[col.key]}
                        </td>
                      );
                    }
                  })}
                </tr>
              ))}
            </tbody>
          )}
        </table>
      </div>

      {recruiters?.length > 0 && (
        <PaginationFooter
          currentPage={currentPage}
          totalPages={totalPages}
          setCurrentPage={setCurrentPage}
          pageSize={pageSize}
          setPageSize={setPageSize}
          totalResults={total}
        />
      )}

      {recruiters?.length <= 0 && !loading && (
        <div className="flex flex-col w-full items-center justify-center py-10">
          <img
            src="/assets/images/empty-state.svg"
            alt="No Data"
            className="mb-4"
          />
          <p className="text-gray-500">No recruiters found.</p>
        </div>
      )}
    </div>
  );
};

export default AllRecruiters;
