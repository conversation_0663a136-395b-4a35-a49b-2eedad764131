import { createAvatar } from "@dicebear/core";
import { useEffect, useState } from "react";
import { initials } from "@dicebear/collection";

const Message = ({ name, message, userType }) => {
  const [avatarUri, setAvatarUri] = useState();

  useEffect(() => {
    const generateAvatar = async () => {
      const avatar = await createAvatar(initials, {
        seed: name,
        backgroundColor: ["3E9900"],
      }).toDataUri();
      setAvatarUri(avatar);
    };

    generateAvatar();
  }, [name]);

  return (
    <div
      className={`flex max-w-[60%] ${
        message?.userType === userType && "place-self-end flex-row-reverse"
      }   w-full gap-4`}
    >
      <img src={avatarUri} alt="" className=" h-10 w-10 rounded-full" />
      <div className=" w-full  bg-white flex ">
        <div className=" w-2 bg-[#296600] h-full rounded-l-xl"></div>
        <div className="border border-[#CBD4E1] rounded-r-xl p-4 w-full">
          {message?.content}
          <div className=" text-[#94A3B8] text-[0.75rem] leading-[150%] place-self-end pt-2 ">
            {new Date(message?.timestamp).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Message;
