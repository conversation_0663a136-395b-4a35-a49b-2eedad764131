import { apiConnector } from "../apiConnector";
import { userEndpoints } from "../apis";

const { UPDATE_PROFILE_API, RESEND_VERIFICATION_API } = userEndpoints;

export const updateProfile = async (formData, userId) => {
  const response = await apiConnector(
    "PATCH",
    `${UPDATE_PROFILE_API}/${userId}`,
    formData,
    {
      "Content-Type": "multipart/form-data",
    }
  );

  if (!response?.data?.success) {
    throw new Error(response?.data?.message || "Failed to update profile");
  }

  return response.data;
};

export const resendVerification = async ({ userId }) => {
  const response = await apiConnector("POST", RESEND_VERIFICATION_API, {
    userId,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message || "Failed to resend verification");
  }
  return response.data;
};
