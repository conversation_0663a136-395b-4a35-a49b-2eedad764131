const Pills = ({ title, icon, type = "regular" }) => {
  return (
    <div
      className={` ${
        type === "regular"
          ? "bg-[#D3E1FE]"
          : type === "highPriority"
          ? "bg-[#FFEBEB]"
          : type === "commission"
          ? "bg-[#FFF5D5]"
          : ""
      } w-fit py-0.5 px-2 rounded-2xl flex items-center justify-center`}
    >
      {icon && <img src={icon} alt="" className="mr-1" />}
      <div
        className={`text-[0.75rem] ${
          type === "highPriority"
            ? "text-[#D92D2A]"
            : type === "commission"
            ? "text-[#EFB008]"
            : "text-[#4D82F3]"
        }  font-medium leading-[150%] flex items-center gap-1`}
      >
        {type === "commission" && "$"}
        {type === "highPriority" && (
          <span className="w-1.5 h-1.5 bg-red-600 block rounded-full"></span>
        )}
        <p>{title}</p>
      </div>
    </div>
  );
};

export default Pills;
