import Image from "next/image";

import "./perks.css";

const PerkCardMobile = ({ icon, title, description }) => {
  return (
    <div className=" flip-card w-40 md:w-80 h-40   flex items-center justify-center ">
      <div className="flip-card-inner w-full h-full flex  items-center justify-center">
        <div
          className="flip-card-front flex flex-col bg-white rounded-2xl   justify-center gap-[1.25rem]  px-7"
          style={{
            boxShadow: "7px 7px 100px 0px rgba(0,0,0,0.26)",
          }}
        >
          <Image src={icon} alt={title} width={40} height={40} />
          <div className="flex flex-col gap-1">
            <h4 className={"text-[14px]"}>{title}</h4>
          </div>
        </div>
        <div className="flip-card-back rotate-180 flex flex-col bg-[#A9FF70]   justify-center gap-[1.25rem] rounded-2xl  px-2">
          <p className={"  text-[12px]"}>{description}</p>
        </div>
      </div>
    </div>
  );
};

export default PerkCardMobile;
