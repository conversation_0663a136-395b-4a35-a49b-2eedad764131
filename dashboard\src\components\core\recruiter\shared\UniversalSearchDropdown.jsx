import React, { useState, useEffect, useRef, useCallback } from "react";
import TableLikeSearchSelector from "../../../common/filters/TableLikeSearchSelector";


const UniversalSearchDropdown = ({ 
  isOpen, 
  onClose, 
  onSearch, 
  section = 'jobs', 
  activeTab = 0
}) => {
  const dropdownRef = useRef(null);
  const debounceTimerRef = useRef(null);
  const isInitialMount = useRef(true);
  
  // Generate storage key based on section and activeTab
  const getStorageKey = useCallback(() => {
    return `${section}Search_${activeTab}`;
  }, [section, activeTab]);

  const [searchInputs, setSearchInputs] = useState(() => {
    const params = new URLSearchParams(window.location.search);
    const savedSearch = localStorage.getItem(getStorageKey());
    const savedData = savedSearch ? JSON.parse(savedSearch) : null;

    return {
      searchTerm: params.get("searchTerm") || savedData?.searchTerm || "",
      searchField: params.get("searchField") || savedData?.searchField || "all",
    };
  });

  // Save to URL and localStorage
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (searchInputs.searchTerm) {
      params.set("searchTerm", searchInputs.searchTerm);
      params.set("searchField", searchInputs.searchField);
    } else {
      params.delete("searchTerm");
      params.delete("searchField");
    }
    window.history.replaceState({}, "", `${window.location.pathname}?${params}`);
    localStorage.setItem(getStorageKey(), JSON.stringify(searchInputs));
  }, [searchInputs, activeTab, section, getStorageKey]);

  // Track the last search term to prevent unnecessary API calls
  const lastSearchTermRef = useRef("");

  // Debounced search effect - only trigger when user actively types
  useEffect(() => {
    // Skip on initial mount to prevent immediate API call
    if (isInitialMount.current) {
      isInitialMount.current = false;
      // Set initial last search term to current term to prevent initial trigger
      lastSearchTermRef.current = searchInputs.searchTerm.trim();
      return;
    }

    // Only trigger search when dropdown is open (user is actively searching)
    if (!isOpen) {
      return;
    }

    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Only trigger search if there's a search term AND it's different from last search
    const currentTerm = searchInputs.searchTerm.trim();

    if (currentTerm && currentTerm !== lastSearchTermRef.current) {
      debounceTimerRef.current = setTimeout(() => {
        onSearch(searchInputs);
        lastSearchTermRef.current = currentTerm;
      }, 600);
    } else if (searchInputs.searchTerm === "" && lastSearchTermRef.current !== "") {
      // Clear search when input is empty and we had a previous search
      onSearch({
        searchTerm: "",
        searchField: searchInputs.searchField,
      });
      lastSearchTermRef.current = "";
    }

    // Cleanup function
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchInputs.searchTerm, searchInputs.searchField, isOpen]);

  // Configuration object for different sections and tabs
  const searchFieldConfigurations = {
    jobs: {
      0: [ // Working On (Recruiter Jobs Tab 0)
        { value: "all", label: "All Fields" },
        { value: "jobTitle", label: "Job Title" },
        { value: "jobType", label: "Job Type" },
        { value: "jobId", label: "Job ID" },
        { value: "industry", label: "Specialization" },
        { value: "priority", label: "Priority" },
      ],
      1: [ // Mapped (Recruiter Jobs Tab 1)
        { value: "all", label: "All Fields" },
        { value: "jobTitle", label: "Job Title" },
        { value: "jobId", label: "Job ID" },
        { value: "jobType", label: "Job Type" },
        { value: "location", label: "Location" },
        { value: "priority", label: "Priority" },
      ],
      2: [ // Work on request (Recruiter Jobs Tab 2)
        { value: "all", label: "All Fields" },
        { value: "jobId", label: "Job ID" },
        { value: "jobTitle", label: "Job Title" },
        { value: "location", label: "Location" },
        { value: "jobType", label: "Job Type" },
        { value: "priority", label: "Priority" },
      ],
      3: [ // All Jobs (Recruiter Jobs Tab 3)
        { value: "all", label: "All Fields" },
        { value: "jobId", label: "Job ID" },
        { value: "jobTitle", label: "Job Title" },
        { value: "location", label: "Location" },
        { value: "jobType", label: "Job Type" },
        { value: "priority", label: "Priority" },
      ],
      4: [ // Saved (Recruiter Jobs Tab 4)
        { value: "all", label: "All Fields" },
        { value: "jobTitle", label: "Job Title" },
        { value: "jobId", label: "Job ID" },
        { value: "jobType", label: "Job Type" },
        { value: "location", label: "Location" },
        { value: "priority", label: "Priority" },
      ],
    },
    candidates: {
      0: [ // Active Submissions
        { value: "all", label: "All Fields" },
        { value: "candidateName", label: "Candidate Name" },
        { value: "jobTitle", label: "Job Title" },
        { value: "JobId", label: "JobId"},
        { value: "location", label: "Location" },
        { value: "status", label: "Status" },
        { value: "email", label: "Email" },
      ],
      1: [ // Hired Candidates
        { value: "all", label: "All Fields" },
        { value: "candidateName", label: "Candidate Name" },
        { value: "jobTitle", label: "Job Title" },
        { value: "JobId", label: "Job Id"},
        { value: "JobType", label: "Job Type"},
        { value: "location", label: "Location" },
        { value: "email", label: "Email" },
        { value: "Experience", label: "Experience" },
      ],
      2: [ // Rejected Candidates
        { value: "all", label: "All Fields" },
        { value: "candidateName", label: "Candidate Name" },
        { value: "jobTitle", label: "Job Title" },
        { value: "JobId", label: "Job Id"},
        { value: "email", label: "Email" },
      ],
      3: [ // All Submissions
        { value: "all", label: "All Fields" },
        { value: "candidateName", label: "Candidate Name" },
        { value: "jobTitle", label: "Job Title" },
        { value: "JobId", label: "JobId"},
        { value: "location", label: "Location" },
        { value: "status", label: "Status" },
        { value: "JobType", label: "Job Type"},
        { value: "email", label: "Email" },
        { value: "Experience", label: "Experience" },
        { value: "Phonenumber", label: "Mobile Number" },
      ],
    }
  };

  // Get the appropriate search fields based on section and activeTab
  const getCurrentSearchFields = () => {
    const sectionConfig = searchFieldConfigurations[section];
    if (!sectionConfig) {
      // Fallback to jobs section if section not found
      return searchFieldConfigurations.jobs[0];
    }

    // Return the fields for the current tab, or fallback to tab 0
    return sectionConfig[activeTab] || sectionConfig[0];
  };

  const currentSearchFields = getCurrentSearchFields();

  const handleInputChange = (field, value) => {
    setSearchInputs((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle Enter key press for immediate search
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      // Clear any pending debounced search and trigger immediate search
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      if (searchInputs.searchTerm.trim()) {
        onSearch(searchInputs);
      }
    }
  };

  // Restore saved search on tab/section change - but don't close dropdown
  useEffect(() => {
    const storageKey = getStorageKey();
    const saved = localStorage.getItem(storageKey);
    if (saved) {
      const parsed = JSON.parse(saved);
      setSearchInputs(parsed);
    } else {
      setSearchInputs({ searchTerm: "", searchField: "all" });
    }
    // Note: We don't close the dropdown here to keep it open during tab switches
  }, [activeTab, section, getStorageKey]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        // Check if the click was on the search icon
        const isSearchIcon = event.target.closest('[data-search-icon="true"]');
        if (!isSearchIcon) {
          onClose();
        }
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      ref={dropdownRef}
      className="absolute top-full right-0 mt-2 w-88 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
    >
      {/* Search Form */}
      <div className="p-4 pb-0 flex ">
        {/* Search Field Selector */}
        <TableLikeSearchSelector
          searchInputs={searchInputs}
          handleInputChange={handleInputChange}
          currentSearchFields={currentSearchFields}
        />

        {/* Search Input */}
        <div className="mb-3">
          <div className="relative">
            <input
              type="text"
              value={searchInputs.searchTerm}
              onChange={(e) => handleInputChange("searchTerm", e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={`Search ${section}...`}
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer">
              <svg
                className={`w-4 h-4 ${searchInputs.searchTerm.trim() ? 'text-blue-600' : 'text-gray-400'}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default UniversalSearchDropdown;
