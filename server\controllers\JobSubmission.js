const { candidateSubmission } = require("../models/CandidateSubmission.models");
const CoinTransaction = require("../models/CoinTransaction");
const Job = require("../models/Job.models");
const mongoose = require("mongoose");

//* @Desc Get Job Submission Details
//* @Send submissionId in body
//* @Access private, rolebased
exports.getJobSubmissionDetails = async (req, res) => {
  try {
    const { submissionId } = req.body;

    if (!mongoose.Types.ObjectId.isValid(submissionId)) {
      return res
        .status(404)
        .json({ success: false, message: "invalid submission Id" });
    }

    const submissionDetails = await candidateSubmission.findById(submissionId);

    if (!submissionDetails) {
      return res
        .status(404)
        .json({ success: false, message: "Submission not found" });
    }

    res.status(200).json({ success: true, data: submissionDetails });
  } catch (err) {
    console.log("Error during fetching submission details", err);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

//* @get update submitted condidate data for job
//* @Route POST /api/v1/job/condidate/update-condidate
//* @Access private, rolebased
const updateSubmitedJobsubmission = async (req, res) => {
  try {
    req.user = { _id: "dummyUserId", name: "Test User" };
    const { submissionId, ...updateData } = req.body;
    //const updateData = req.body;
    const user = req.user;

    if (!user) {
      return res.status(404).json({ status: false, message: "user not found" });
    }

    if (!mongoose.Types.ObjectId.isValid(submissionId))
      return res.status(500).json({ message: "invalid job" });

    const updatedJob = await jobSubmission.findByIdAndUpdate(
      submissionId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    console.log(updatedJob);

    if (!updatedJob) return res.status(404).json({ message: "Job not found" });

    res.status(200).json({
      message: "Job updated successfully",
      job: updatedJob,
    });
  } catch (err) {
    console.log("err", err);
    res.status(500).json({ status: false, message: err });
  }
};

//* @get get all job submissions
//* @Route POST /api/v1/headmanager/manager/getallsubmission
//* @Access private, head manager
const getAllSubmission = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        status: false,
        message: "User not found",
      });
    }

    // Extract filter, sorting, and search parameters from query
    const {
      status,
      recruiterName,
      jobTitle,
      sortBy,
      sortOrder,
      postedDate,
      submissionDate,
      search,
      searchField,
    } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Helper function to parse filter arrays from query strings
    const parseFilterArray = (filterValue) => {
      if (!filterValue) return null;
      if (Array.isArray(filterValue)) {
        return filterValue.filter((item) => item && item.trim().length > 0);
      }
      if (typeof filterValue === "string") {
        return filterValue
          .split(",")
          .map((item) => item.trim())
          .filter((item) => item.length > 0);
      }
      return null;
    };

    // Safe regex escape function
    const escapeRegex = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };

    // Build the base match condition
    const baseMatchCondition = {};

    // Function to build status match condition (applied AFTER lookups)
    const buildStatusMatchCondition = () => {
      if (!status) return {};

      const statusArray = parseFilterArray(status);
      if (!statusArray || statusArray.length === 0) return {};

      const statusConditions = [];

      statusArray.forEach((stat) => {
        const statusValue = stat.toLowerCase().trim();

        switch (statusValue) {
          case "submitted":
            statusConditions.push({ status: { $regex: /^submitted$/i } });
            break;
          case "reviewing":
            statusConditions.push({ status: { $regex: /^reviewing$/i } });
            break;
          case "submitted to client":
            statusConditions.push({
              status: { $regex: /^submitted to client$/i },
            });
            break;
          case "selected":
            statusConditions.push({ status: { $regex: /^selected$/i } });
            break;
          case "awaiting offer":
            statusConditions.push({ status: { $regex: /^awaiting offer$/i } });
            break;
          case "accepted":
            statusConditions.push({ status: { $regex: /^accepted$/i } });
            break;
          case "rejected":
            statusConditions.push({ status: { $regex: /^rejected$/i } });
            break;
          case "interviewing":
            statusConditions.push({ status: { $regex: /^interviewing$/i } });
            break;
          case "offer accepted":
            statusConditions.push({ status: { $regex: /^offer accepted$/i } });
            break;
          case "offer rejected":
            statusConditions.push({ status: { $regex: /^offer rejected$/i } });
            break;
          case "offer released":
            statusConditions.push({ status: { $regex: /^offer released$/i } });
            break;
          case "hired-under guarantee period":
            statusConditions.push({
              status: { $regex: /^hired-under guarantee period$/i },
            });
            break;
          case "guarantee period not completed":
            statusConditions.push({
              status: { $regex: /^guarantee period not completed$/i },
            });
            break;
          case "guarantee period completed":
            statusConditions.push({
              status: { $regex: /^guarantee period completed$/i },
            });
            break;
          default:
            // Unknown status – match directly
            statusConditions.push({
              status: { $regex: new RegExp(`^${escapeRegex(stat)}$`, "i") },
            });
            console.warn(
              `🔍 Backend Submissions: Unknown status "${stat}", using direct match`
            );
        }
      });

      // Return the combined status condition
      if (statusConditions.length === 1) {
        return statusConditions[0];
      } else if (statusConditions.length > 1) {
        return { $or: statusConditions };
      }
      return {};
    };

    // Function to build recruiter name filter condition
    const buildRecruiterNameFilterCondition = () => {
      if (!recruiterName) return {};

      const recruiterArray = parseFilterArray(recruiterName);
      if (!recruiterArray || recruiterArray.length === 0) return {};

      const recruiterConditions = recruiterArray.map((name) => {
        const nameRegex = new RegExp(escapeRegex(name), "i");
        return {
          $or: [
            { "recruiter.name.firstName": nameRegex },
            { "recruiter.name.lastName": nameRegex },
            { "recruiter.name.middleName": nameRegex },
          ],
        };
      });

      return recruiterConditions.length === 1
        ? recruiterConditions[0]
        : { $or: recruiterConditions };
    };

    // Function to build job title filter condition
    const buildJobTitleFilterCondition = () => {
      if (!jobTitle) return {};

      const jobTitleArray = parseFilterArray(jobTitle);
      if (!jobTitleArray || jobTitleArray.length === 0) return {};

      const jobTitleConditions = jobTitleArray.map((title) => ({
        "job.jobTitle": { $regex: new RegExp(escapeRegex(title), "i") },
      }));

      return jobTitleConditions.length === 1
        ? jobTitleConditions[0]
        : { $or: jobTitleConditions };
    };

    // Updated buildSearchMatchCondition function based on actual API data structure
    const buildSearchMatchCondition = () => {
      if (!search || search.trim().length === 0) return {};

      const searchTerm = escapeRegex(search.trim());
      const searchRegex = new RegExp(searchTerm, "i");

      if (searchField && searchField !== "all") {
        // Search in specific field based on actual data structure
        switch (searchField.toLowerCase()) {
          case "candidateid":
          case "candidate_id":
            return { "candidate.candidateID": searchRegex };

          case "candidatename":
          case "candidate_name":
          case "name":
            return {
              $or: [
                { "candidate.personalDetails.firstName": searchRegex },
                { "candidate.personalDetails.lastName": searchRegex },
              ],
            };

          case "submissionid":
          case "submission_id":
            return { submissionId: searchRegex };

          case "jobid":
          case "job_id":
            return { "job.jobId": searchRegex };

          case "jobtitle":
          case "job_title":
            return { "job.jobTitle": searchRegex };

          case "email":
            return { "candidate.personalDetails.emailAddress": searchRegex };

          case "phone":
          case "phoneno":
          case "phone_no":
            return {
              $or: [
                { "candidate.personalDetails.phoneNumber": searchRegex },
                { "candidate.personalDetails.phoneCountryCode": searchRegex },
              ],
            };

          case "status":
            return { status: searchRegex };

          case "location":
            return {
              $or: [
                { "candidate.personalDetails.city": searchRegex },
                { "candidate.personalDetails.state": searchRegex },
                { "candidate.personalDetails.country": searchRegex },
                { "candidate.personalDetails.currentAddress": searchRegex },
              ],
            };

          case "recruitername":
          case "recruiter_name":
          case "recruiter":
            return {
              $or: [
                { "recruiter.name.firstName": searchRegex },
                { "recruiter.name.lastName": searchRegex },
                { "recruiter.name.middleName": searchRegex },
              ],
            };

          case "company":
          case "clientname":
          case "client_name":
            return { "job.clientname": searchRegex };

          case "industry":
            return { "job.industry": searchRegex };

          case "skills":
          case "primaryskills":
          case "primary_skills":
            return { "job.primarySkills": { $in: [searchRegex] } };

          case "experience":
            return {
              "candidate.skillsAndExperience.totalYearsOfExperience":
                searchRegex,
            };

          default:
            // Default to searching candidate name
            return {
              $or: [
                { "candidate.personalDetails.firstName": searchRegex },
                { "candidate.personalDetails.lastName": searchRegex },
              ],
            };
        }
      } else {
        return {
          $or: [
            // Candidate fields
            { "candidate.candidateID": searchRegex },
            { "candidate.personalDetails.firstName": searchRegex },
            { "candidate.personalDetails.lastName": searchRegex },
            { "candidate.personalDetails.emailAddress": searchRegex },
            { "candidate.personalDetails.phoneNumber": searchRegex },
            { "candidate.personalDetails.city": searchRegex },
            { "candidate.personalDetails.state": searchRegex },
            { "candidate.personalDetails.country": searchRegex },
            { "candidate.personalDetails.currentAddress": searchRegex },
            {
              "candidate.skillsAndExperience.totalYearsOfExperience":
                searchRegex,
            },

            // Recruiter fields
            { "recruiter.name.firstName": searchRegex },
            { "recruiter.name.lastName": searchRegex },
            { "recruiter.name.middleName": searchRegex },
            { "recruiter.email": searchRegex },
            { "recruiter.userId": searchRegex },

            // Job fields
            { "job.jobId": searchRegex },
            { "job.jobTitle": searchRegex },
            { "job.clientname": searchRegex },
            { "job.industry": searchRegex },
            { "job.primarySkills": { $in: [searchRegex] } },
            { "job.location.city": searchRegex },
            { "job.location.state": searchRegex },
            { "job.location.country": searchRegex },

            // Submission fields
            { submissionId: searchRegex },
            { status: searchRegex },
          ],
        };
      }
    };

    // Build sorting object - Updated to handle both postedDate and submissionDate
    const buildSortObject = () => {
      const defaultSort = { submittedAt: -1 };

      // ✅ UPDATED: Check submissionDate first, then postedDate for backward compatibility
      const dateParam = submissionDate || postedDate;
      if (dateParam) {
        const dateSort = dateParam.toLowerCase();
        if (
          dateSort === "recent" ||
          dateSort === "newest" ||
          dateSort === "latest"
        ) {
          return { submittedAt: -1 };
        } else if (dateSort === "oldest" || dateSort === "earliest") {
          return { submittedAt: 1 };
        }
      }

      if (!sortBy) return defaultSort;

      const sortField = sortBy.toLowerCase();
      const order = sortOrder && sortOrder.toLowerCase() === "asc" ? 1 : -1;

      switch (sortField) {
        case "posteddate":
        case "posted_date":
        case "createdat":
        case "created_at":
        case "submitteddate":
        case "submitted_date":
        case "submittedat":
        case "submitted_at":
        case "submissiondate": // ✅ ADDED: Handle submissiondate
        case "submission_date": // ✅ ADDED: Handle submission_date
          return { submittedAt: order };
        case "recent":
          return { submittedAt: -1 };
        case "oldest":
          return { submittedAt: 1 };
        case "candidateid":
        case "candidate_id":
          return { "candidate.candidateID": order };
        case "name":
        case "candidatename":
        case "candidate_name":
          return { "candidate.personalDetails.firstName": order };
        case "recruitername":
        case "recruiter_name":
          return { "recruiter.name.firstName": order };
        case "jobid":
        case "job_id":
          return { "job.jobId": order };
        case "jobtitle":
        case "job_title":
          return { "job.jobTitle": order };
        case "status":
          return { status: order };
        default:
          console.warn(`Unknown sort field: ${sortField}, using default sort`);
          return defaultSort;
      }
    };

    const sortObject = buildSortObject();

    // Build aggregation pipeline
    const pipeline = [
      // Stage 1: Base filtering
      {
        $match: baseMatchCondition,
      },

      // Stage 2: Lookup job data
      {
        $lookup: {
          from: "jobs",
          localField: "job.jobID",
          foreignField: "jobId",
          as: "job",
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "submittedBy.userID",
          foreignField: "userId",
          as: "recruiter",
        },
      },
      {
        $lookup: {
          from: "candidates",
          localField: "candidate.candidateID",
          foreignField: "candidateID",
          as: "candidate",
        },
      },
      {
        $unwind: {
          path: "$candidate",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$recruiter",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$job",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // Apply filters and search (NOW lookup data is available)
    const statusMatchCondition = buildStatusMatchCondition();
    const recruiterNameFilterCondition = buildRecruiterNameFilterCondition();
    const jobTitleFilterCondition = buildJobTitleFilterCondition();
    const searchMatchCondition = buildSearchMatchCondition();

    // Combine all filter conditions
    const combinedMatchConditions = [];
    if (Object.keys(statusMatchCondition).length > 0) {
      combinedMatchConditions.push(statusMatchCondition);
    }
    if (Object.keys(recruiterNameFilterCondition).length > 0) {
      combinedMatchConditions.push(recruiterNameFilterCondition);
    }
    if (Object.keys(jobTitleFilterCondition).length > 0) {
      combinedMatchConditions.push(jobTitleFilterCondition);
    }
    if (Object.keys(searchMatchCondition).length > 0) {
      combinedMatchConditions.push(searchMatchCondition);
    }

    // Add combined match stage if there are conditions
    if (combinedMatchConditions.length > 0) {
      const combinedMatch =
        combinedMatchConditions.length === 1
          ? combinedMatchConditions[0]
          : { $and: combinedMatchConditions };

      pipeline.push({
        $match: combinedMatch,
      });
    }

    // Add project and facet stages
    pipeline.push(
      {
        $project: {
          submittedBy: 0,
        },
      },
      {
        $sort: sortObject,
      },
      {
        $facet: {
          data: [{ $skip: skip }, { $limit: limit }],
          meta: [{ $count: "total" }],
        },
      }
    );

    const submission = await candidateSubmission.aggregate(pipeline);

    const totalSubmissions = submission[0]?.meta[0]?.total || 0;
    const submissionData = submission[0]?.data || [];

    const validatedPage = Math.max(1, page);
    const totalPages = Math.ceil(totalSubmissions / limit);

    return res.status(200).json({
      message: "All job submissions retrieved successfully",
      success: true,
      results: submissionData,
      totalPages,
      totalSubmissions,
      page: validatedPage,
      limit,
      appliedFilters: {
        status: parseFilterArray(status),
        recruiterName: parseFilterArray(recruiterName),
        jobTitle: parseFilterArray(jobTitle),
        search: search || null,
        searchField: searchField || null,
      },
      appliedSorting: {
        sortBy: sortBy || "submittedAt",
        sortOrder: sortOrder || "desc",
        postedDate: postedDate || null,
        submissionDate: submissionDate || null,
        sortObject: sortObject,
      },
    });
  } catch (error) {
    console.error("Error in getAllSubmission:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching submissions",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

//* @get get all job submissions by account manager
//* @Route POST /api/v1/headmanager/manager/getallsubmission
//* @Access private, account manager
const getAllSubmissionByAccountManager = async (req, res) => {
  // NEW IMPLEMENTATION – add full search / filter / sort support for Account-Manager submissions
  try {
    const user = req.user;
    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "User not found" });
    }

    const userId = user.userId;

    // -------------------------
    // Parse query parameters
    // -------------------------
    const {
      status,
      recruiterName,
      jobTitle,
      sortBy,
      sortOrder,
      postedDate, // legacy support
      submissionDate, // preferred date param
      search,
      searchField,
    } = req.query;

    const page = parseInt(req.query.page) || null;
    const limit = parseInt(req.query.limit) || null;

    // -------------------------
    // Helper utilities
    // -------------------------
    const parseFilterArray = (filterValue) => {
      if (!filterValue) return null;
      if (Array.isArray(filterValue)) {
        return filterValue.filter((item) => item && item.trim().length > 0);
      }
      if (typeof filterValue === "string") {
        return filterValue
          .split(",")
          .map((item) => item.trim())
          .filter((item) => item.length > 0);
      }
      return null;
    };

    const escapeRegex = (string) =>
      string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // --- Status filter ---
    const buildStatusMatchCondition = () => {
      if (!status) return {};
      const statusArray = parseFilterArray(status);
      if (!statusArray || statusArray.length === 0) return {};

      const statusConditions = statusArray.map((stat) => {
        const value = stat.toLowerCase().trim();
        return {
          status: { $regex: new RegExp(`^${escapeRegex(value)}$`, "i") },
        };
      });
      return statusConditions.length === 1
        ? statusConditions[0]
        : { $or: statusConditions };
    };

    // --- Recruiter name filter ---
    const buildRecruiterNameFilterCondition = () => {
      if (!recruiterName) return {};
      const recruiterArray = parseFilterArray(recruiterName);
      if (!recruiterArray || recruiterArray.length === 0) return {};

      const recruiterConditions = recruiterArray.map((name) => {
        const regex = new RegExp(escapeRegex(name), "i");
        return {
          $or: [
            { "recruiter.name.firstName": regex },
            { "recruiter.name.lastName": regex },
            { "recruiter.name.middleName": regex },
          ],
        };
      });

      return recruiterConditions.length === 1
        ? recruiterConditions[0]
        : { $or: recruiterConditions };
    };

    // --- Job title filter ---
    const buildJobTitleFilterCondition = () => {
      if (!jobTitle) return {};
      const titles = parseFilterArray(jobTitle);
      if (!titles || titles.length === 0) return {};
      const titleConditions = titles.map((title) => ({
        "job.jobTitle": { $regex: new RegExp(escapeRegex(title), "i") },
      }));
      return titleConditions.length === 1
        ? titleConditions[0]
        : { $or: titleConditions };
    };

    // --- Search condition ---
    const buildSearchMatchCondition = () => {
      if (!search || !search.trim()) return {};
      const regex = new RegExp(escapeRegex(search.trim()), "i");
      if (searchField && searchField !== "all") {
        switch (searchField.toLowerCase()) {
          case "candidateid":
          case "candidate_id":
            return { "candidate.candidateID": regex };
          case "name":
          case "candidate":
          case "candidatename":
            return {
              $or: [
                { "candidate.personalDetails.firstName": regex },
                { "candidate.personalDetails.lastName": regex },
              ],
            };
          case "recruiter":
          case "recruitername":
            return {
              $or: [
                { "recruiter.name.firstName": regex },
                { "recruiter.name.lastName": regex },
                { "recruiter.name.middleName": regex },
              ],
            };
          case "jobid":
          case "job_id":
            return { "job.jobId": regex };
          case "jobtitle":
          case "job_title":
            return { "job.jobTitle": regex };
          case "status":
            return { status: regex };
          default:
            // default broad search
            return {
              $or: [
                { "candidate.candidateID": regex },
                { "candidate.personalDetails.firstName": regex },
                { "candidate.personalDetails.lastName": regex },
                { "recruiter.name.firstName": regex },
                { "recruiter.name.lastName": regex },
                { "job.jobId": regex },
                { "job.jobTitle": regex },
              ],
            };
        }
      }
      // Global search
      return {
        $or: [
          { "candidate.candidateID": regex },
          { "candidate.personalDetails.firstName": regex },
          { "candidate.personalDetails.lastName": regex },
          { "candidate.personalDetails.emailAddress": regex },
          { "candidate.personalDetails.phoneNumber": regex },
          { "recruiter.name.firstName": regex },
          { "recruiter.name.lastName": regex },
          { "recruiter.email": regex },
          { "job.jobId": regex },
          { "job.jobTitle": regex },
          { status: regex },
        ],
      };
    };

    // --- Sorting ---
    const buildSortObject = () => {
      const defaultSort = { submittedAt: -1 };

      const dateParam = submissionDate || postedDate;
      if (dateParam) {
        const dateSort = dateParam.toLowerCase();
        if (["recent", "newest", "latest"].includes(dateSort))
          return { submittedAt: -1 };
        if (["oldest", "earliest"].includes(dateSort))
          return { submittedAt: 1 };
      }

      if (!sortBy) return defaultSort;
      const sortField = sortBy.toLowerCase();
      const order = sortOrder && sortOrder.toLowerCase() === "asc" ? 1 : -1;
      switch (sortField) {
        case "submissiondate":
        case "submittedat":
          return { submittedAt: order };
        case "candidateid":
          return { "candidate.candidateID": order };
        case "name":
          return { "candidate.personalDetails.firstName": order };
        case "recruitername":
          return { "recruiter.name.firstName": order };
        case "jobid":
          return { "job.jobId": order };
        case "jobtitle":
          return { "job.jobTitle": order };
        case "status":
          return { status: order };
        default:
          return defaultSort;
      }
    };

    const statusMatch = buildStatusMatchCondition();
    const recruiterMatch = buildRecruiterNameFilterCondition();
    const jobTitleMatch = buildJobTitleFilterCondition();
    const searchMatch = buildSearchMatchCondition();
    const sortObject = buildSortObject();

    // -------------------------
    // Build aggregation pipeline
    // -------------------------

    const pipeline = [
      // Lookup job
      {
        $lookup: {
          from: "jobs",
          localField: "job._id",
          foreignField: "_id",
          as: "job",
        },
      },
      { $unwind: "$job" },

      // Restrict to submissions where current AM is owner
      { $match: { "job.accountManager.userID": userId } },

      // Lookup recruiter
      {
        $lookup: {
          from: "users",
          localField: "submittedBy.userID",
          foreignField: "userId",
          as: "recruiter",
        },
      },
      {
        $unwind: {
          path: "$recruiter",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup candidate
      {
        $lookup: {
          from: "candidates",
          localField: "candidate.candidateID",
          foreignField: "candidateID",
          as: "candidate",
        },
      },
      {
        $unwind: {
          path: "$candidate",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // Combine filter conditions
    const combinedMatch = { $and: [] };
    if (Object.keys(statusMatch).length) combinedMatch.$and.push(statusMatch);
    if (Object.keys(recruiterMatch).length)
      combinedMatch.$and.push(recruiterMatch);
    if (Object.keys(jobTitleMatch).length)
      combinedMatch.$and.push(jobTitleMatch);
    if (Object.keys(searchMatch).length) combinedMatch.$and.push(searchMatch);

    if (combinedMatch.$and.length > 0) {
      pipeline.push({ $match: combinedMatch });
    }

    // Sorting
    pipeline.push({ $sort: sortObject });

    const skip = (page - 1) * limit || 0;
    // Pagination + metadata
    pipeline.push({
      $facet: {
        data: page && limit ? [{ $skip: skip }, { $limit: limit }] : [],
        meta: [{ $count: "total" }],
      },
    });

    const submission = await candidateSubmission.aggregate(pipeline);

    const totalSubmissions = submission[0]?.meta[0]?.total || 0;
    const submissionData = submission[0]?.data || [];
    const totalPages = Math.ceil(totalSubmissions / limit);

    return res.status(200).json({
      message: "All job submissions retrieved successfully",
      success: true,
      results: submissionData,
      totalPages,
      totalSubmissions,
      page,
      limit,
      appliedFilters: {
        status: parseFilterArray(status),
        recruiterName: parseFilterArray(recruiterName),
        jobTitle: parseFilterArray(jobTitle),
        search: search || null,
        searchField: searchField || null,
      },
      appliedSorting: {
        sortBy: sortBy || "submittedAt",
        sortOrder: sortOrder || "desc",
        postedDate: postedDate || null,
        submissionDate: submissionDate || null,
        sortObject,
      },
    });
  } catch (error) {
    console.error("Error in getAllSubmissionByAccountManager:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching submissions",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

//* @Desc Get Submission Details
//* @Route GET /api/v1/candidate/submission-details
//* @Access private, Account Manager, Head Account Manager
const getSubmissionDetails = async (req, res) => {
  try {
    const { submissionId } = req.query;

    if (!submissionId) {
      return res
        .status(400)
        .json({ success: false, message: "Valid Submission ID is required" });
    }

    const pipeline = [
      {
        $match: { submissionId },
      },
      {
        $lookup: {
          from: "candidates",
          localField: "candidate._id",
          foreignField: "_id",
          as: "candidateInfo",
        },
      },
      {
        $unwind: {
          path: "$candidateInfo",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    const submissionDetails = await candidateSubmission.aggregate(pipeline);

    if (!submissionDetails || submissionDetails.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "Submission not found" });
    }

    res.status(200).json({
      success: true,
      data: submissionDetails[0],
    });
  } catch (error) {
    console.error("Error fetching submission details:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

module.exports = {
  updateSubmitedJobsubmission,
  getAllSubmission,
  getAllSubmissionByAccountManager,
  getSubmissionDetails,
};
