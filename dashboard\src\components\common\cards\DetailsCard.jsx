import React from "react";

const DetailsCard = ({ details, open = true, columns = 5 }) => {
  if (!open || !details) return null;

  // Helper to render array values (like education)
  const renderArray = (arr) => {
    if (!Array.isArray(arr) || arr.length === 0) return "-";
    const keys = Object.keys(arr[0] || {});
    return <div className="overflow-x-auto">{arr}</div>;
  };

  const detailArray = Object.entries(details).map(([label, value]) => {
    if (label == "totalYearsOfExperience" || label == "relevantExperience") {
      return {
        label: label
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase()),
        value:
          value && typeof value === "object" && !Array.isArray(value)
            ? value?.year + "." + value?.month + " " + "year"
            : value,
      };
    }

    return {
      label: label
        .replace(/([A-Z])/g, " $1")
        .replace(/^./, (str) => str.toUpperCase()),
      value:
        value === null || value === undefined || value === ""
          ? "-"
          : typeof value === "boolean"
          ? value
            ? "Yes"
            : "No"
          : Array.isArray(value)
          ? renderArray(value)
          : value,
    };
  });

  const rows = [];
  for (let i = 0; i < detailArray.length; i += columns) {
    rows.push(detailArray.slice(i, i + columns));
  }

  // Define grid classes based on columns
  const getGridClass = (cols) => {
    switch (cols) {
      case 4:
        return "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4";
      case 5:
        return "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5";
      default:
        return "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5";
    }
  };

  return (

    <div className="bg-white px-4 py-3 rounded-b-lg border-gray-200">
      <div className="space-y-4">
        {rows?.map((row, i) => (
          <div key={i} className={`${getGridClass(columns)} gap-4`}>
            {row?.map((item) => (
              <div key={item?.label} className="flex flex-col min-w-[140px]">
                <span className="text-sm text-gray-500 truncate">
                  {item?.label}
                </span>
                <span className="text-sm font-medium text-gray-800 truncate">
                  {item?.value}
                </span>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DetailsCard;
