import { apiConnector } from "../apiConnector";
import { notificationEndpoints } from "../apis";

const { GET_NOTIFICATION, MARK_AS_READ_NOTIFICATION } = notificationEndpoints;

export const getNotification = async () => {
  const response = await apiConnector("GET", `${GET_NOTIFICATION}`);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const markAsRead = async (notificationId) => {
  const response = await apiConnector("POST", `${MARK_AS_READ_NOTIFICATION}`, {
    notificationId,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
