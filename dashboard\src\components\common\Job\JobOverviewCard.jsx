import React from "react";
import {
  HiOutlineLocationMarker,
  HiOutlineMail,
  HiOutlineBell,
} from "react-icons/hi";
import Pills from "./Pills";
import JobAnalytics from "./JobAnalytics";

const JobOverviewCard = ({ job }) => {
  if (!job) return null;

  const location = [
    job?.location?.city,
    job?.location?.state,
    job?.location?.country,
  ]
    .filter(Boolean)
    .join(", ");

  const salary = job.salary
    ? `${job?.salary?.currency} ${job?.salary?.min} - ${job?.salary?.max}`
    : "N/A";

  const commission = job.commission
    ? `${job?.commission?.currency} ${job?.commission?.amount}`
    : "N/A";

  const experience = job.experience
    ? `${job?.experience?.min} - ${job?.experience?.max} ${job?.experience?.unit}`
    : "N/A";

  const submissions = {
    submitted:
      job.submission?.candidate
        ?.filter((item) =>
          [
            "submitted",
            "reviewing",
            "submitted to client",
            "selected",
            "interviewing",
            "reject after interview",
            "awaiting offer",
            "rejected",
            "offer released",
            "offer accepted",
            "offer rejected",
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(item?.status)
        )
        .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0,
    accepted:
      job.submission?.candidate
        ?.filter((item) =>
          [
            "selected",
            "interviewing",
            "reject after interview",
            "awaiting offer",
            "rejected",
            "offer released",
            "offer accepted",
            "offer rejected",
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(item?.status)
        )
        .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0,
    working: job?.recruiterCount?.count || 0,
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md w-full flex items-stretch gap-6">
      {/* LEFT SIDE */}
      <div className="flex-1 min-w-0 flex flex-col justify-between">
        <div>
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold flex items-center gap-2">
              {job.jobTitle}
              {job.jobId && (
                <span className="text-base text-gray-500 font-normal">
                  ({job.jobId})
                </span>
              )}
              <HiOutlineBell className="text-gray-500 text-xl" />
            </h2>
          </div>

          <div className="mt-2 flex gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <HiOutlineLocationMarker className="text-lg" />
              <span>{location}</span>
            </div>
            <div className="flex items-center gap-1">
              <HiOutlineMail className="text-lg" />
              <span>
                {job.accountManager && job.accountManager.email
                  ? job.accountManager.email
                  : "N/A"}
              </span>
            </div>
          </div>

          <div className="flex flex-wrap gap-3 text-sm mt-2">
            <Pills title={job.jobType} type="regular" />
            <Pills title={job.industry} type="regular" />
            <Pills title={experience} type="regular" />
            <Pills title={`${job.openings} Openings`} type="regular" />
            <Pills title={salary} type="regular" />
            <Pills title={commission} type="commission" />
            <Pills title={job.priority} type="highPriority" />
          </div>
        </div>
      </div>

      {/* VERTICAL DIVIDER */}
      <div className="w-px bg-gray-300 min-h-[140px] self-stretch" />

      {/* RIGHT SIDE */}
      <div className="flex flex-col gap-4 text-sm text-gray-800 justify-center min-w-[220px]">
        <h3 className="text-md font-semibold text-gray-700">
          Overall Recruiter Submissions
        </h3>
        {[
          { label: "Resume Submitted", value: submissions.submitted },
          { label: "Resume Accepted", value: submissions.accepted },
          { label: "Recruiter Working", value: submissions.working },
        ].map((item, index) => (
          <JobAnalytics key={index} item={item} />
        ))}
      </div>
    </div>
  );
};

export default JobOverviewCard;
