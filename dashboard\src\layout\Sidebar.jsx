import route from "../routes";
import { Link } from "react-router-dom";
const Sidebar = ({ userType = "", isHide = false, setIsHide }) => {
  return (
    <>
      <aside
        id="logo-sidebar"
        className={`fixed top-0 left-0 z-40 ${
          isHide ? "" : "w-64"
        } h-screen p-[24px] transition-transform -translate-x-full bg-white border-r border-gray-200 sm:translate-x-0`}
        aria-label="Sidebar"
      >
        <div className="h-full pb-4 overflow-y-auto bg-white">
          <div
            className={
              isHide
                ? "flex items-center flex-col"
                : "flex items-center justify-between"
            }
          >
            {isHide ? (
              <img
                src="/assets/Logo_Icon.svg"
                className="h-[32px] w-[32px]"
                alt="hirring.com"
              />
            ) : (
              <img
                src="/assets/logo.svg"
                className="h-[45.002px] w-[130.909px]"
                alt="hirring.com"
              />
            )}

            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 25 24"
                fill="none"
                className={
                  isHide ? "cursor-pointer rotate-180" : "cursor-pointer"
                }
                onClick={(e) => {
                  e.preventDefault();
                  setIsHide((item) => !item);
                }}
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M17.4694 15.1151L14.3654 12.0031L17.4774 8.89113C17.7894 8.57913 17.7894 8.07513 17.4774 7.76313C17.3279 7.61332 17.125 7.52914 16.9134 7.52914C16.7018 7.52914 16.4988 7.61332 16.3494 7.76313L12.6774 11.4351C12.3654 11.7471 12.3654 12.2511 12.6774 12.5631L16.3494 16.2351C16.6614 16.5471 17.1654 16.5471 17.4774 16.2351C17.7814 15.9311 17.7814 15.4191 17.4694 15.1151ZM8.90938 7.20312C9.34938 7.20312 9.70938 7.56312 9.70938 8.00313V16.0031C9.70938 16.4431 9.34938 16.8031 8.90938 16.8031C8.46937 16.8031 8.10938 16.4431 8.10938 16.0031V8.00313C8.10938 7.56312 8.46937 7.20312 8.90938 7.20312Z"
                  fill="#27364B"
                />
              </svg>
            </div>
          </div>

          {/* sidebar element  */}
          <ul className={`space-y-2 font-medium  ${isHide ? "mt-4" : "mt-10"}`}>
            {route[userType]?.map((item, key) => {
              return (
                <Link
                  to={item.url}
                  key={key}
                  className="flex items-center gap-3 py-2 px-[12px]  text-[#475569] transition duration-75 rounded-lg hover:bg-[#3E9900] group"
                >
                  <img
                    src={item?.icon}
                    alt={item?.name || ""}
                    className="w-5 h-5"
                  />
                  {isHide ? (
                    ""
                  ) : (
                    <span className="group-hover:text-white text-[14px] transition duration-75 text-[#475569]">
                      {item?.name || ""}
                    </span>
                  )}
                </Link>
              );
            })}
          </ul>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
