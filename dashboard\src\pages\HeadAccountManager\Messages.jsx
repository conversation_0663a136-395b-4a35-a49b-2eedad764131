import { useState } from "react";
import EmptyState from "../../components/common/EmptyState";

const Messages = () => {
  const [messages, setMessages] = useState([]);
  // API integration ready - chat endpoints available

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Messages</h1>
        <p className="text-gray-600 mt-1">View and manage all conversations and messages</p>
      </div>

      {/* Data validation - show content if data exists, otherwise show EmptyState */}
      {messages.length > 0 ? (
        <div className="bg-white rounded-lg shadow">
          {/* Chat interface will go here when API is connected */}
          <div className="p-4">
            {messages.map((message, index) => (
              <div key={index} className="border-b py-3">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{message.sender}</p>
                    <p className="text-sm text-gray-600 mt-1">{message.content}</p>
                  </div>
                  <span className="text-xs text-gray-500">{message.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <EmptyState
          title="No Messages Found"
          description="There are no messages to display at the moment. Messages and conversations will appear here once communication begins."
        />
      )}
    </div>
  );
};

export default Messages;
