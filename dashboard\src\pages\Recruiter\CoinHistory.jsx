import React, { useEffect, useState } from "react";
import StatsCard from "../../components/common/Dashboard/StatsCard";
import TableHeader from "../../components/common/Table/TableHeader";
import PaginationFooter from "../../components/common/Table/TableFooter";
import { getRecruiterCoinHistory } from "../../services/operations/recruiterAPI";

const CoinHistory = () => {
  const [transactions, setTransactions] = useState([]);
  const [balance, setBalance] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [totalSpent, setTotalSpent] = useState(0);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    const fetchCoinHistory = async () => {
      setLoading(true);
      try {
        const response = await getRecruiterCoinHistory(currentPage, pageSize);
        if (response?.success) {
          const data = response.data;
          setTransactions(data.transactions || []);
          setBalance(data.balance || 0);
          setTotalEarned(data.totalEarned || 0);
          setTotalSpent(data.totalSpent || 0);
          setTotalResults(data.total || 0);
          setTotalPages(data.totalPages || 1);
        } else {
          console.error("Failed to fetch coin history.");
        }
      } catch (err) {
        console.error("Error fetching coin history:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchCoinHistory();
  }, [currentPage, pageSize]);

  const columns = [
    { label: "DATE", key: "createdAt" },
    { label: "TYPE", key: "spendType" },
    { label: "COINS", key: "amount" },
    { label: "BALANCE", key: "balanceAfter" },
    { label: "DETAILS", key: "relatedJobId" },
  ];

  return (
    <>
      <div className="flex gap-3 mb-6">
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/Slider.svg"
          lable="Your Balance"
          value={balance}
        />
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/Slider.svg"
          lable="Last 30 days used"
          value={totalSpent}
        />
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/Slider.svg"
          lable="Last 30 days received"
          value={totalEarned}
        />
      </div>

      <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full bg-white">
          <TableHeader columns={columns.map((col) => col.label)} />
          <tbody>
            {transactions.map((item, index) => (
              <tr
                key={index}
                className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
              >
                {columns.map((col) => (
                  <td
                    key={col.key}
                    className="px-4 py-3 whitespace-nowrap capitalize"
                  >
                    {col.key === "createdAt" ? (
                      new Date(item[col.key]).toLocaleDateString()
                    ) : col.key === "relatedJobId" ? (
                      item[col.key]?.jobTitle ? (
                        `${item[col.key].jobTitle} - ${item[col.key].jobId}`
                      ) : (
                        item[col.key]?._id ?? "-"
                      )
                    ) : col.key === "amount" ? (
                      <div className="flex items-center gap-1">
                        <img
                          src={
                            item.transactionType === "earned"
                              ? "/assets/icons/plus.svg"
                              : "/assets/icons/minus.svg"
                          }
                          alt={item.transactionType}
                          className="w-4 h-4"
                        />
                        <span>{item[col.key]}</span>
                      </div>
                    ) : col.key === "spendType" ? (
                      item.transactionType === "earned" ? (
                        item.earnedType || "-"
                      ) : (
                        item.spendType || "-"
                      )
                    ) : (
                      item[col.key]
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>

        <PaginationFooter
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          setPageSize={setPageSize}
          setCurrentPage={setCurrentPage}
          totalResults={totalResults}
        />
      </div>
    </>
  );
};

export default CoinHistory;
