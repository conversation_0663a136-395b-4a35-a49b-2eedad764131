.editor-paragraph {
  margin: 0 0 1em 0;
  line-height: 1.5;
}

.editor-heading-h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}

.editor-heading-h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.75em 0;
}

.editor-heading-h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin: 0.83em 0;
}

.editor-list-ul {
  list-style-type: disc;
  margin-left: 1.5em;
}

.editor-list-ol {
  list-style-type: decimal;
  margin-left: 1.5em;
}

.editor-list-item {
  margin: 0.5em 0;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-link {
  color: blue;
  text-decoration: underline;
}

.editor-code {
  font-family: monospace;
  background-color: #f4f4f4;
  padding: 2px 4px;
  border-radius: 4px;
}

.editor-quote {
  border-left: 4px solid #ccc;
  margin: 1em 0;
  padding-left: 1em;
  color: #666;
  font-style: italic;
}
