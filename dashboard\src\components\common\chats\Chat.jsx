import ChatHeader from "./ChatHeader";
import { useEffect, useState } from "react";
import MessagesSection from "./MessagesSection";
import InputContainer from "./InputContainer";

const Chat = ({ job }) => {
  const [getMessages, setGetMessages] = useState(false);

  const [newChatId, setNewChatId] = useState("");

  useEffect(() => {
    console.log("Get messages state changed:", getMessages);
  }, [getMessages]);

  return (
    <div className="flex flex-col justify-center gap-[1.125rem] w-full">
      <ChatHeader job={job} />
      <MessagesSection
        jobId={job?._id}
        getMessages={getMessages}
        setGetMessages={setGetMessages}
        newChatId={newChatId}
      />
      <InputContainer
        accountManager={job?.accountManager}
        jobId={job?._id}
        setGetMessages={setGetMessages}
        setNewChatId={setNewChatId}
        newChatId={newChatId}
      />
    </div>
  );
};

export default Chat;
