const User = require("../models/User.models");
const Profile = require("../models/RecruiterProfile.models");
const { isEmailValid } = require("../utils/emailValidator");
const ShortUniqueId = require("short-unique-id");
const UAParser = require("ua-parser-js");
const mailSender = require("../utils/mailSender");
const {
  recruiterDetailTemplate,
} = require("../templates/email/RecruiterDetails");
const { v4: uuidv4 } = require("uuid");
const userService = require("../services/userService");
const { accountManager } = require("../models/AccountManager.models");
const bcrypt = require("bcryptjs");

const {
  generateAccessToken,
  generateRefreshToken,
  isTokenBlacklisted,
  verifyRefreshToken,
} = require("../services/tokenService");
const recruiterProfile = require("../models/RecruiterProfile.models");
const { RefreshToken } = require("../models/RefreshToken");

const uid = new ShortUniqueId({
  dictionary: "number",
  length: 10,
});

const userCoinBalance = require("../models/UserCoinBalance");
const { UserVerificationToken } = require("../models/userVerificationToken");
const { generatePassword } = require("../utils/genratePassword");
const generateId = require("../utils/genreateId");
const { default: mongoose } = require("mongoose");

//* @Desc Register a new user
//* @Route POST /api/v1/auth/register
//* @Access Public
exports.registerUser = async (req, res) => {
  try {
    let {
      name,
      email,
      countryCode,
      password,
      phone,
      linkedinUrl,
      userType,
      domain,
      isUserEligibileForITAndHealthcare,
    } = req.body;

    // Input validation
    if (
      !name ||
      !email ||
      !countryCode ||
      !phone ||
      !userType ||
      (!linkedinUrl && userType == "recruiter") ||
      (!domain && userType == "accountManager") ||
      (!password && userType !== "accountManager")
    ) {
      return res.status(400).json({
        success: false,
        message: "Please fill all the fields",
      });
    }

    if (userType === "accountManager") {
      const user = req.user;
      if (!user) {
        return res.status(400).json({
          success: false,
          message: "UserId is required to create an account manager.",
        });
      }
    }

    // Email validation
    if (!isEmailValid(email)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid email",
      });
    }

    email = email?.trim()?.toLowerCase();

    // Phone number validation
    if (!/^\d{8,15}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid phone number",
      });
    }

    // LinkedIn URL validation
    const validLinkedInPatterns = [
      "https://www.linkedin.com/in/",
      "https://linkedin.com/in/",
    ];

    const isValidLinkedInUrl = validLinkedInPatterns?.some((pattern) =>
      linkedinUrl?.startsWith(pattern)
    );

    if (!isValidLinkedInUrl && userType == "recruiter") {
      return res.status(400).json({
        success: false,
        message:
          "Please provide a valid LinkedIn URL starting with https://linkedin.com/in/ or https://www.linkedin.com/in/",
      });
    }

    // Check for existing user
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "User already exists",
      });
    }

    // Name parsing
    const nameArray = name.split(" ");
    const firstName = nameArray[0] || "";
    const lastName =
      nameArray.length > 1 ? nameArray[nameArray.length - 1] : "";
    const middleName =
      nameArray.length >= 2 ? nameArray.slice(1, -1).join(" ") : "";

    // Device details
    const parser = new UAParser(req.headers["user-agent"]);
    const { browser, os } = parser.getResult();
    const ipAddress = req.headers["x-forwarded-for"] || req.ip;
    const deviceDetails = {
      browser: browser?.name || "",
      os: os?.name || "",
      ipAddress,
    };

    const { source, campaign } = req.body;

    // Set default source if not provided
    const userSource = source || "website";

    // Validate campaign for non-website sources
    if (userSource !== "website" && !campaign) {
      return res.status(400).json({
        success: false,
        message: "Campaign is required for non-website sources",
      });
    }

    // genrate password
    const newPassword = password ? password : generatePassword(14);

    // Generate unique userId with retry limit
    let userId =
      userType == "recruiter"
        ? await generateId("HR")
        : userType == "accountManager"
        ? await generateId("AM")
        : await generateId("HAM");

    const userData = {
      name: {
        firstName,
        middleName,
        lastName,
      },
      password: newPassword,
      userId,
      email,
      emailVerified: ["accountManager", "headAccountManager"].includes(userType)
        ? true
        : false,
      phone: {
        countryCode,
        number: phone,
      },
      userType,
      signupDevice: deviceDetails,
      source: {
        name: userSource,
        campaign,
      },
      createdBy: req.user ? req?.user?._id : undefined,
    };

    const user = await userService.createUser(userData);

    if (!user) {
      throw new Error("Failed to create user");
    }

    let profile;
    if (userType == "recruiter") {
      profile = await Profile.create({
        linkedin: linkedinUrl,
        user: {
          _id: user._id,
          userId: user.userId,
        },
        isUserEligibileForITAndHealthcare: isUserEligibileForITAndHealthcare,
      });
      await userCoinBalance.create({
        userId: user._id,
      });
    } else {
      profile = await accountManager.create({
        user: {
          _id: user._id,
          userId: user.userId,
        },
        domain,
      });
    }

    const emailBody = `
  <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    
    <p>Dear ${firstName},</p>
    
    <p>You can use the following credentials to log into your account:</p>
    
    <table style="border-collapse: collapse; margin-top: 10px;">
      <tr>
        <td style="padding: 6px 12px; font-weight: bold;">Email:</td>
        <td style="padding: 6px 12px; background-color: #f4f4f4;">${email}</td>
      </tr>
      <tr>
        <td style="padding: 6px 12px; font-weight: bold;">Password:</td>
        <td style="padding: 6px 12px; background-color: #f4f4f4;">${newPassword}</td>
      </tr>
    </table>

    <p style="margin-top: 20px;">
      Please change your password after your first login for security reasons.
    </p>

    <p>Regards,<br/>The Support Team</p>
  </div>
`;

    if (userType == "accountManager") {
      await mailSender(email, "Hirring.com Account Credentials", emailBody);
    }

    if (!profile) {
      // Rollback user creation if profile creation fails
      await User.deleteOne({ _id: user._id });
      throw new Error("Failed to create user profile");
    }
    return res.status(201).json({
      success: true,
      message: "User registered successfully",
      user,
    });
  } catch (error) {
    console.error("Registration error:", error);

    // Handle specific error types
    if (error.name === "MongoError" && error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: "Duplicate entry detected",
      });
    }

    if (error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Invalid input data",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

//* @Desc Login user
//* @Route POST /api/v1/auth/login
//* @Access Public

exports.login = async (req, res) => {
  try {
    let { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "email & password are required." });
    }

    email = email?.trim()?.toLowerCase();
    // Find user
    const user = await userService.findUserByEmail(email);
    if (!user) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({ message: "Account has been deactivated" });
    }

    // Check password
    const isMatch = await userService.verifyPassword(user, password);
    if (!isMatch) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Device details
    const parser = new UAParser(req.headers["user-agent"]);
    const { browser, os } = parser.getResult();
    const ipAddress = req.headers["x-forwarded-for"] || req.ip;
    const deviceDetails = {
      userAgent: browser?.name || "",
      deviceName: os?.name || "",
      ipAddress,
    };

    // Update last login time
    await userService.updateLastLogin(user._id, deviceDetails);

    // Generate tokens
    const accessToken = generateAccessToken({
      _id: user._id,
      userId: user.userId,
      role: user.role,
      tokenVersion: user.tokenVersion,
    });
    const refreshToken = await generateRefreshToken(
      user._id,
      user.tokenVersion
    );
    if (user.userType == "recruiter") {
      if (!user.emailVerified) {
        // send verification email
        const token = uuidv4();
        await UserVerificationToken.create({
          userId: user._id,
          token,
          type: "emailVerification",
        });
        const verificationLink = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
        const verificationBody = `<p>Dear ${user.name.firstName || "User"},</p>
          <p>Please click the link below to verify your email.</p>
          <p>
            <a href="${verificationLink}" style="color: #2563eb; text-decoration: underline;">
              Verify Email
            </a>
          </p>
          <p>If you did not request this, please ignore this email.</p>
          <p>Regards,<br/>The Support Team</p>`;
        await mailSender(user.email, "Verify Your Email", verificationBody);

        res.status(200).json({
          user: {
            emailVerified: user.emailVerified,
            id: user._id,
            userId: user.userId,
            role: user.userType,
          },
        });
      } else {
        res.status(200).json({
          accessToken,
          refreshToken,
          user: {
            id: user._id,
            email: user.email,
            userId: user.userId,
            emailVerified: user.emailVerified,
            firstName: user.name.firstName,
            lastName: user.name.lastName,
            role: user.userType,
          },
        });
      }
    } else {
      res.status(200).json({
        accessToken,
        refreshToken,
        user: {
          id: user._id,
          email: user.email,
          userId: user.userId,
          emailVerified: user.emailVerified,
          firstName: user.name.firstName,
          lastName: user.name.lastName,
          role: user.userType,
        },
      });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

//* @Desc Refresh access token
//* @Route POST /api/v1/auth/refresh-token
//* @Access Public

exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: "Refresh token is required" });
    }

    // Check if token is blacklisted
    // const isBlacklisted = await isTokenBlacklisted(refreshToken);
    // if (isBlacklisted) {
    //   return res
    //     .status(401)
    //     .json({ message: "Refresh token has been invalidated" });
    // }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);
    if (!decoded.success) {
      return res
        .status(401)
        .json({ message: "Refresh token is invalid, please login again" });
    }

    const isRefreshToken = await RefreshToken.findOne({
      userId: decoded.data.userId,
      token: refreshToken,
    });

    if (!isRefreshToken) {
      return res
        .status(401)
        .json({ message: "Refresh token is invalid, please login again." });
    }
    // Get user
    const user = await userService.findUserById(decoded.data.userId);
    if (!user) {
      return res.status(400).json({ message: "User not found" });
    }

    // Check if user is active
    // if (!user.isActive) {
    //   return res.status(401).json({ message: "Account has been deactivated" });
    // }

    // Check token version (for forced logout)
    if (user.tokenVersion !== decoded.data.tokenVersion) {
      return res
        .status(401)
        .json({ message: "Refresh token is invalid, please login again" });
    }

    // Generate new tokens
    const newAccessToken = generateAccessToken({
      _id: user._id,
      userId: user.userId,
      role: user.role,
      tokenVersion: user.tokenVersion,
    });

    res.status(200).json({
      accessToken: newAccessToken,
      refreshToken: refreshToken,
    });
  } catch (error) {
    console.error("Refresh token error:", error);
    res.status(401).json({
      success: false,
      message: `${error.message || "Internal server error"}`,
    });
  }
};

//* @Desc Logout user
//* @Route POST /api/v1/auth/logout
//* @Access Private

exports.logout = async (req, res) => {
  try {
    // Add current tokens to blacklist
    const accessToken = req.header("Authorization")?.replace("Bearer ", "");
    const { refreshToken } = req.body;

    if (accessToken) {
      await tokenService.blacklistToken(accessToken);
    }

    if (refreshToken) {
      await tokenService.blacklistToken(refreshToken);
    }

    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

//* @Desc user info
//* @Route POST /api/v1/auth/user
//* @Access Private
exports.userInfo = async (req, res) => {
  try {
    const user = req.user;

    if (user.userType == "recruiter") {
      const recruiter = await recruiterProfile.findOne({
        "user.userId": user.userId,
      });

      const profiles = await recruiterProfile.aggregate([
        {
          $match: {
            "user.userId": user?.userId,
          },
        },
        {
          $unwind: {
            path: "$jobsWorkingOn",
            preserveNullAndEmptyArrays: false,
          },
        },

        {
          $match: {
            "jobsWorkingOn.status": "assigned",
            "jobsWorkingOn.isActive": true,
            "jobsWorkingOn.isSlotEmpty": false,
          },
        },
        {
          $count: "count",
        },
      ]);
      let emptySlot = profiles[0]?.count;

      const userCoin = await userCoinBalance.findOne({
        userId: user._id,
      });

      return res.status(200).json({
        success: true,
        data: {
          firstName: user.name.firstName,
          middleName: user.name.middleName,
          lastName: user.name.lastName,
          email: user.email,
          phoneNo: user.phone,
          profile: recruiter,
          id: user._id,
          userID: user.userId,
          role: user.userType,
          coinBalance: userCoin?.currentBalance || 0,
          emptySlot,
        },
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        email: user.email,
        firstName: user.name.firstName,
        middleName: user.name.middleName,
        id: user._id,
        userID: user.userId,
        lastName: user.name.lastName,
        role: user.userType,
      },
    });
  } catch (error) {
    res.status(400).json({ success: false, message: error.message });
  }
};

//* @Desc Force logout user
//* @Route POST /api/v1/auth/force-logout
//* @Access Private (Admin only)

exports.forceLogout = async (req, res) => {
  try {
    const { userId } = req.params;

    // Only admins can force logout
    if (req.user.role !== "admin" || req.user.role !== "headAccountManager") {
      return res.status(403).json({
        message: "You are not authorized to perform this action",
      });
    }

    // Increment token version to invalidate all current tokens
    try {
      const user = await userService.incrementTokenVersion(userId);
      res.status(200).json({
        message: `User ${user.email} has been logged out from all devices`,
      });
    } catch (error) {
      if (error.message === "User not found") {
        return res.status(404).json({ message: "User not found" });
      }
      throw error;
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

exports.verifyEmail = async (req, res) => {
  try {
    const token = req.query.token;
    const type = req.query.type;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: "Missing token",
      });
    }

    if (type == "passwordReset") {
      const record = await UserVerificationToken.findOne({
        token,
        type: "passwordReset",
      });
      if (!record || record.expiresAt < new Date()) {
        return res.status(400).json({
          success: false,
          message: "Invalid or expired token",
        });
      }
      const user = await User.findById(record.userId);
      if (!user) {
        return res.status(400).json({
          success: false,
          message: "User not found",
        });
      }

      await UserVerificationToken.deleteOne({ token });

      // Generate a new token for password reset
      const tokens = uuidv4();
      await UserVerificationToken.create({
        userId: user._id,
        token: tokens,
        type: "passwordReset",
      });

      return res.status(200).json({
        success: true,
        message: "Password reset token verified successfully.",
        token: tokens,
      });
    }

    const record = await UserVerificationToken.findOne({
      token,
      type: "emailVerification",
    });
    if (!record || record.expiresAt < new Date()) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired token",
      });
    }
    const user = await User.findById(record.userId);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    // Mark email as verified
    user.emailVerified = true;
    const userInfo = await user.save();

    // Delete the verification token
    await UserVerificationToken.deleteOne({ token });

    const accessToken = generateAccessToken({
      _id: userInfo._id,
      userId: userInfo.userId,
      role: userInfo.userType,
      tokenVersion: userInfo.tokenVersion,
    });

    const refreshToken = await generateRefreshToken(
      userInfo._id,
      userInfo.tokenVersion
    );

    res.status(200).json({
      success: true,
      message: "Email verified successfully",
      accessToken,
      refreshToken,
      userInfo: {
        id: userInfo._id,
        email: userInfo.email,
        firstName: userInfo.name.firstName,
        lastName: userInfo.name.lastName,
        role: userInfo.userType,
        emailVerified: userInfo.emailVerified,
      },
    });
  } catch (error) {
    console.error("Email verification error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: "Email is required",
      });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    await UserVerificationToken.deleteOne({ userId: user._id });

    const token = uuidv4();
    await UserVerificationToken.create({
      userId: user._id,
      token,
      type: "passwordReset",
    });

    // Send reset link via email
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    const resetPassbody = `<p>Dear ${user.name.firstName || "User"},</p>
          <p>Please click the link below to reset your password.</p>
          <p>
            <a href="${resetLink}" style="color: #2563eb; text-decoration: underline;">
              Reset Password
            </a>
          </p>
          <p>If you did not request this, please ignore this email.</p>
          <p>Regards,<br/>The Hirring Team</p>`;

    await mailSender(email, "Hirring.com Password Reset", resetPassbody);

    res.status(200).json({
      success: true,
      message: "Password reset link sent to your email",
    });
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

exports.resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;
    if (!token || !password) {
      return res.status(400).json({
        success: false,
        message: "Token and password are required",
      });
    }
    const record = await UserVerificationToken.findOne({
      token,
      type: "passwordReset",
    });
    if (!record) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired token",
      });
    }

    const user = await User.findById(record.userId);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }
    // Update user password
    const hashedPassword = await bcrypt.hash(password, 10);
    user.password = hashedPassword;
    await user.save();

    // Delete the verification token
    await UserVerificationToken.deleteOne({ token });
    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

//* @Desc fixing recruiter profile
//* @Route POST /api/v1/auth/fixing-recruiter-details
//* @Access Private (Admin only)
exports.FixingRecruiterDetails = async (req, res) => {
  try {
    const recruiters = await recruiterProfile.find({}).lean();
    const bulkOps = [];

    for (const recruiter of recruiters) {
      let updateFields = {};
      const { domain, specialization } = recruiter;

      if (Array.isArray(domain) && domain.length > 0) {
        const val = domain[0];
        if (typeof val === "string") {
          if (val.trim().startsWith("[")) {
            try {
              const parsed = JSON.parse(val);
              if (Array.isArray(parsed)) updateFields.domain = parsed;
            } catch {}
          } else if (val.includes(",")) {
            updateFields.domain = val.split(",").map((v) => v.trim());
          }
        }
      }

      if (Array.isArray(specialization) && specialization.length > 0) {
        const val = specialization[0];
        if (typeof val === "string" && val.includes(",")) {
          updateFields.candidateRole = val.split(",").map((v) => v.trim());
        }
      }

      if (Object.keys(updateFields).length > 0) {
        let recruiterId = recruiter._id;

        if (typeof recruiterId === "string") {
          recruiterId = new mongoose.Types.ObjectId(recruiterId);
        }
        bulkOps.push({
          updateOne: {
            filter: { _id: recruiterId },
            update: { $set: updateFields },
          },
        });
      }
    }

    if (bulkOps.length > 0) {
      await recruiterProfile.bulkWrite(bulkOps);
    }

    res.json({ bulkOps, recruiters, updatedCount: bulkOps.length });
  } catch (error) {
    console.log("error in Fixing recruiter profile ", error);
  }
};
