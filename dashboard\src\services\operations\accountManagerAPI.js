import { apiConnector } from "../apiConnector";
import { accountManagerEndpoints } from "../apis";

const { GET_ALL_SUBMISSIONS_API } = accountManagerEndpoints;

// Get all submissions for Account Manager with pagination, filters, sorting, and search
export const getAllSubmissions = async (
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  try {
    const queryParams = new URLSearchParams();

    // Basic pagination
    queryParams.append("page", page);
    queryParams.append("limit", limit);

    // Filters
    Object.keys(filters).forEach((key) => {
      const value = filters[key];
      if (Array.isArray(value) && value.length > 0) {
        queryParams.append(key, value.join(","));
      } else if (typeof value === "string" && value.trim()) {
        queryParams.append(key, value.trim());
      }
    });

    // Search
    if (search && search.searchTerm && search.searchTerm.trim()) {
      queryParams.append("search", search.searchTerm.trim());
      if (search.searchField && search.searchField !== "all") {
        queryParams.append("searchField", search.searchField);
      }
    }

    // Sorting
    if (sorting.submissionDate) {
      queryParams.append("submissionDate", sorting.submissionDate);
    } else if (sorting.postedDate) {
      queryParams.append("postedDate", sorting.postedDate); // legacy support
    }

    if (sorting.sortBy) queryParams.append("sortBy", sorting.sortBy);
    if (sorting.sortOrder) queryParams.append("sortOrder", sorting.sortOrder);

    // Make the API call
    const response = await apiConnector(
      "GET",
      `${GET_ALL_SUBMISSIONS_API}?${queryParams.toString()}`
    );

    return response?.data;
  } catch (error) {
    console.error("Error fetching submissions:", error);
    throw error;
  }
};
