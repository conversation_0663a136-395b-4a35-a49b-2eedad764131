import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import EmailStatusCard from "../components/common/cards/EmailStatusCard";
import { loginToken, userInfo } from "../redux/reducer/auth.slice";

const EmailVerifiedPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { accessToken, refreshToken, userinfo } = location.state || {};

  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(
        loginToken({
          accessToken: accessToken,
          refreshToken: refreshToken,
        })
      );

      dispatch(userInfo(userinfo));
      navigate("/");
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <EmailStatusCard
      title="Great! Your Email is Now Verified"
      image={"/assets/images/email-verified.svg"}
      message={
        "Thank you for verifying your email. You'll be redirected to your dashboard in 3 seconds. We're excited to have you on board!"
      }
    />
  );
};

export default EmailVerifiedPage;
