import React, { useEffect, useState, useCallback, useRef } from "react";
import { useSearchParams, useNavigate, useLocation } from "react-router-dom";
import FormStepper from "../../components/core/recruiter/candidates/FormStepper";
import PersonalDetailsForm from "../../components/core/recruiter/candidates/AddCandidate/PersonalDetailsForm";
import LicensingForm from "../../components/core/recruiter/candidates/AddCandidate/LicensingForm";
import CertificationsForm from "../../components/core/recruiter/candidates/AddCandidate/CerificationsForm";
import EducationForm from "../../components/core/recruiter/candidates/AddCandidate/EducationForm";
import WorkHistoryForm from "../../components/core/recruiter/candidates/AddCandidate/WorkHistoryForm";
import SkillExperienceForm from "../../components/core/recruiter/candidates/AddCandidate/SkillExperienceForm";
import HealthComplianceForm from "../../components/core/recruiter/candidates/AddCandidate/HealthComplianceForm";
import SubmissionDetailsForm from "../../components/core/recruiter/candidates/AddCandidate/SubmissionDetailsForm";
import DocumentAttachmentForm from "../../components/core/recruiter/candidates/AddCandidate/DocumentAttachmentForm";
import AppButton from "../../components/common/Button/AppButton";
import JobOverviewCard from "../../components/common/Job/JobOverviewCard";
import { Formik } from "formik";
import { useDispatch, useSelector } from "react-redux";
import { updateUserCoins } from "../../redux/reducer/auth.slice";
import {
  setCandidateID,
  clearCandidateID,
} from "../../redux/reducer/candidate.slice";
import {
  createCandidate,
  updateCandidate,
  getCandidate,
  submitCandidateToJob,
  instantSubmitCandidate,
} from "../../services/operations/candidateAPI";
import { getJobByID } from "../../services/operations/jobAPI";
import { toast, ToastContainer, Slide } from "react-toastify";

import {
  User,
  Briefcase,
  FileBadge,
  GraduationCap,
  ClipboardList,
  Layers,
  ShieldCheck,
  UploadCloud,
  FileText,
} from "lucide-react";

const stepIcons = [
  User,
  Briefcase,
  FileBadge,
  GraduationCap,
  ClipboardList,
  Layers,
  ShieldCheck,
  UploadCloud,
  FileText,
];

const AddCandidates = () => {
  const dispatch = useDispatch();
  const candidateID = useSelector((data) => data.candidate?.candidateID);
  const userInfo = useSelector((data) => data.auth?.user);
  const [searchParams, setSearchParams] = useSearchParams();
  const jobId = searchParams.get("jobId");
  const [job, setJob] = useState(null);

  const [steps, setSteps] = useState([
    "Personal Details",
    "Licensing (Only for Contractual)",
    "Certifications",
    "Education",
    "Work History (For Contractual)",
    "Skill & Experience",
    "Health & Compliance",
    "Submission Details",
    "Document Attachment",
  ]);

  const [initialFormData, setInitialFormData] = useState({
    personalDetails: {
      firstName: "",
      lastName: "",
      phoneCountryCode: "",
      phoneNumber: "",
      emailAddress: "",
      currentAddress: "",
      country: "",
      city: "",
      state: "",
      zipcode: "",
      relocationWillingness: "",
      workAuthorizationStatus: "",
      ssnLast4Digit: "",
      availableStartDate: "",
    },
    licensing: {
      stateLicenses: "",
      licenseExpireDate: "",
      compactLicense: "",
    },
    certification: {
      blsCertification: "",
      blsExpiration: "",
      aclsPalsNals: "",
      aclsPalsNalsExpiration: "",
      otherRelevantCertificate: "",
    },
    education: [
      {
        degree: "",
        collegeName: "",
        graduationYear: "",
      },
    ],
    workHistory: {
      mostRecentEmployer: "",
      positionTitle: "",
      employmentDate: "",
      reasonForLeaving: "",
      supervisorReferenceName: "",
      supervisorReferenceTitle: "",
      supervisorReferenceContact: "",
      professionalReferenceName: "",
      professionalReferenceContact1: "",
      professionalReferenceName2: "",
      professionalReferenceContact2: "",
    },
    skillsAndExperience: {
      totalYearsOfExperience: { month: "", year: "" },
      relevantExperience: { month: "", year: "" },
      otherSkills: "",
    },
    healthAndCompliance: {
      covid19Status: "",
      dateOfLastCovid19Dose: "",
      boosterReceived: "",
      proofOfVaccinationAvailable: "",
      fluVaccination: "",
    },
    submissionDetails: {
      rateExpectation: "",
      referenceProvider: "",
      candidateAvailabilityForInterview: "",
      additionalNote: "",
    },
    documentAttachments: {
      resume: null,
      coverLetter: null,
      licenseCopy: null,
      blsCertificate: null,
      aclsPalsNalsCertificate: null,
      fluVaccinationProof: null,
      covid19VaccinationProof: null,
      recentSkillChecklist: null,
    },
  });

  const [stepKeys, setStepKeys] = useState([
    "personalDetails",
    "licensing",
    "certification",
    "education",
    "workHistory",
    "skillsAndExperience",
    "healthAndCompliance",
    "submissionDetails",
    "documentAttachments",
  ]);

  const initialStep = parseInt(searchParams.get("step")) || 0;
  const [currentStepIndex, setCurrentStepIndex] = useState(
    isNaN(initialStep) ? 0 : Math.min(initialStep, steps.length - 1)
  );

  const navigate = useNavigate();
  const isInstantSubmit = searchParams.get("isInstantSubmit") === "true";

  const [visitedSteps, setVisitedSteps] = useState([0]);
  const [isStepValid, setIsStepValid] = useState(false);
  const [isStepDirty, setIsStepDirty] = useState(false);
  const formikSubmitRef = useRef(null);
  const formikRef = useRef();
  const location = useLocation();
  const isJobSubmission = location.search?.includes("jobId");

  const validate = (values) => {
    const errors = {};
    const stepKey = stepKeys[currentStepIndex];

    // PERSONAL DETAILS
    if (stepKey === "personalDetails") {
      const pd = values.personalDetails;
      const pdErrors = {};
      if (!pd.firstName) pdErrors.firstName = "First name is required";
      if (!pd.lastName) pdErrors.lastName = "Last name is required";
      if (!pd.phoneNumber) {
        pdErrors.phoneNumber = "Phone number is required";
      } else {
        // Validate only digits, 10-15 length
        const localNumber = pd.phoneNumber.replace(/\D/g, "");
        if (localNumber.length < 10 || localNumber.length > 15) {
          pdErrors.phoneNumber = "Phone number must be between 10 to 15 digits";
        }
      }
      if (!pd.emailAddress) {
        pdErrors.emailAddress = "Email is required";
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(pd.emailAddress)) {
        pdErrors.emailAddress = "Invalid email format";
      }
      if (!pd.currentAddress)
        pdErrors.currentAddress = "Current address is required";
      if (!pd.country) pdErrors.country = "Country is required";
      if (!pd.city) pdErrors.city = "City is required";
      if (!pd.state) pdErrors.state = "State is required";
      if (!pd.zipcode) {
        pdErrors.zipcode = "Zipcode is required";
      } else if (!/^\d+$/.test(pd.zipcode)) {
        pdErrors.zipcode = "Zipcode must contain only digits";
      }
      if (!pd.workAuthorizationStatus)
        pdErrors.workAuthorizationStatus =
          "Work authorization status is required";
      // if (!pd.ssnLast4Digit) {
      //   pdErrors.ssnLast4Digit = "SSN last 4 digits are required";
      // } else if (!/^\d{4}$/.test(pd.ssnLast4Digit)) {
      //   pdErrors.ssnLast4Digit = "SSN must be exactly 4 digits";
      // }
      if (
        pd.relocationWillingness === "" ||
        pd.relocationWillingness === undefined
      ) {
        pdErrors.relocationWillingness = "Relocation willingness is required";
      }
      if (!pd.availableStartDate)
        pdErrors.availableStartDate = "Available start date is required";
      if (Object.keys(pdErrors).length) errors.personalDetails = pdErrors;
    }

    // LICENSING
    if (stepKey === "licensing") {
      const lic = values.licensing;
      const licErrors = {};
      if (!lic.stateLicenses)
        licErrors.stateLicenses = "State license status is required";
      // if (lic.stateLicenses === "yes" && !lic.licenseExpireDate) {
      //   licErrors.licenseExpireDate = "License expiration date is required";
      // }
      if (lic.compactLicense === "" || lic.compactLicense === undefined) {
        licErrors.compactLicense = "Compact license status is required";
      }
      if (Object.keys(licErrors).length) errors.licensing = licErrors;
    }

    // CERTIFICATION
    if (stepKey === "certification") {
      const cert = values.certification;
      const certErrors = {};
      if (!cert.blsCertification)
        certErrors.blsCertification = "BLS certification status is required";
      // if (cert.blsCertification === "yes" && !cert.blsExpiration) {
      //   certErrors.blsExpiration =
      //     "BLS certification expiration date is required";
      // }
      if (!cert.aclsPalsNals)
        certErrors.aclsPalsNals =
          "ACLS/PALS/NALS certification status is required";
      // if (cert.aclsPalsNals === "yes" && !cert.aclsPalsNalsExpiration) {
      //   certErrors.aclsPalsNalsExpiration =
      //     "ACLS/PALS/NALS expiration date is required";
      // }
      if (Object.keys(certErrors).length) errors.certification = certErrors;
    }

    // EDUCATION
    if (stepKey === "education") {
      let hasErrors = false;
      errors.education = [];
      values.education.forEach((edu, index) => {
        const eduErrors = {};
        if (!edu.degree) eduErrors.degree = "Degree is required";
        if (!edu.collegeName)
          eduErrors.collegeName = "College name is required";

        // if (!edu.graduationYear || edu.graduationYear === "") {
        //   eduErrors.graduationYear = "Graduation year is required";
        // } else if (!/^\d{4}$/.test(edu.graduationYear)) {
        //   eduErrors.graduationYear = "Graduation year must be 4 digits";
        // }

        if (Object.keys(eduErrors).length) {
          errors.education[index] = eduErrors;
          hasErrors = true;
        }
      });
      if (!hasErrors) delete errors.education;
    }

    // WORK HISTORY
    if (stepKey === "workHistory") {
      const wh = values.workHistory;
      const whErrors = {};
      if (!wh.mostRecentEmployer)
        whErrors.mostRecentEmployer = "Most recent employer is required";
      if (!wh.positionTitle)
        whErrors.positionTitle = "Position title is required";
      if (!wh.employmentDate)
        whErrors.employmentDate = "Employment date is required";
      if (!wh.reasonForLeaving)
        whErrors.reasonForLeaving = "Reason for leaving is required";
      if (!wh.supervisorReferenceName)
        whErrors.supervisorReferenceName =
          "Supervisor reference name is required";
      if (!wh.supervisorReferenceTitle)
        whErrors.supervisorReferenceTitle =
          "Supervisor reference title is required";
      if (!wh.supervisorReferenceContact) {
        whErrors.supervisorReferenceContact =
          "Supervisor reference contact is required";
      } else {
        const digits = wh.supervisorReferenceContact.replace(/\D/g, "");
        if (digits.length < 10 || digits.length > 15) {
          whErrors.supervisorReferenceContact =
            "Contact must be 10 to 15 digits";
        }
      }

      if (!wh.professionalReferenceName)
        whErrors.professionalReferenceName =
          "Professional reference name is required";
      if (!wh.professionalReferenceContact1) {
        whErrors.professionalReferenceContact1 =
          "Professional reference contact 1 is required";
      } else {
        const digits = wh.professionalReferenceContact1.replace(/\D/g, "");
        if (digits.length < 10 || digits.length > 15) {
          whErrors.professionalReferenceContact1 =
            "Contact must be 10 to 15 digits";
        }
      }
      if (!wh.professionalReferenceName2)
        whErrors.professionalReferenceName2 =
          "Professional reference name 2 is required";
      if (!wh.professionalReferenceContact2) {
        whErrors.professionalReferenceContact2 =
          "Professional reference contact 2 is required";
      } else {
        const digits = wh.professionalReferenceContact2.replace(/\D/g, "");
        if (digits.length < 10 || digits.length > 15) {
          whErrors.professionalReferenceContact2 =
            "Contact must be 10 to 15 digits";
        }
      }
      if (Object.keys(whErrors).length) errors.workHistory = whErrors;
    }

    // SKILLS & EXPERIENCE
    if (stepKey === "skillsAndExperience") {
      const sk = values.skillsAndExperience;
      const skErrors = {};
      if (!sk.totalYearsOfExperience.year)
        skErrors.totalYearsOfExperience = {
          year: "Total years of experience is required.",
        };
      if (!sk.relevantExperience.year)
        skErrors.relevantExperience = {
          year: "Relevant experience is required.",
        };
      if (Object.keys(skErrors).length) errors.skillsAndExperience = skErrors;
    }

    // HEALTH & COMPLIANCE
    if (stepKey === "healthAndCompliance") {
      const hc = values.healthAndCompliance;
      const hcErrors = {};
      if (!hc.covid19Status)
        hcErrors.covid19Status = "COVID-19 status is required";
      // if (hc.covid19Status === "fully" && !hc.dateOfLastCovid19Dose) {
      //   hcErrors.dateOfLastCovid19Dose =
      //     "Date of last COVID-19 dose is required for fully vaccinated status";
      // }
      if (!hc.boosterReceived)
        hcErrors.boosterReceived = "Booster received status is required";
      // Only require proofOfVaccinationAvailable if covid19Status is "fully"
      if (
        hc.covid19Status === "fully" &&
        (hc.proofOfVaccinationAvailable === "" ||
          hc.proofOfVaccinationAvailable === undefined)
      ) {
        hcErrors.proofOfVaccinationAvailable =
          "Proof of vaccination available is required";
      }
      // Do NOT require proofOfVaccinationAvailable for "none" or "exempt"
      if (!hc.fluVaccination)
        hcErrors.fluVaccination = "Flu vaccination status is required";
      if (Object.keys(hcErrors).length) errors.healthAndCompliance = hcErrors;
    }
    // SUBMISSION DETAILS
    if (stepKey === "submissionDetails") {
      const sub = values.submissionDetails;
      const subErrors = {};
      if (!sub.rateExpectation)
        subErrors.rateExpectation = "Rate expectation is required";
      if (!sub.referenceProvider)
        subErrors.referenceProvider = "Reference provider is required";
      if (!sub.candidateAvailabilityForInterview)
        subErrors.candidateAvailabilityForInterview =
          "Candidate availability is required";

      if (Object.keys(subErrors).length) errors.submissionDetails = subErrors;
    }

    // DOCUMENT ATTACHMENTS
    if (stepKey === "documentAttachments") {
      const docs = values.documentAttachments;
      const docsErrors = {};
      if (!docs.resume) docsErrors.resume = "Resume is required";
      if (!docs.coverLetter)
        docsErrors.coverLetter = "Cover letter is required";
      if (Object.keys(docsErrors).length)
        errors.documentAttachments = docsErrors;
    }

    return errors;
  };

  // On mount, sync candidateID from URL to Redux if present
  useEffect(() => {
    const idFromURL = searchParams.get("candidateID");
    if (idFromURL && !candidateID) {
      dispatch(setCandidateID(idFromURL));
    }
  }, [searchParams, candidateID, dispatch]);

  const fetchCandidateData = async () => {
    if (candidateID) {
      try {
        const getCan = await getCandidate(candidateID);
        const candidate = getCan[0];
        // Only set values if formikRef.current is available
        if (formikRef.current) {
          const licensing = candidate.licensing || {};
          const certification = candidate.certification || {};
          const educationArr =
            Array.isArray(candidate.education) && candidate.education.length > 0
              ? candidate.education.filter(
                  (e) =>
                    e &&
                    typeof e === "object" &&
                    (e.degree?.trim() || e.collegeName?.trim())
                )
              : [{ degree: "", collegeName: "", graduationYear: "" }];
          formikRef.current.setValues({
            personalDetails: candidate.personalDetails
              ? {
                  ...candidate.personalDetails,
                  phoneNumber: `+${candidate.personalDetails.phoneCountryCode} ${candidate.personalDetails.phoneNumber}`,
                }
              : initialFormData.personalDetails,

            licensing: {
              compactLicense:
                licensing.compactLicense === true
                  ? "yes"
                  : licensing.compactLicense === false
                  ? "no"
                  : "",
              licenseExpireDate: licensing.licenseExpireDate || "",
              stateLicenses: licensing.stateLicenses || "",
            },

            certification: {
              blsCertification:
                certification.blsCertification === true
                  ? "yes"
                  : certification.blsCertification === false
                  ? "no"
                  : "",
              blsExpiration: certification.blsExpiration || "",
              aclsPalsNals:
                certification.aclsPalsNals === true
                  ? "yes"
                  : certification.aclsPalsNals === false
                  ? "no"
                  : "",
              aclsPalsNalsExpiration:
                certification.aclsPalsNalsExpiration || "",
              otherRelevantCertificate:
                certification.otherRelevantCertificate || "",
            },
            education: educationArr,
            workHistory: candidate.workHistory || initialFormData.workHistory,
            skillsAndExperience:
              candidate.skillsAndExperience ||
              initialFormData.skillsAndExperience,

            healthAndCompliance: candidate.healthAndCompliance
              ? {
                  covid19Status:
                    candidate.healthAndCompliance.covid19Status === true
                      ? "fully"
                      : candidate.healthAndCompliance.covid19Status === false
                      ? "none"
                      : candidate.healthAndCompliance.covid19Status || "",
                  dateOfLastCovid19Dose:
                    candidate.healthAndCompliance.dateOfLastCovid19Dose || "",
                  boosterReceived:
                    candidate.healthAndCompliance.boosterReceived === true
                      ? "yes"
                      : candidate.healthAndCompliance.boosterReceived === false
                      ? "no"
                      : candidate.healthAndCompliance.boosterReceived || "",
                  proofOfVaccinationAvailable:
                    candidate.healthAndCompliance
                      .proofOfVaccinationAvailable === true
                      ? "yes"
                      : candidate.healthAndCompliance
                          .proofOfVaccinationAvailable === false
                      ? "no"
                      : candidate.healthAndCompliance
                          .proofOfVaccinationAvailable || "",
                  fluVaccination:
                    candidate.healthAndCompliance.fluVaccination === true
                      ? "yes"
                      : candidate.healthAndCompliance.fluVaccination === false
                      ? "no"
                      : candidate.healthAndCompliance.fluVaccination || "",
                }
              : initialFormData.healthAndCompliance,

            submissionDetails:
              candidate.submissionDetails || initialFormData.submissionDetails,

            documentAttachments: candidate.documentAttachments
              ? {
                  resume: candidate.documentAttachments.resume || null,
                  coverLetter:
                    candidate.documentAttachments.coverLetter || null,
                  licenseCopy:
                    candidate.documentAttachments.licenseCopy || null,
                  blsCertificate:
                    candidate.documentAttachments.blsCertificate || null,
                  aclsPalsNalsCertificate:
                    candidate.documentAttachments.aclsPalsNalsCertificate ||
                    null,
                  fluVaccinationProof:
                    candidate.documentAttachments.fluVaccinationProof || null,
                  covid19VaccinationProof:
                    candidate.documentAttachments.covid19VaccinationProof ||
                    null,
                  recentSkillChecklist:
                    candidate.documentAttachments.recentSkillChecklist || null,
                }
              : initialFormData.documentAttachments,
          });
        }
      } catch (err) {
        console.error("getCandidate error:", err);
        toast.error("Failed to fetch candidate data.");
      }
    }
  };

  useEffect(() => {
    if (jobId) {
      getJobByID(jobId)
        .then((data) => {
          if (Array.isArray(data?.data) && data.data.length > 0) {
            const jobs = data.data[0];
            if (jobs.jobType == "full-time") {
              setSteps([
                "Personal Details",
                "Certifications",
                "Education",
                "Skill & Experience",
                "Health & Compliance",
                "Submission Details",
                "Document Attachment",
              ]);

              setStepKeys([
                "personalDetails",
                "certification",
                "education",
                "skillsAndExperience",
                "healthAndCompliance",
                "submissionDetails",
                "documentAttachments",
              ]);

              setInitialFormData({
                personalDetails: {
                  firstName: "",
                  lastName: "",
                  phoneCountryCode: "",
                  phoneNumber: "",
                  emailAddress: "",
                  currentAddress: "",
                  country: "",
                  city: "",
                  state: "",
                  zipcode: "",
                  relocationWillingness: "",
                  workAuthorizationStatus: "",
                  ssnLast4Digit: "",
                  availableStartDate: "",
                },

                certification: {
                  blsCertification: "",
                  blsExpiration: "",
                  aclsPalsNals: "",
                  aclsPalsNalsExpiration: "",
                  otherRelevantCertificate: "",
                },
                education: [
                  {
                    degree: "",
                    collegeName: "",
                    graduationYear: "",
                  },
                ],

                skillsAndExperience: {
                  totalYearsOfExperience: { month: 0, year: 0 },
                  relevantExperience: { month: 0, year: 0 },
                  otherSkills: "",
                },
                healthAndCompliance: {
                  covid19Status: "",
                  dateOfLastCovid19Dose: "",
                  boosterReceived: "",
                  proofOfVaccinationAvailable: "",
                  fluVaccination: "",
                },
                submissionDetails: {
                  rateExpectation: "",
                  referenceProvider: "",
                  candidateAvailabilityForInterview: "",
                  additionalNote: "",
                },
                documentAttachments: {
                  resume: null,
                  coverLetter: null,
                  licenseCopy: null,
                  blsCertificate: null,
                  aclsPalsNalsCertificate: null,
                  fluVaccinationProof: null,
                  covid19VaccinationProof: null,
                  recentSkillChecklist: null,
                },
              });
            }
            setJob(data.data[0]);
            fetchCandidateData();
          } else if (data?.data) {
            setJob(data.data);
          } else {
            setJob(data);
          }
        })
        .catch(() => setJob(null));
    }
    fetchCandidateData();
  }, [jobId, candidateID]);

  // Always keep candidateID and step in URL
  useEffect(() => {
    setSearchParams((prevParams) => {
      const params = new URLSearchParams(prevParams);
      params.set("step", currentStepIndex);
      if (candidateID) params.set("candidateID", candidateID);
      return params;
    });
  }, [currentStepIndex, candidateID, setSearchParams]);

  const handleValidationChange = useCallback(({ isValid, dirty }) => {
    setIsStepValid(isValid);
    setIsStepDirty(dirty);
  }, []);

  const removeCandidateIDFromURL = () => {
    setSearchParams((prevParams) => {
      const params = new URLSearchParams(prevParams);
      params.delete("candidateID");
      return params;
    });
  };

  const canProceed =
    (candidateID && visitedSteps.includes(currentStepIndex)) ||
    (isStepValid && isStepDirty);

  // Main submit handler for all steps
  const handleStepSubmit = async (stepKey, values, setValues) => {
    try {
      // PERSONAL DETAILS STEP
      if (currentStepIndex === 0) {
        if (!candidateID) {
          const result = await createCandidate({
            ...values.personalDetails,
            phoneNumber: values.personalDetails.phoneNumber
              .split(" ")
              .slice(1)
              .join("")
              ?.replace(/[^a-zA-Z0-9]/g, ""),
          });
          if (result.success) {
            const newID = result?.data?.candidateID;
            dispatch(setCandidateID(newID));
            setSearchParams((prevParams) => {
              const params = new URLSearchParams(prevParams);
              params.set("step", currentStepIndex);
              params.set("candidateID", newID);
              return params;
            });
            toast.success("Candidate created successfully!");
          } else {
            throw new Error(result.message || "Failed to create candidate.");
          }
        } else {
          const updateResult = await updateCandidate(
            candidateID,
            {
              ...values.personalDetails,
              phoneNumber: values.personalDetails.phoneNumber
                .split(" ")
                .slice(1)
                .join("")
                ?.replace(/[^a-zA-Z0-9]/g, ""),
            },
            ""
          );
          if (!updateResult?.success) {
            throw new Error(
              updateResult.message || "Failed to update candidate."
            );
          }
        }
      } else if (candidateID) {
        const updateType =
          stepKey === "licensing"
            ? "licensing"
            : stepKey === "certification"
            ? "certification"
            : stepKey === "education"
            ? "education"
            : stepKey === "workHistory"
            ? "workHistory"
            : stepKey === "skillsAndExperience"
            ? "skillsAndExperience"
            : stepKey === "healthAndCompliance"
            ? "healthAndCompliance"
            : stepKey === "submissionDetails"
            ? "submissionDetails"
            : stepKey === "documentAttachments"
            ? "documentAttachment"
            : "";

        let payload = values[stepKey];

        if (updateType === "education") {
          const filtered = Array.isArray(values.education)
            ? values.education.filter(
                (e) =>
                  e &&
                  typeof e === "object" &&
                  e.degree?.trim() &&
                  e.collegeName?.trim()
                //  &&
                // e.graduationYear?.trim()
              )
            : [];

          payload = filtered;
        }

        if (updateType === "licensing") {
          payload = {
            stateLicense: values.licensing.stateLicenses,
            stateLicenseExpiration: values.licensing.licenseExpireDate,
            isCompactLicense: values.licensing.compactLicense === "yes",
          };
        }

        if (updateType === "certification") {
          payload = {
            ...values.certification,
            blsCertification: values.certification.blsCertification === "yes",
            aclsPalsNals: values.certification.aclsPalsNals === "yes",
          };
        }

        if (updateType === "workHistory") {
          payload = {
            ...values.workHistory,
            supervisorReferenceContact:
              values.workHistory.supervisorReferenceContact,
            professionalReferenceContact1:
              values.workHistory.professionalReferenceContact1,
            professionalReferenceContact2:
              values.workHistory.professionalReferenceContact2,
          };
        }

        if (stepKey === "skillsAndExperience") {
          const { totalYearsOfExperience, relevantExperience, otherSkills } =
            values.skillsAndExperience;

          payload = {
            totalYearsOfExperience,
            relevantExperience,
            otherSkills: Array.isArray(otherSkills)
              ? otherSkills.join(",")
              : otherSkills || "",
          };
        }

        if (stepKey === "submissionDetails") {
          const {
            rateExpectation,
            referenceProvider,
            candidateAvailabilityForInterview,
            additionalNote,
          } = values.submissionDetails;
          payload = {
            rateExpectation,
            referenceProvider,
            candidateAvailabilityForInterview,
            additionalNote,
          };
        }

        if (updateType === "documentAttachment") {
          const form = new FormData();
          Object.entries(values.documentAttachments).forEach(([key, file]) => {
            if (file) form.append(key, file);
          });
          payload = form;
        }

        const updateResult = await updateCandidate(
          candidateID,
          payload,
          updateType
        );

        if (!updateResult?.success) {
          throw new Error(
            updateResult.message || "Failed to update candidate."
          );
        }
      }

      if (!visitedSteps.includes(currentStepIndex)) {
        setVisitedSteps((prev) => [...prev, currentStepIndex]);
      }

      // try {
      //   if (currentStepIndex < steps.length - 1) {
      //     setCurrentStepIndex((prev) => prev + 1);
      //   } else {
      //     toast.success("Candidate saved successfully!");
      //     if (jobId && candidateID) {
      //       try {
      //         if (isInstantSubmit) {
      //           try {
      //             await instantSubmitCandidate(candidateID, jobId);
      //             toast.success("Candidate instantly submitted to job!");
      //             const newCoinBalance = Math.max(
      //               0,
      //               (userInfo?.coinBalance || 0) - 2
      //             );
      //             dispatch(updateUserCoins(newCoinBalance));

      //             dispatch(clearCandidateID());
      //             removeCandidateIDFromURL();

      //             navigate(`/jobs/jobdetails?tab=submissions&jobId=${jobId}`);
      //           } catch (error) {
      //             console.log("instant submit", error);
      //             toast.error(result.message || "Instant submit failed.");
      //           }
      //         } else {
      //           try {
      //             await submitCandidateToJob(candidateID, jobId).then(() => {
      //               toast.success("Candidate submitted to job successfully!");
      //               dispatch(clearCandidateID());
      //               removeCandidateIDFromURL();
      //               navigate(`/jobs/jobdetails?tab=submissions&jobId=${jobId}`);
      //             });
      //           } catch (error) {
      //             console.log("error candidate submit", error);
      //           }
      //         }
      //       } catch (err) {
      //         console.error("Candidate Job Submission API error:", err);
      //         const backendMsg =
      //           err?.response?.data?.message ||
      //           "Failed to submit candidate to job.";
      //         toast.error(backendMsg);
      //       }
      //     } else {
      //       dispatch(clearCandidateID());
      //       removeCandidateIDFromURL();
      //       setTimeout(() => {
      //         navigate("/candidate?tabs=3", { replace: true });
      //       }, 100);
      //     }
      //   }
      // } catch (error) {
      //   console.log("---->>>", error);
      // }

      try {
        if (currentStepIndex < steps.length - 1) {
          setCurrentStepIndex((prev) => prev + 1);
          return;
        }
        if (!jobId || !candidateID) {
          dispatch(clearCandidateID());
          removeCandidateIDFromURL();
          window.location.href = "/candidate?tabs=3";
          // navigate("/candidate?tabs=3", { replace: true });
          return;
        }

        try {
          if (isInstantSubmit) {
            await instantSubmitCandidate(candidateID, jobId);
            toast.success("Candidate instantly submitted to job!");

            const newCoinBalance = Math.max(
              0,
              (userInfo?.coinBalance || 0) - 2
            );
            dispatch(updateUserCoins(newCoinBalance));
          } else {
            await submitCandidateToJob(candidateID, jobId);
            toast.success("Candidate submitted to job successfully!");
          }

          dispatch(clearCandidateID());
          removeCandidateIDFromURL();
          window.location.href = `/jobs/jobdetails?tab=submissions&jobId=${jobId}`;
        } catch (err) {
          console.error("Candidate Job Submission API error:", err);
          const backendMsg =
            err?.response?.data?.message || "Submission failed.";
          toast.error(backendMsg);
        }
      } catch (error) {
        console.error("Unexpected error:", error);
      }
    } catch (error) {
      if (error.message?.includes("409") || error?.response?.status === 409) {
        toast.error(
          "Candidate already exists with this email or phone number."
        );
      } else {
        toast.error("Failed to save data.");
        console.error("Error submitting step:", error);
        dispatch(clearCandidateID());
        removeCandidateIDFromURL();
      }
    }
  };

  const StepIcon = stepIcons[currentStepIndex];

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex((prev) => prev - 1);
    } else {
      toast.error("Already at the first step");
    }
  };

  // Unified step rendering
  const renderStepForm = (formik) => {
    const stepKey = stepKeys[currentStepIndex];
    const commonProps = {
      onValidationChange: handleValidationChange,
      setSubmitRef: (submitForm) => {
        formikSubmitRef.current = async () => {
          await formik.validateForm();
          if (formik.isValid) {
            await handleStepSubmit(stepKey, formik.values, formik.setValues);
          }
        };
      },
    };
    if (job?.jobType == "full-time") {
      switch (currentStepIndex) {
        case 0:
          return <PersonalDetailsForm {...commonProps} />;
        case 1:
          return <CertificationsForm {...commonProps} />;
        case 2:
          return <EducationForm {...commonProps} />;
        case 3:
          return <SkillExperienceForm {...commonProps} />;
        case 4:
          return <HealthComplianceForm {...commonProps} />;
        case 5:
          return <SubmissionDetailsForm {...commonProps} />;
        case 6:
          return <DocumentAttachmentForm {...commonProps} />;
        default:
          return null;
      }
    } else {
      switch (currentStepIndex) {
        case 0:
          return <PersonalDetailsForm {...commonProps} />;
        case 1:
          return <LicensingForm {...commonProps} />;
        case 2:
          return <CertificationsForm {...commonProps} />;
        case 3:
          return <EducationForm {...commonProps} />;
        case 4:
          return <WorkHistoryForm {...commonProps} />;
        case 5:
          return <SkillExperienceForm {...commonProps} />;
        case 6:
          return <HealthComplianceForm {...commonProps} />;
        case 7:
          return <SubmissionDetailsForm {...commonProps} />;
        case 8:
          return <DocumentAttachmentForm {...commonProps} />;
        default:
          return null;
      }
    }
  };

  return (
    <>
      <Formik
        innerRef={formikRef}
        enableReinitialize
        initialValues={initialFormData}
        validate={validate}
        validateOnChange={true}
        validateOnBlur={true}
        onSubmit={() => {}}
      >
        {(formik) => (
          <div className="p-4">
            {jobId && job && (
              <div className="mb-6">
                <JobOverviewCard job={job} />
              </div>
            )}
            <div className="flex gap-8">
              <div className="w-1/3 min-w-[280px]">
                <FormStepper
                  currentStepIndex={currentStepIndex}
                  setCurrentStepIndex={setCurrentStepIndex}
                  visitedSteps={visitedSteps}
                  steps={steps}
                />
              </div>
              <div className="flex-1">
                <h2 className="mb-2">
                  <div className="flex items-center gap-2">
                    <StepIcon size={20} className="text-[#27364B]" />
                    <span className="text-[#27364B] text-[16px] font-semibold">
                      {steps[currentStepIndex]}
                    </span>
                  </div>
                </h2>
                <hr className="border-t border-gray-300 mb-6" />
                {renderStepForm(formik)}

                <div className="flex justify-between mt-8">
                  <AppButton
                    label="Back"
                    variant="secondary"
                    onClick={handleBack}
                    disabled={currentStepIndex === 0}
                    style={{ opacity: currentStepIndex === 0 ? 0.5 : 1 }}
                  />
                  <div className="flex items-center gap-x-8">
                    {(currentStepIndex === 1 || currentStepIndex === 4) &&
                      !isJobSubmission && (
                        <button
                          type="button"
                          className="text-gray-500 font-medium cursor-pointer"
                          onClick={() => {
                            setCurrentStepIndex(currentStepIndex + 1);
                            formik.resetForm();
                          }}
                        >
                          Skip
                        </button>
                      )}
                    <AppButton
                      label={
                        currentStepIndex === steps.length - 1
                          ? "Save"
                          : "Save & Next"
                      }
                      variant="primary"
                      onClick={() => {
                        if (formikSubmitRef.current) {
                          formikSubmitRef.current();
                        }
                      }}
                      disabled={!canProceed}
                      style={{
                        background: "#3E9900",
                        opacity: canProceed ? 1 : 0.5,
                        cursor: canProceed ? "pointer" : "not-allowed",
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Formik>
    </>
  );
};

export default AddCandidates;
