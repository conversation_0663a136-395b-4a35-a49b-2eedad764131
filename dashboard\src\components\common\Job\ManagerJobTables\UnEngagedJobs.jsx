import React, { useEffect, useState, useRef } from "react";
import PaginationFooter from "../../Table/TableFooter";
import ThreeDot from "../../Button/ThreeDot";
import TableHeader from "../../Table/TableHeader";
import EmptyState from "../../EmptyState";
import { useNavigate, useLocation } from "react-router-dom";

import { useSelector } from "react-redux";

const UnEngagedJobs = ({
  getJob,
  filters = {},
  sorting = "",
  search = {},
  jobs,
  paginationProps,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const userInfo = useSelector((state) => state.auth.user);

  // Refs to track previous values and prevent unnecessary calls
  const prevFiltersRef = useRef("");
  const prevSortingRef = useRef("");
  const prevSearchRef = useRef("");
  const isInitialMount = useRef(true);
  const fetchTimeoutRef = useRef(null);

  const columns = [
    "Job ID",
    "Job Title",
    "Location",
    "Job Type",
    "Recruiter Request Count",
    "Request",
    "",
  ];

  // URL query params
  const queryParams = new URLSearchParams(location.search);
  const initialPage = parseInt(queryParams.get("page")) || 1;
  const initialLimit = parseInt(queryParams.get("limit")) || 10;

  const [pageSize, setPageSize] = useState(initialLimit);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [allJobs, setAllJobs] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);

  // If jobs are passed from parent (HeadAccountManager pattern), use them
  useEffect(() => {
    if (jobs && Array.isArray(jobs)) {
      setAllJobs(jobs);
      setLoading(false);

      if (paginationProps) {
        setTotalResults(paginationProps.totalResults || 0);
        setTotalPages(paginationProps.totalPages || 1);
        setCurrentPage(paginationProps.currentPage || 1);
        setPageSize(paginationProps.pageSize || 10);
      }
      return;
    }
  }, [jobs, paginationProps]);

  // Update URL query params
  const updateQueryParams = (page, limit) => {
    // Only update URL if not using parent-managed data
    if (jobs && Array.isArray(jobs)) return;

    const params = new URLSearchParams(location.search);
    params.set("page", page);
    params.set("limit", limit);
    navigate({ search: params.toString() }, { replace: true });
  };

  // Function to fetch data with debouncing
  const fetchData = async (
    page,
    size,
    currentFilters,
    currentSorting,
    currentSearch
  ) => {
    if (!getJob) return;

    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Debounce the API call
    fetchTimeoutRef.current = setTimeout(async () => {
      setLoading(true);
      try {
        const res = await getJob(
          page,
          size,
          currentFilters,
          currentSorting,
          currentSearch
        );

        setAllJobs(res?.results || []);
        setTotalResults(res?.total || 0);
        setTotalPages(res?.totalPages || 1);
      } catch (err) {
        console.error("Error fetching unengaged jobs:", err);
        setAllJobs([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    }, 300);
  };

  // Effect for pagination changes
  useEffect(() => {
    // Don't fetch if jobs are provided by parent
    if (jobs && Array.isArray(jobs)) return;

    fetchData(currentPage, pageSize, filters, sorting, search);
  }, [currentPage, pageSize]);

  // Effect for filters, sorting, search changes
  useEffect(() => {
    // Don't fetch if jobs are provided by parent
    if (jobs && Array.isArray(jobs)) return;

    const filtersStr = JSON.stringify(filters);
    const searchStr = JSON.stringify(search);

    // Check if values actually changed
    const filtersChanged = filtersStr !== prevFiltersRef.current;
    const sortingChanged = sorting !== prevSortingRef.current;
    const searchChanged = searchStr !== prevSearchRef.current;

    // Skip if nothing changed and not initial mount
    if (
      !isInitialMount.current &&
      !filtersChanged &&
      !sortingChanged &&
      !searchChanged
    ) {
      return;
    }

    // Update refs
    prevFiltersRef.current = filtersStr;
    prevSortingRef.current = sorting;
    prevSearchRef.current = searchStr;

    // Reset to page 1 when filters change (but not on initial mount)
    if (
      !isInitialMount.current &&
      (filtersChanged || sortingChanged || searchChanged)
    ) {
      setCurrentPage(1);
      updateQueryParams(1, pageSize);
      fetchData(1, pageSize, filters, sorting, search);
    } else if (isInitialMount.current) {
      // Initial fetch
      fetchData(currentPage, pageSize, filters, sorting, search);
      isInitialMount.current = false;
    }
  }, [filters, sorting, search, pageSize, getJob, jobs]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  const handlePageChange = (page) => {
    if (paginationProps && paginationProps.setCurrentPage) {
      paginationProps.setCurrentPage(page);
    } else {
      setCurrentPage(page);
      updateQueryParams(page, pageSize);
    }
  };

  const handlePageSizeChange = (limit) => {
    if (paginationProps && paginationProps.setPageSize) {
      paginationProps.setPageSize(limit);
    } else {
      setPageSize(limit);
      setCurrentPage(1);
      updateQueryParams(1, limit);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <span className="ml-2 text-gray-600">Loading unengaged jobs...</span>
      </div>
    );
  }

  // Check if filters are applied
  const hasActiveFilters = Object.values(filters).some((filterValue) =>
    Array.isArray(filterValue) ? filterValue.length > 0 : !!filterValue
  );

  const hasActiveSearch =
    search && search.searchTerm && search.searchTerm.trim();

  return (
    <div className="flex flex-col mb-16">
      {allJobs.length > 0 ? (
        <div className="flex flex-col">
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <TableHeader columns={columns} />
              <tbody>
                {allJobs?.map((job, index) => (
                  <tr
                    key={job.jobid || index}
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(
                        `/jobs/assignrecruiter?jobID=${job?.jobid}&tab=Assign+Recruiter`
                      );
                    }}
                    className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                  >
                    {Object.keys(job)?.map((item, itemIndex) => {
                      return (
                        <td key={itemIndex} className="px-4 py-3 text-left">
                          {job[item]}
                        </td>
                      );
                    })}
                    <td
                      className="px-4 py-3 text-left underline text-[#4D82F3] cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        navigate(
                          `/jobs/assignrecruiter?jobID=${job?.jobid}&tab=Assign+Recruiter`
                        );
                      }}
                    >
                      Request to work
                    </td>
                    <td className="px-4 py-3 text-start">
                      <ThreeDot
                        dropdownSize="w-32"
                        buttonDropDown={
                          <>
                            <ul className="py-0" role="none">
                              {userInfo?.role == "accountManager" ? (
                                ""
                              ) : (
                                <li>
                                  <div
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      navigate(
                                        `/jobs/editjob?jobID=${job?.jobid}`
                                      );
                                    }}
                                    className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                                    role="menuitem"
                                  >
                                    <img
                                      src={"/assets/icons/edit.svg"}
                                      alt="Edit Job"
                                    />
                                    Edit Job
                                  </div>
                                </li>
                              )}
                              <li>
                                <div
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    navigate(
                                      `/jobs/assignrecruiter?jobID=${job.jobid}&tab=Assign+Recruiter`
                                    );
                                  }}
                                  className="flex cursor-pointer items-center justify-start  gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                                  role="menuitem"
                                >
                                  <img
                                    src="/assets/icons/view.svg"
                                    alt="View Job"
                                  />
                                  View Job
                                </div>
                              </li>
                            </ul>
                          </>
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="bg-white">
            <PaginationFooter
              currentPage={paginationProps?.currentPage || currentPage}
              totalPages={paginationProps?.totalPages || totalPages}
              pageSize={paginationProps?.pageSize || pageSize}
              setPageSize={paginationProps?.setPageSize || handlePageSizeChange}
              setCurrentPage={
                paginationProps?.setCurrentPage || handlePageChange
              }
              totalResults={paginationProps?.totalResults || totalResults}
            />
          </div>
        </div>
      ) : (
        <EmptyState
          title={
            hasActiveFilters || hasActiveSearch
              ? "No Jobs Match Your Criteria"
              : "No Unengaged Jobs Found"
          }
          description={
            hasActiveFilters || hasActiveSearch
              ? "Try adjusting your filters or search terms to see more results."
              : "There are no unengaged jobs at the moment. Jobs that need recruiter engagement will appear here."
          }
        />
      )}
    </div>
  );
};
export default UnEngagedJobs;
