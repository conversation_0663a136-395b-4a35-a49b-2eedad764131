import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { getIn } from "formik";

const CalendarDropDownField = ({
  name,
  label,
  required,
  placeholder,
  errorMessage,
  minDate,
  maxDate,
  width = "w-[325px]",
  showTimeSelect = false,
  values,
  onChange,
}) => {
  const selectedDate = getIn(values) ? new Date(getIn(values)) : null;

  return (
    <div className="relative w-full">
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <DatePicker
        id={name}
        selected={selectedDate}
        onChange={onChange}
        placeholderText={placeholder || "YYYY-MM-DD"}
        className={`${width} border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500`}
        dateFormat={showTimeSelect ? "yyyy-MM-dd h:mm aa" : "yyyy-MM-dd"}
        minDate={minDate}
        maxDate={maxDate}
        showTimeSelect={showTimeSelect}
        timeIntervals={30}
        popperPlacement="bottom-end"
        calendarClassName="text-sm"
      />

      {errorMessage}
    </div>
  );
};

export default CalendarDropDownField;
