import { useEffect, useState } from "react";
import StatsCard from "../../components/common/Dashboard/StatsCard";
import ContainerCard from "../../components/common/Dashboard/ContainerCard";
import ListCard from "../../components/common/Dashboard/ListCard";
import RadialBarChart from "../../components/common/Charts/RadialBarChart";
import PieDonutChart from "../../components/common/Charts/PieDonutChart";
import { useNavigate } from "react-router";
import { getHeadAccountDasboard } from "../../services/operations/dashboardAPI";
import { getRelativeTime } from "../../utils/RelativeTimeFormatter";
import EmptyState from "../../components/common/EmptyState";

const Dashboard = () => {
  const navigate = useNavigate();
  const [data, setData] = useState(null);
  async function callAPI() {
    const res = await getHeadAccountDasboard();
    setData(res.data);
  }

  useEffect(() => {
    callAPI();
  }, []);

  function ObjectCount(countArray = [], objs) {
    return objs
      ? Object.keys(objs || {})
          .filter((item) => countArray.includes(item))
          .reduce((sum, key) => sum + (objs[key] || 0), 0)
      : 0;
  }

  console.log(data);

  return (
    <>
      <div className="flex gap-3">
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          onClick={() => {
            navigate("/submissions");
          }}
          lable="All Submission"
          value={data?.submissionCount || 0}
        />
        <StatsCard
          imageBackground="bg-[#D1E9FF]"
          image="/assets/icons/file_color_blue.svg"
          onClick={() => {
            navigate("/jobs?tabs=2");
          }}
          lable="Active Jobs"
          value={data?.Active || 0}
        />
        <StatsCard
          imageBackground="bg-[#FEE4E2]"
          image="/assets/icons/file_color_red.svg"
          onClick={() => {
            navigate("/jobs?tabs=1");
          }}
          lable="High Priority"
          value={data ? data["high priority"] || 0 : 0}
        />
        <StatsCard
          imageBackground="bg-[#D1FADF]"
          image="/assets/icons/file_color_green.svg"
          onClick={() => {
            navigate("/recruiter");
          }}
          lable="Total Recruiter"
          value={data?.recruiterCount || 0}
        />
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          onClick={() => {
            navigate("/manager");
          }}
          lable="Total Managers"
          value={data?.accountManagerCount || 0}
        />
      </div>

      <div className="mt-2 flex gap-3">
        <ContainerCard
          width="flex-grow basis-1/2 "
          label="Recent Submission"
          childrenHeight=" h-80 overflow-auto"
          children={
            <>
              {data?.recentSubmissions && data.recentSubmissions.length > 0 ? (
                data.recentSubmissions.map((item) => {
                  return (
                    <ListCard
                      onClick={() => {
                        navigate(
                          `/submissions/submissionDetails?candidateId=${item?.candidate?.candidateID}&submissionId=${item?.submissionId}`
                        );
                      }}
                      time={getRelativeTime(item?.submittedAt)}
                      value={`${item?.job?.jobId}-${item?.job?.jobTitle}`}
                      varient="update"
                    />
                  );
                })
              ) : (
                <EmptyState title="No Recent Submissions" />
              )}
            </>
          }
        />
        <ContainerCard
          width="flex-grow basis-1/2"
          label="Submission per jobs"
          childrenHeight=" h-80 overflow-auto"
          children={
            <>
              {data?.jobPerSubmittion && data.jobPerSubmittion.length > 0 ? (
                data.jobPerSubmittion.map((item) => {
                  return (
                    <ListCard
                      onClick={() => {
                        navigate(
                          `/jobs/jobdetails?tab=submissions&jobId=${item?.jobId}`
                        );
                      }}
                      count={item?.submissionCount}
                      value={`${item?.jobId} - ${item?.jobTitle}`}
                      varient="submissioncount"
                    />
                  );
                })
              ) : (
                <EmptyState title="No Submission Data" />
              )}
            </>
          }
        />
      </div>

      <div className="mt-2 flex gap-3">
        <ContainerCard
          onChange={(e) => {}}
          value="This Month"
          isDropDown={false}
          label="Submission Status"
          width="flex-grow-[1.5] basis-4/6"
          children={
            <>
              {data?.submission &&
              Object.values(data.submission).some((val) => val > 0) ? (
                <RadialBarChart
                  colorCode={[
                    "#7A5AF8",
                    "#717680",
                    "#00BBA7",
                    "#2E90FA",
                    "#F79009",
                    "#F04438",
                    "#6172F3",
                    "#7A5AF8",
                    "#EE46BC",
                    "#F63D68",
                    "#FB6514",
                    "#00A6F4",
                  ]}
                  // isCountVisible={false}
                  width={300}
                  height={300}
                  series={[
                    data?.submission?.submitted || 0,
                    data?.submission ? data?.submission["reviewing"] || 0 : 0,
                    data?.submission ? data?.submission["selected"] || 0 : 0,
                    data?.submission?.interviewing || 0,
                    data?.submission
                      ? data?.submission["offer released"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["offer rejected"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["guarantee period not completed"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["guarantee period completed"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["submitted to client"] || 0
                      : 0,
                    data?.submission ? data?.submission["rejected"] || 0 : 0,
                    data?.submission
                      ? data?.submission["awaiting offer"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["offer accepted"] || 0
                      : 0,
                    data?.submission
                      ? data?.submission["hired-under guarantee period"] || 0
                      : 0,
                  ]}
                  labelNames={[
                    "Submitted",
                    "reviewing",
                    "Selected",
                    "Interviewing",
                    "Offer Released",
                    "Offer Rejected",
                    "Guarantee period not completed",
                    "Guarantee period completed",
                    "Submitted to client",
                    "Rejected",
                    "Awaiting Offer",
                    "Offer Accepted",
                    "Hired -UGP",
                  ]}
                />
              ) : (
                <EmptyState title="No Submission Status Data" />
              )}
            </>
          }
        />
        <ContainerCard
          onChange={(e) => {}}
          value="This Month"
          isDropDown={false}
          label="Job Status"
          width="flex-grow basis-2/4"
          children={
            <>
              {data &&
              [
                data?.Active,
                data?.Inactive,
                data?.Onhold,
                data?.Holdbyclient,
                data?.Filled,
                data?.Cancelled,
                data?.Closed,
              ].some((val) => val > 0) ? (
                <div className="mt-10">
                  <PieDonutChart
                    series={[
                      data?.Active || 0,
                      data?.Inactive || 0,
                      data?.Onhold || 0,
                      data?.Holdbyclient || 0,
                      data?.Filled || 0,
                      data?.Cancelled || 0,
                      data?.Closed || 0,
                    ]}
                    colors={[
                      "#4C78FF",
                      "#16DBCC",
                      "#FF82AC",
                      "#F46E6E",
                      "#FFBB38",
                      "#26C6DA",
                      "#5C6BC0",
                    ]}
                    labels={[
                      "Active",
                      "Inactive",
                      "On Hold",
                      "Filled",
                      "Cancelled",
                      "Closed",
                      "Hold by Client",
                    ]}
                  />
                </div>
              ) : (
                <div className="mt-10">
                  <EmptyState title="No Job Status Data" />
                </div>
              )}
            </>
          }
        />
      </div>
    </>
  );
};

export default Dashboard;
