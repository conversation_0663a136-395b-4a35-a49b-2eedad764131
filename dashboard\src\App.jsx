import React, { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import route from "./routes";
import ProtectedRoute from "./ProtectedRoute";
import PublicRoute from "./PublicRoute";
import { useSelector } from "react-redux";
import { ToastContainer, Slide } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Loader from "./components/common/Loader/Loader";

// Lazy-load your public pages
const Signup = React.lazy(() => import("./pages/Recruiter/Signup"));
const SignUpStep2 = React.lazy(() => import("./pages/Recruiter/SignUpStep2"));
const ThankYouForRegistration = React.lazy(() =>
  import("./pages/Recruiter/ThankYouForRegistration")
);
const Login = React.lazy(() => import("./pages/Recruiter/Login"));
const Profile = React.lazy(() => import("./pages/Profile"));
const EmailVerificationPage = React.lazy(() =>
  import("./pages/EmailVerificationPage")
);
const EmailVerifiedPage = React.lazy(() => import("./pages/EmailVerifiedPage"));
const VerifyEmailPage = React.lazy(() => import("./pages/VerifyEmailPage"));
const VerificationFailedPage = React.lazy(() =>
  import("./pages/VerificationFailedPage")
);
const ForgotPassword = React.lazy(() =>
  import("./components/core/recruiter/login/ForgotPassword")
);
const NewPassword = React.lazy(() =>
  import("./components/core/recruiter/login/NewPassword")
);
const PassUpdated = React.lazy(() =>
  import("./components/core/recruiter/login/PassUpdated")
);

const App = () => {
  const userInfo = useSelector((state) => state.auth?.user)?.role || "";
  const isLoading = useSelector((state) => state.loader?.isLoading);

  return (
    <Suspense fallback={<Loader isloading={true} />}>
      <Routes>
        <Route element={<PublicRoute />}>
          <Route path="/signup">
            <Route index element={<Signup />} />
            <Route path="personal-info" element={<SignUpStep2 />} />
            <Route path="thank-you" element={<ThankYouForRegistration />} />
            <Route path="verify-email" element={<EmailVerificationPage />} />
          </Route>

          <Route path="/login" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<NewPassword />} />
          <Route path="/password-updated" element={<PassUpdated />} />
          <Route path="/verify-email" element={<VerifyEmailPage />} />
          <Route path="/email-verified" element={<EmailVerifiedPage />} />
          <Route
            path="/verification-failed"
            element={<VerificationFailedPage />}
          />
        </Route>

        <Route path="/" element={<ProtectedRoute />} loader={true}>
          {route[userInfo]?.map((item) => (
            <React.Fragment key={item.url}>
              <Route
                path={item.url}
                element={
                  <Suspense fallback={<Loader isloading={true} />}>
                    {item.element}
                  </Suspense>
                }
              />
              {item.nestedRoutes?.map((nestedItem) => (
                <Route
                  key={nestedItem.url}
                  path={nestedItem.url}
                  element={
                    <Suspense fallback={<Loader isloading={true} />}>
                      {nestedItem.element}
                    </Suspense>
                  }
                />
              ))}
            </React.Fragment>
          ))}

          {/* Static route for user profile */}
          <Route path="/user-profile" element={<Profile />} />

          {/* Catch-all fallback */}
          <Route path="*" element={<></>} />
        </Route>
      </Routes>

      <ToastContainer
        position="top-center"
        autoClose={3000}
        transition={Slide}
        className="toast-container-top-center"
        closeOnClick
        hideProgressBar={false}
        pauseOnHover
        draggable
        toastClassName="custom-toast"
      />

      <Loader isloading={isLoading} />
    </Suspense>
  );
};

export default App;
