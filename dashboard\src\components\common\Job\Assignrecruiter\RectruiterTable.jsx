import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Check<PERSON>Field from "../../Input/CheckBoxField";
import ThreeDot from "../../Button/ThreeDot";
import PaginationFooter from "../../Table/TableFooter";
import TableHeader from "../../Table/TableHeader";
import ColorSelectionInput from "../../Input/ColorSelectionInput";

const RecruiterTable = ({
  recruiterData,
  assignRecruiter,
  removeRecruiter,
  selectedRecruiters,
  setSelectedRecruiters,
  setRecruiters,
}) => {
  const navigate = useNavigate();
  // const [pageSize, setPageSize] = useState(10);
  // const [currentPage, setCurrentPage] = useState(1);

  // const totalResults = recruiterData.length;
  // const totalPages = Math.ceil(totalResults / pageSize);
  const columns = [
    "",
    "Recruiter ID",
    "Recruiter Name",
    "Specialization",
    "Jobs Working On",
    "Total Submissions",
    "Status",
    "",
  ];

  // Pagination logic (simple slice, replace with real logic if needed)
  // const paginatedData = recruiterData.slice(
  //   (currentPage - 1) * pageSize,
  //   currentPage * pageSize
  // );

  const paginatedData = recruiterData;

  const handleCheckboxChange = (recruiterId) => (e) => {
    setSelectedRecruiters((prev) => ({
      ...prev,
      [recruiterId]: e.target.checked,
    }));
  };

  return (
    <>
      <table className="min-w-full bg-white">
        <TableHeader columns={columns} />
        <tbody>
          {paginatedData.map((rec, i) => (
            <tr
              key={i}
              className="border-b border-[#E2E8F0] hover:bg-gray-50 text-sm text-gray-800"
            >
              <td className="px-4 py-3 flex justify-center">
                {!rec.status && (
                  <CheckboxField
                    checked={!!selectedRecruiters[rec.userID]}
                    onChange={handleCheckboxChange(rec.userID)}
                  />
                )}
              </td>
              {Object.keys(rec)?.map((item) => {
                if (item == "_id") {
                  return;
                }
                return (
                  <td className="px-2 py-3">
                    {item == "status" ? (
                      <>
                        {rec[item] ? (
                          <ColorSelectionInput
                            value={rec[item]}
                            width={"w-42"}
                            disabled={
                              [
                                "revokeRequest",
                                "rejected",
                                "accepted",
                              ].includes(rec[item])
                                ? true
                                : false
                            }
                            onChange={(newStatus) => {
                              if (newStatus == "revokeRequest") {
                                removeRecruiter(rec.userID);
                              }
                            }}
                            options={
                              ["requestToWork"].includes(rec[item])
                                ? [
                                    {
                                      label: "Request Sent",
                                      value: "requestToWork",
                                      color: "text-[#EFB008] bg-[#FFF5D5]",
                                    },
                                    {
                                      label: "Revoke Request",
                                      value: "revokeRequest",
                                      color: "text-[#00B8DB] bg-[#ECFEFF]",
                                    },
                                  ]
                                : [
                                    {
                                      label: "Accepted",
                                      value: "accepted",
                                      color: "text-[#12B76A] bg-[#ECFDF3]",
                                    },
                                    {
                                      label: "Revoke Request",
                                      value: "revokeRequest",
                                      color: "text-[#00B8DB] bg-[#ECFEFF]",
                                    },
                                    {
                                      label: "Rejected",
                                      value: "rejected",
                                      color: "text-[#F04438] bg-[#FEF3F2]",
                                    },
                                  ]
                            }
                          />
                        ) : (
                          <span
                            className="underline text-[#4D82F3] cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              assignRecruiter({ [rec.userID]: true });
                            }}
                          >
                            Request to work
                          </span>
                        )}
                      </>
                    ) : (
                      rec[item]
                    )}
                  </td>
                );
              })}
              <td className="px-4 py-3 text-start">
                <ThreeDot
                  dropdownSize="w-32"
                  buttonDropDown={
                    <>
                      <ul className="py-0" role="none">
                        <li>
                          <div
                            onClick={(e) => {
                              e.preventDefault();
                            }}
                            className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-dark700 hover:bg-gray-100 :text-gray-300 "
                            role="menuitem"
                          >
                            <img src="/assets/icons/view.svg" alt="Edit Job" />
                            View
                          </div>
                        </li>
                      </ul>
                    </>
                  }
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* <PaginationFooter
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        totalResults={totalResults}
      /> */}
    </>
  );
};

export default RecruiterTable;
