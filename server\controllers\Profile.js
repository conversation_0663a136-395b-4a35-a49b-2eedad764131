const recruiterProfile = require("../models/RecruiterProfile.models");
const User = require("../models/User.models");
const { uploadToS3 } = require("../configs/s3Config");
const { sendUserDetailsEmail } = require("../utils/mailSender");
const mailSender = require("../utils/mailSender");
const {
  recruiterDetailTemplate,
} = require("../templates/email/RecruiterDetails");
const { v4: uuidv4 } = require("uuid");
const { UserVerificationToken } = require("../models/userVerificationToken");

//* @desc Update user profile
//* @route PUT /api/v1/user/update-profile
//* @access Private
exports.updateProfile = async (req, res) => {
  try {
    const { userID } = req.params;

    if (!userID) {
      return res
        .status(400)
        .json({ success: false, message: "userId is required." });
    }

    const {
      country,
      state,
      domain,
      candidateRole,
      about,
      phoneNo,
      phoneCountryCode,
      linkedin,
      fullname,
      isSignup = false,
    } = req.body;

    if (isSignup) {
      if (
        !country ||
        !state ||
        !domain ||
        domain.length <= 0 ||
        !candidateRole ||
        candidateRole.length <= 0 ||
        !about
      ) {
        return res.status(400).json({
          success: false,
          message: "Please fill all the required fields",
        });
      }
    } else {
      // Basic input validation
      if (
        !country ||
        !state ||
        !domain ||
        !phoneCountryCode ||
        !phoneNo ||
        !linkedin ||
        !fullname ||
        domain.length <= 0 ||
        !candidateRole ||
        candidateRole.length <= 0 ||
        !about
      ) {
        return res.status(400).json({
          success: false,
          message: "Please fill all the required fields",
        });
      }
    }

    // LinkedIn URL validation
    const validLinkedInPatterns = [
      "https://www.linkedin.com/in/",
      "https://linkedin.com/in/",
    ];

    const isValidLinkedInUrl = validLinkedInPatterns?.some((pattern) =>
      linkedin?.startsWith(pattern)
    );

    if (!isValidLinkedInUrl && !isSignup) {
      return res.status(400).json({
        success: false,
        message:
          "Please provide a valid LinkedIn URL starting with https://linkedin.com/in/ or https://www.linkedin.com/in/",
      });
    }

    // Phone number validation
    if (!/^\d{8,15}$/.test(phoneNo) && !isSignup) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid phone number",
      });
    }

    const nameArray = fullname?.split(" ");
    const firstName = nameArray ? nameArray[0] || "" : "";
    const lastName =
      nameArray?.length > 1 ? nameArray[nameArray?.length - 1] : "";
    const middleName =
      nameArray?.length >= 2 ? nameArray?.slice(1, -1)?.join(" ") : "";

    const userProfile = await recruiterProfile.findOne({
      "user.userId": userID,
    });
    const user = await User.findOne({ userId: userID });

    if (!userProfile || !user) {
      return res.status(400).json({
        success: false,
        message: `Profile or user not found for user ID: ${userID}`,
      });
    }

    // Handle file upload if present
    let resumeUrl = ""; // Keep existing resume URL if no new file
    if (req.file && req.file.buffer) {
      try {
        // Upload file to S3
        resumeUrl = await uploadToS3(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype,
          "resume"
        );
      } catch (uploadError) {
        console.error("Error uploading file:", uploadError);
        return res.status(500).json({
          success: false,
          message: "Failed to upload resume",
        });
      }
    }

    if (!isSignup) {
      // Update profile fields
      const profileUpdate = await recruiterProfile.findOneAndUpdate(
        { "user.userId": userID },
        {
          country,
          state,
          domain: JSON.parse(domain),
          candidateRole: JSON.parse(candidateRole),
          about,
          resume: resumeUrl,
          linkedin: linkedin,
        },
        {
          new: true,
        }
      );
      const userUpdate = await User.findOneAndUpdate(
        { userId: userID },
        {
          phone: {
            countryCode: phoneCountryCode,
            number: phoneNo,
          },
          name: {
            firstName: firstName,
            middleName: middleName,
            lastName: lastName,
          },
        },
        {
          new: true,
        }
      );
      if (!profileUpdate || !userUpdate) {
        return res.status(400).json({
          success: false,
          message: `Profile or user not found for user ID: ${userID}`,
        });
      }

      const userInfo = {
        firstName: userUpdate.name.firstName,
        middleName: userUpdate.name.middleName,
        lastName: userUpdate.name.lastName,
        email: userUpdate.email,
        phoneNo: userUpdate.phone,
        profile: profileUpdate,
        id: userUpdate._id,
        userID: user.userId,
        role: userUpdate.userType,
      };

      return res.status(200).json({
        success: true,
        message: "Profile updated successfully",
        data: userInfo,
      });
    } else {
      // Update profile fields
      const profileUpdate = await recruiterProfile.findOneAndUpdate(
        { "user.userId": userID },
        {
          country,
          state,
          domain: JSON.parse(domain),
          candidateRole: JSON.parse(candidateRole),
          about,
          resume: resumeUrl,
        },
        {
          new: true,
        }
      );
      if (!profileUpdate) {
        return res.status(400).json({
          success: false,
          message: `Profile or user not found for user ID: ${userID}`,
        });
      }

      if (!user.emailVerified) {
        const verificationToken = uuidv4();
        await UserVerificationToken.create({
          userId: user._id,
          token: verificationToken,
          type: "emailVerification",
        });
        const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
        const verificationEmailBody = `
          <p>Dear ${firstName},</p>
          <p>Thank you for registering on Hirring.com!</p>
          <p>Please verify your email address by clicking the link below:</p>
          <p>
            <a href="${verificationUrl}" style="color: #2563eb; text-decoration: underline;">
              Verify Email
            </a>
          </p>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not sign up, please ignore this email.</p>
          <p>Regards,<br/>The Support Team</p>
        `;

        await mailSender(
          user.email,
          "Hirring.com Email Verification",
          verificationEmailBody
        );
      }

      return res.status(200).json({
        success: true,
        message: "Profile updated successfully.Verification email sent.",
      });
    }
  } catch (error) {
    console.error("Error updating profile:", error);

    // Handle specific MongoDB errors
    if (error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

exports.resendVerification = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res
        .status(400)
        .json({ success: false, message: "userId is required" });
    }

    const user = await User.findOne({ userId });
    if (!user) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    if (user.emailVerified) {
      return res
        .status(400)
        .json({ success: false, message: "Email is already verified" });
    }

    await UserVerificationToken.findOneAndDelete({ userId: user._id });

    const token = uuidv4();

    await UserVerificationToken.create({
      userId: user._id,
      token,
      type: "emailVerification",
    });

    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    const emailContent = `
      <p>Dear ${user?.name?.firstName || "User"},</p>
          <p>Thank you for registering on Hirring.com!</p>
          <p>Please verify your email address by clicking the link below:</p>
          <p>
            <a href="${verificationUrl}" style="color: #2563eb; text-decoration: underline;">
              Verify Email
            </a>
          </p>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not sign up, please ignore this email.</p>
          <p>Regards,<br/>The Support Team</p>
        `;

    await mailSender(user.email, "Verify Your Email", emailContent);

    return res.status(200).json({
      success: true,
      message: "Verification email sent successfully",
    });
  } catch (error) {
    console.error("Resend verification error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};
