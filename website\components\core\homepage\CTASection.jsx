import background from "@/public/assets/images/homepage/cta-background.webp";

const CTASection = () => {
  return (
    <section
      className="w-full flex flex-col items-center justify-center py-24 px-6 gap-5 bg-fill bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(${background.src})`,
      }}
    >
      <h2 className="text-white text-4xl lg:text-5xl font-bold text-center">
        Get started today
      </h2>
      <p className="text-white text-md lg:text-lg font-light text-center">
        Ready to take control of your recruitment business? Join HIRRING.COM
        today and start earning high commissions <br /> with the best job
        opportunities around the globe.
      </p>
      <a href=" https://recruiter.hirring.com/user/signup">
        <button className="bg-[#F3FFEB] text-[#163501] px-4 py-2 rounded-md font-semibold cursor-pointer">
          Sign Up Now
        </button>
      </a>
    </section>
  );
};

export default CTASection;
