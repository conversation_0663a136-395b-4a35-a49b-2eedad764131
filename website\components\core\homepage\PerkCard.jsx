import Image from "next/image";

const PerkCard = ({ icon, title, description, size }) => {
  return (
    <div
      className={`${
        size === "large" ? "w-[32rem]" : "w-[21rem]"
      } rounded-2xl bg-white flex flex-col  justify-center gap-[1.25rem] py-11 px-7`}
      style={{
        boxShadow:
          size === "large" ? "9px 9px 200px 0px rgba(0,0,0,0.26)" : "none",
      }}
    >
      <Image src={icon} alt={title} width={48} height={48} />
      <div className="flex flex-col gap-1">
        <h4 className={"font-medium"}>{title}</h4>
        <p className={"font-medium text-[#837D7D]"}>{description}</p>
      </div>
    </div>
  );
};

export default PerkCard;
