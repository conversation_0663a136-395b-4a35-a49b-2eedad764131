const mongoose = require("mongoose");

const payoutSchema = new mongoose.Schema(
  {
    payoutId: {
      type: String,
    },
    jobId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Job",
      required: true,
    },
    bankReference: {
      type: String,
    },
    payoutStatus: {
      type: String,
      enum: ["pending", "paid", "rejected"],
      default: "pending",
    },
    commission: {
      type: String,
      require: true,
    },
    payRate: {
      type: String,
      require: true,
    },
    payRateAmount: {
      type: String,
      require: true,
    },
    candidateId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Candidate",
      required: true,
    },
    recruiterId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    payoutReleaseDate: {
      type: String,
    },
    guaranteePeriod: {
      type: String,
      require: true,
    },
    accountManagerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  { timestamps: true }
);

const payOut = mongoose.model("payout", payoutSchema);

module.exports = { payOut };
