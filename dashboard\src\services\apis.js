const BASE_URL =
  process.env.NODE_ENV === "development"
    ? "http://localhost:4000/api/v1"
    : "https://api.hirring.com/api/v1";

export const authEndpoints = {
  SIGN_UP_API: `${BASE_URL}/auth/register`,
  SIGN_IN_API: `${BASE_URL}/auth/login`,
  GENRATE_ACCESS_TOKEN_API: `${BASE_URL}/auth/refresh-token`,
  SIGN_IN_USER_INFO_API: `${BASE_URL}/auth/user`,
  VERIFY_EMAIL_API: `${BASE_URL}/auth/verify-email`,
  VERIFY_FORGOT_PASSWORD_API: `${BASE_URL}/auth/forgot-password`,
  RESET_PASSWORD_API: `${BASE_URL}/auth/reset-password`,
};

export const candidateEndpoints = {
  CREATE_CANDIDATE_API: `${BASE_URL}/candidate/addcandidate/personalDetails`,
  UPDATE_CANDIDATE_API: `${BASE_URL}/candidate/updatecandidate/personaldetails`,
  // UPDATE_Lis_API: `${BASE_URL}/candidate/updatecandidate/personaldetails`,
  GET_ALL_CANDIDATE: `${BASE_URL}/candidate/getallcandidate`,
  LICENSING_UPDATE_API: `${BASE_URL}/candidate/addcandidate/licensing`,
  EDUCATION_UPDATE_API: `${BASE_URL}/candidate/addcandidate/education`,
  SKILLSANDEXPERIENCE_UPDATE_API: `${BASE_URL}/candidate/addcandidate/skillsandexperience`,
  WORKHISTORTY_UPDATE_API: `${BASE_URL}/candidate/addcandidate/workhistory`,
  CERTIFICATION_UPDATE_API: `${BASE_URL}/candidate/addcandidate/certification`,
  HEALTHANDCOMPLIANCE_UPDATE_API: `${BASE_URL}/candidate/addcandidate/healthandcompliance`,
  SUBMISSIONDETAILS_UPDATE_API: `${BASE_URL}/candidate/addcandidate/submissiondetails`,
  DOCUMENTATTACHMENT_UPDATE_API: `${BASE_URL}/candidate/addcandidate/documentattachment`,
  GET_CANDIDATE_API: `${BASE_URL}/candidate/getcandidate`,
  GET_SUBMISSION: `${BASE_URL}/candidate/getsubmissions`,
  GET_CANDIDATE_JOB_SUBMISSIONS_API: `${BASE_URL}/candidate/candidatejobsubmissions`,
  INSTANT_SUBMIT_API: `${BASE_URL}/candidate/instantsubmit`,
  UPDATE_SUBMISSION_STATUS_AND_TIMELINE: `${BASE_URL}/candidate/candidate-status-update`,
  GET_CANDIDATES_BY_JOB_RECRUITER: `${BASE_URL}/candidate/get-candidates-by-job-recruiter`,
  GET_CANDIDATE_TIMELINE: `${BASE_URL}/candidate/candidate-timeline`,
  GET_CANDIDATE_WITHOUT_JOB_SUBMISSIONS: `${BASE_URL}/candidate/candidate-without-job-submissions`,
  GET_SUBMISSION_DETAILS: `${BASE_URL}/candidate/submission-details`,
  GET_MAX_EXPERIENCE_API: `${BASE_URL}/candidate/max-experience`,
};

export const dashboardEndPoint = {
  GET_DASHBOARD_RECRUITER_API: `${BASE_URL}/recruiter/dashboard-stats`,
  GET_DASHBOARD_HEAD_ACCOUNT_MANAGER_API: `${BASE_URL}/headmanager/dashboard-stats`,
  GET_DASHBOARD_SUBMISSION_STATS_API: `${BASE_URL}/candidate/submission-stats`,
};

export const jobEndpoints = {
  CREATE_JOBS_API: `${BASE_URL}/job/create-job`,
  GET_ALL_JOBS_API: `${BASE_URL}/job/getalljobs`,
  GET_JOBS_BY_ID_API: `${BASE_URL}/job/getJob`,
  UPDATE_JOB_HEAD_ACCOUNT_API: `${BASE_URL}/job/updatejobheadAccount`,
  PUBLISH_JOB_HEAD_ACCOUNT_API: `${BASE_URL}/job/publishJobheadAccount`,
  GET_UNASSIGNED_JOBS_API: `${BASE_URL}/job/getunassignedjobs`,

  BULK_ASSIGN_JOB_TO_MANAGER: `${BASE_URL}/job/jobassigntomanager`,
  BULK_UPLOAD_JOBS_API: `${BASE_URL}/job/bulkupload`,

  GET_HIGH_PERIORITY_JOBS: `${BASE_URL}/job/gethighperiorityjobs`,
  GET_ACTIVE_JOBS: `${BASE_URL}/job/getactivejobs`,
  GET_CLOSE_JOBS: `${BASE_URL}/job/getclosejobs`,
  GET_UN_ENGAGED_JOBS: `${BASE_URL}/job/getunengagedjobs`,
  UPDATE_JOB_STATUS: `${BASE_URL}/job/updatejobstatus`,
  GET_ASSIGN_RECRUITER_API: `${BASE_URL}/job/get-job-details-and-recruiters`,
  WORK_ON_REQUEST_API: `${BASE_URL}/job/workonrequest/create`,
  REMOVE_WORK_ON_REQUEST_API: `${BASE_URL}/job/workonrequest/remove`,

  GET_JOBS_BY_ACCOUNT_MANAGER: `${BASE_URL}/job/jobs-by-account-manager`,

  // am jobs routes
  PUBLISH_JOB_API: `${BASE_URL}/job/publishJob`,

  // recruiter jobs
  GET_WORK_UPON_JOBS: `${BASE_URL}/recruiter/work-upon-jobs`,
  GET_MATCHED_JOBS: `${BASE_URL}/recruiter/matched-jobs`,
  GET_WORK_ON_REQUEST_JOBS: `${BASE_URL}/recruiter/work-on-request-jobs`,
  GET_ALL_JOBS: `${BASE_URL}/recruiter/get-all-jobs`,
  GET_SAVE_JOBS: `${BASE_URL}/recruiter/get-all-save-jobs`,

  GET_ALL_RECRUITERS_WORKING_ON_JOB: `${BASE_URL}/job/am/get-recruiters-working-on-job`,

  ADD_BOOKMARK_JOBS: `${BASE_URL}/recruiter/add-to-bookmark`,
  REMOVE_BOOKMARK_JOBS: `${BASE_URL}/recruiter/remove-to-bookmark`,
  WORK_ON_REQUEST_STATUS_UPDATE_API: `${BASE_URL}/recruiter/work-on-request-status-update`,
  SELECT_JOB_TO_WORK_ON_API: `${BASE_URL}/recruiter/select-job-to-work-upon`,
  UPMAP_JOB_API: `${BASE_URL}/recruiter/unmap-job`,
};

export const headManager = {
  GET_ALL_MANAGER_WORK_ON_REQUEST: `${BASE_URL}/headmanager/getallmanagerworkhistory`,
  GET_ALL_SUBMISSIONS: `${BASE_URL}/headmanager/getallsubmission`,
  GET_ALL_ACCOUNT_MANAGER: `${BASE_URL}/headmanager/manager/getallmanager`,
  CREATE_ACCOUNT_MANAGER: `${BASE_URL}/auth/register/accountmanager`,
  GET_ACCOUNT_MANAGER_BY_ID: `${BASE_URL}/headmanager/manager/getmanager`,
  UPDATE_ACCOUNT_MANAGER: `${BASE_URL}/headmanager/manager/update`,
};

export const userEndpoints = {
  UPDATE_PROFILE_API: `${BASE_URL}/user/update-profile`,
  RESEND_VERIFICATION_API: `${BASE_URL}/user/resend-verification`,
};

export const chatEndpoints = {
  CREATE_CHAT_API: `${BASE_URL}/chat/create-chat`,
  SEND_MESSAGE_API: `${BASE_URL}/chat/send-message`,
  GET_CHAT_DETAILS_API: `${BASE_URL}/chat/messages`,
  GET_USER_CHATS_API: `${BASE_URL}/chat/user-chats`,
  GET_JOB_CHATS_API: `${BASE_URL}/chat/job-chats`,
  GET_CANDIDATE_CHATS_API: `${BASE_URL}/chat/candidate-chats`,
};

export const accountManagerEndpoints = {
  GET_ALL_SUBMISSIONS_API: `${BASE_URL}/accountmanager/getallsubmission`,
  GET_ALL_ACCOUNT_MANAGER_SUBMISSIONS: `${BASE_URL}/accountmanager/getallsubmission`,
};

export const recruiterEndpoints = {
  GET_COIN_HISTORY: `${BASE_URL}/recruiter/coinHistory`,
  GET_RECRUITER_DETAILS: `${BASE_URL}/recruiter/recruiter-details`,
  GET_ALL_RECRUITERS: `${BASE_URL}/recruiter/get-all-recruiters`,
  GET_RECRUITER_BY_JOBID: `${BASE_URL}/recruiter/get-recruiters-by-job-id`,
};

export const notificationEndpoints = {
  GET_NOTIFICATION: `${BASE_URL}/notification/getnotification`,
  MARK_AS_READ_NOTIFICATION: `${BASE_URL}/notification/mark-read`,
};

export const payoutEndpoints = {
  GET_PAYOUT_API: `${BASE_URL}/payout/getpayout`,
  ADD_PAYOUT_API: `${BASE_URL}/payout/create`,
  CREATE_PAYOUT_API: `${BASE_URL}/payout/create-payout`,
  UPDATE_PAYOUT_API: `${BASE_URL}/payout/update-details`,
};
