const { default: mongoose } = require("mongoose");
const { Notification } = require("../models/Notification.models");

//* @Desc get all notification
//* @Route GET /api/v1/notification/getnotification
//* @Access Private
exports.getAllNotification = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const notification = await Notification.aggregate([
      {
        $match: {
          recipients: new mongoose.Types.ObjectId(user._id),
        },
      },
      {
        $addFields: {
          isRead: {
            $cond: {
              if: {
                $in: [new mongoose.Types.ObjectId(user._id), "$isRead"],
              },
              then: true,
              else: false,
            },
          },
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ]);
    res.status(200).json({ success: true, data: notification });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: error?.message });
  }
};

//* @Desc mark read notification
//* @Route POST /api/v1/notification/mark-read
//* @Access Private
exports.markRead = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const notificationId = req?.body?.notificationId;
    if (notificationId) {
      // this will mark as read only single notifications
      await Notification.findByIdAndUpdate(notificationId, {
        $addToSet: { isRead: user._id },
      });

      // it will mark as read all notifications
    } else {
      const notification = await Notification.aggregate([
        {
          $match: {
            recipients: new mongoose.Types.ObjectId(user._id),
          },
        },
      ]);

      const notificationIds = notification?.map((item) => item?._id);

      // update all notification mark as read
      await Notification.updateMany(
        {
          _id: { $in: notificationIds },
          isRead: { $ne: user._id }, // only add if not already present
        },
        {
          $addToSet: { isRead: user._id },
        }
      );
    }

    return res
      .status(200)
      .json({ success: true, message: "notification mark as read" });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: error?.message });
  }
};
