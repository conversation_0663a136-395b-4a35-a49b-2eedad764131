import React, { useState, useEffect, useRef } from "react";
import TableLikeSearchSelector from "./TableLikeSearchSelector";

const SearchDropdown = ({ isOpen, onClose, onSearch, activeTab }) => {
  const dropdownRef = useRef(null);
  const debounceTimerRef = useRef(null);
  
  const [searchInputs, setSearchInputs] = useState({
    searchTerm: "",
    searchField: "all",
  });

  // Search field options based on your data structure
  const searchFieldOptions = {
    0: [
      // Un-assigned Jobs
      { value: "all", label: "All Fields" },
      { value: "jobTitle", label: "Job Title" },
      { value: "jobType", label: "Job Type" },
      { value: "jobId", label: "Job ID" },
      { value: "industry", label: "Specialization" },
    ],
    1: [
      // High Priority Jobs
      { value: "all", label: "All Fields" },
      { value: "jobTitle", label: "Job Title" },
      { value: "jobId", label: "Job ID" },
      { value: "jobType", label: "Job Type" },
      { value: "location", label: "Location" },
    ],
    2: [
      // Active Jobs
      { value: "all", label: "All Fields" },
      { value: "jobId", label: "Job ID" },
      { value: "jobTitle", label: "Job Title" },
      { value: "location", label: "Location" },
      { value: "jobType", label: "Job Type" },
    ],
    3: [
      // Un-engaged Jobs
      { value: "all", label: "All Fields" },
      { value: "jobId", label: "Job ID" },
      { value: "jobTitle", label: "Job Title" },
      { value: "location", label: "Location" },
      { value: "jobType", label: "Job Type" },
    ],
    4: [
      // All Jobs
      { value: "all", label: "All Fields" },
      { value: "jobTitle", label: "Job Title" },
      { value: "jobId", label: "Job ID" },
      { value: "jobType", label: "Job Type" },
      { value: "location", label: "Location" },
      { value: "jobStatus", label: "Status" },
    ],
    5: [
      // Closed Jobs
      { value: "all", label: "All Fields" },
      { value: "jobTitle", label: "Job Title" },
      { value: "jobId", label: "Job ID" },
      { value: "jobType", label: "Job Type" },
      { value: "location", label: "Location" },
      { value: "clientname", label: "Client Name" },
    ],
    // ADDED: Manager fields (use this for AccountManager component)
    manager: [
      { value: "all", label: "All Fields" },
      { value: "userId", label: "Employee ID" },
      { value: "name", label: "Name" },
      { value: "email", label: "Email" },
      { value: "status", label: "Status" },
    ],
    submissions: [
      { value: "all", label: "All Fields" },
      { value: "candidateID", label: "Candidate ID" },
      { value: "candidateName", label: "Name" }, 
      { value: "recruiterName", label: "Recruiter Name" }, 
      { value: "jobId", label: "Job ID" },
      { value: "jobTitle", label: "Job Title" }, 
      { value: "status", label: "Status" }, 
    ],
  };

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Debounced search effect
  useEffect(() => {
    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Only trigger search if there's a search term
    if (searchInputs.searchTerm.trim()) {
      debounceTimerRef.current = setTimeout(() => {
        onSearch(searchInputs);
      }, 600);
    } else if (searchInputs.searchTerm === "") {
      onSearch({
        searchTerm: "",
        searchField: searchInputs.searchField,
      });
    }

    // Cleanup function
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [searchInputs.searchTerm, searchInputs.searchField]);

  // Get current search fields based on activeTab or component context
  const getCurrentSearchFields = () => {
    // If activeTab is "manager" or if this is being used in AccountManager component
    if (activeTab === "manager" || activeTab === undefined) {
      return searchFieldOptions.manager;
    }
    return searchFieldOptions[activeTab] || searchFieldOptions[0];
  };

  const currentSearchFields = getCurrentSearchFields();

  const handleInputChange = (field, value) => {
    setSearchInputs((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleManualSearch = () => {
    // Clear any pending debounced search
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    if (searchInputs.searchTerm.trim()) {
      onSearch(searchInputs);
      onClose();
    } 
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      // Clear any pending debounced search and trigger immediate search
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      handleManualSearch();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      ref={dropdownRef}
      className="absolute top-full right-0 mt-2 w-88 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
    >
      {/* Search Form */}
      <div className="p-4 pb-0 flex ">
        {/* Search Field Selector */}
        <TableLikeSearchSelector
          searchInputs={searchInputs}
          handleInputChange={handleInputChange}
          currentSearchFields={currentSearchFields}
        />

        {/* Search Input */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchInputs.searchTerm}
              onChange={(e) => handleInputChange("searchTerm", e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter search term..."
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg
                className="w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchDropdown;