import { useEffect, useState } from "react";

const useAvatar = (name) => {
  const [avatarUri, setAvatarUri] = useState("");

  useEffect(() => {
    if (!name) return;

    let isMounted = true;

    const generateAvatar = async () => {
      const { createAvatar } = await import("@dicebear/core");
      const { initials } = await import("@dicebear/collection");

      const avatar = createAvatar(initials, {
        seed: name,
        backgroundColor: ["3E9900"],
      });

      const uri = await avatar.toDataUri();

      if (isMounted) setAvatarUri(uri);
    };

    generateAvatar();

    return () => {
      isMounted = false;
    };
  }, [name]);

  return avatarUri;
};

export default useAvatar;
