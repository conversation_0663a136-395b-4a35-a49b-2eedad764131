const nodemailer = require("nodemailer");

const mailSender = async (email, title, body, attachment) => {
  try {
    // Create reusable transporter object using SMTP transport
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      tls: {
        ciphers: "SSLv3",
      },
    });

    let info = await transporter.sendMail({
      from: `"Hirring.com" <<EMAIL>>`,
      to: `${email}`,
      subject: `${title}`,
      html: body,
      replyTo: "<EMAIL>, <EMAIL>",
      attachments: attachment
        ? [
            {
              filename: attachment.filename,
              content: attachment.content, // Use content for file buffer
            },
          ]
        : [],
    });
    return info;
  } catch (error) {
    console.error("Error during sending email", error);
  }
};

module.exports = mailSender;
