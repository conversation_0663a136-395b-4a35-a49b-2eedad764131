const express = require("express");
const { auth, authorize } = require("../middlewares/auth");
const {
  getAllRecruiters,
  getRecruiterDetails,
} = require("../controllers/Recruiter");
const {
  getAllManager,
  getManager,
  updateManager,
  getAllManagerWorkHistory,
  getDashboardData,
} = require("../controllers/Manager");
const { getAllSubmission } = require("../controllers/JobSubmission");
const router = express.Router();

// get recruiter
router.get(
  "/recruiter/getallrecruiters",
  auth,
  authorize("headAccountManager"),
  getAllRecruiters
);

router.post(
  "/manager/update/:managerID",
  auth,
  authorize("headAccountManager"),
  updateManager
);

router.get(
  "/recruiter/getrecruiter/:recruiterID",

  auth,

  authorize("headAccountManager"),

  getRecruiterDetails
);

// get manager
router.get(
  "/manager/getallmanager",
  auth,
  authorize("headAccountManager"),
  getAllManager
);
router.get(
  "/manager/getmanager/:managerID",
  auth,
  authorize("headAccountManager"),
  getManager
);

router.get(
  "/getallmanagerworkhistory",
  auth,
  authorize("headAccountManager"),
  getAllManagerWorkHistory
);

// get submissions

router.get(
  "/getallsubmission",
  auth,
  authorize("headAccountManager"),
  getAllSubmission
);

router.get(
  "/dashboard-stats",
  auth,
  authorize("headAccountManager"),
  getDashboardData
);
module.exports = router;
