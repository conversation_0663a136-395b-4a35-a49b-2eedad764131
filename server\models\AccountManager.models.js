const mongoose = require("mongoose");

const AccountManagerSchema = new mongoose.Schema(
  {
    user: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      userId: {
        type: String,
        required: true,
      },
    },
    city: {
      type: String,
      trim: true,
    },
    state: {
      type: String,
      trim: true,
    },
    country: {
      type: String,
      trim: true,
    },
    domain: {
      type: [String],
    },
    isUserEligibileForITAndHealthcare: {
      type: Boolean,
    },
    recruiterWorkingUnder: {
      type: [
        {
          _id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
          },
          userId: {
            type: String,
          },
        },
      ],
      default: undefined,
    },
    status: {
      type: String,
      enum: ["Active", "Inactive", "on-leave"],
      default: "Active",
    },
    role: {
      type: String,
      enum: ["accountManager", "headAccountManager"],
      default: "accountManager",
    },
  },
  {
    timestamps: true,
  }
);

const accountManager = mongoose.model("accountManager", AccountManagerSchema);

module.exports = { accountManager };
