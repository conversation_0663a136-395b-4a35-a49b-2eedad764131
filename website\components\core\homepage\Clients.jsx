import Image from "next/image";
import clients from "@/data/clients";
import "./clients.css";

const Clients = () => {
  return (
    <section className="bg-[#CEFFAD] px-20 py-8 overflow-hidden">
      <div className="clientSlider">
        <div className="clientTrack">
          {[...clients, ...clients].map((client, index) => (
            <div className="clientLogo" key={index}>
              <Image
                src={client.logo}
                alt={client.name}
                width={100}
                height={100}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Clients;
