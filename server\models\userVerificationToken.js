const mongoose = require("mongoose");

const userVerificationTokenSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  token: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ["emailVerification", "passwordReset"],
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: "1d",
  },
});

const UserVerificationToken = mongoose.model(
  "userVerificationToken",
  userVerificationTokenSchema
);

module.exports = { UserVerificationToken };
