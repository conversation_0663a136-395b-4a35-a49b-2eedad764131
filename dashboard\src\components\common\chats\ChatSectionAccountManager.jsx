import MessagesSection from "./MessagesSection";
import InputContainer from "./InputContainer";
import RecruitersList from "./RecruitersList";
import { useState } from "react";
import AccountManagerChatHeader from "./AccountManagerChatHeader";

const ChatSectionAccountManager = ({ job }) => {
  const [getMessages, setGetMessages] = useState(false);
  const [selectedRecruiter, setSelectedRecruiter] = useState("");
  const [newChatId, setNewChatId] = useState("");

  return (
    <div className="flex flex-col justify-center gap-[1.125rem] w-full">
      <div className="flex w-full">
        <RecruitersList
          jobId={job?._id}
          setSelectedRecruiter={setSelectedRecruiter}
        />

        <div className="w-[70%]">
          <AccountManagerChatHeader selectedRecruiter={selectedRecruiter} />
          <MessagesSection
            jobId={job?._id}
            getMessages={getMessages}
            setGetMessages={setGetMessages}
            selectedRecruiter={selectedRecruiter}
          />
        </div>
      </div>
      <InputContainer
        accountManager={job?.accountManager}
        jobId={job?._id}
        setGetMessages={setGetMessages}
        selectedRecruiter={selectedRecruiter}
        newChatId={newChatId}
        setNewChatId={setNewChatId}
      />
    </div>
  );
};

export default ChatSectionAccountManager;
