const { payOut } = require("../models/payout");
const mongoose = require("mongoose");
const Job = require("../models/Job.models");
const recruiterProfile = require("../models/RecruiterProfile.models");
const { candidateSubmission } = require("../models/CandidateSubmission.models");
const { createID } = require("../utils");
const generateId = require("../utils/genreateId");

//* @desc Create a pending payout
//* @route POST /api/v1/payout/create-pending
//* @access Private
exports.createPayout = async (req, res) => {
  const { submissionId, guaranteePeriod, payRateAmount, commission } = req.body;
  if (!submissionId || !guaranteePeriod || !payRateAmount || !commission) {
    return res.status(400).json({ success: false, message: "Missing fields" });
  }
  const submission = await candidateSubmission.findOne({ submissionId });
  if (!submission)
    return res
      .status(404)
      .json({ success: false, message: "Submission not found" });

  const exists = await payOut.findOne({
    jobId: submission.job._id,
    candidateId: submission.candidate._id,
    recruiterId: submission.submittedBy._id,
  });
  if (exists)
    return res
      .status(400)
      .json({ success: false, message: "Payout already exists" });

  const payoutId = await generateId("HP");

  const payout = await payOut.create({
    payoutId,
    jobId: submission.job._id,
    candidateId: submission.candidate._id,
    recruiterId: submission.submittedBy._id,
    guaranteePeriod,
    payRateAmount,
    commission,
    payoutStatus: "pending",
    accountManagerId: req.user._id,
  });

  res.status(201).json({ success: true, data: payout });
};

exports.updatePayoutDetails = async (req, res) => {
  const { payoutId, payoutReleaseDate, bankReference, payoutStatus } = req.body;
  if (!payoutId) {
    return res
      .status(400)
      .json({ success: false, message: "Missing payoutId" });
  }
  const updateFields = {};
  if (payoutReleaseDate) updateFields.payoutReleaseDate = payoutReleaseDate;
  if (bankReference) updateFields.bankReference = bankReference;
  if (payoutStatus) updateFields.payoutStatus = payoutStatus;

  const payout = await payOut.findOneAndUpdate({ payoutId }, updateFields, {
    new: true,
  });
  if (!payout)
    return res
      .status(404)
      .json({ success: false, message: "Payout not found" });
  res.status(200).json({ success: true, data: payout });
};

//* @Desc Register a new payout
//* @Route POST /api/v1/payout/create
//* @Access Private
// exports.registerPayout = async (req, res) => {
//   try {
//     const {
//       jobID,
//       recruiterID,
//       candidateID,
//       payoutReleaseDate,
//       payRate,
//       payRateAmount,
//       commission,
//       bankReference,
//       guaranteePeriod,
//     } = req.body;

//     if (
//       !(
//         jobID &&
//         payoutReleaseDate &&
//         recruiterID &&
//         candidateID &&
//         payRate &&
//         payRateAmount &&
//         commission &&
//         bankReference &&
//         guaranteePeriod
//       )
//     ) {
//       return res
//         .status(400)
//         .json({ success: false, message: "Please enter valid fields." });
//     }

//     const job = await Job.findOne({ jobId: jobID });
//     const recruiter = await recruiterProfile.findOne({
//       "user.userId": recruiterID,
//     });
//     const candidate = await candidateSubmission.findOne({
//       "candidate.candidateID": candidateID,
//     });

//     if (!job || !recruiter || !candidate) {
//       return res.status(404).json({
//         success: false,
//         message: `${
//           !job ? "Job" : !recruiter ? "Recruiter" : "Candidate"
//         } not found.`,
//       });
//     }

//     const countPayout = await generateId("HP");

//     const existingPayout = await payOut.findOne({
//       jobId: job._id,
//       recruiterId: recruiter.user._id,
//       candidateId: candidate.candidate?._id,
//     });

//     if (existingPayout) {
//       return res.status(400).json({
//         success: false,
//         message:
//           "Payout already exists for this job, recruiter, and candidate combination.",
//       });
//     }

//     const payoutInstance = new payOut({
//       payoutId: countPayout,
//       jobId: job._id,
//       bankReference,
//       commission,
//       payRate,
//       payRateAmount,
//       candidateId: candidate.candidate?._id,
//       recruiterId: recruiter.user._id,
//       payoutReleaseDate,
//       guaranteePeriod,
//       accountManagerId: req.user._id,
//     });

//     const payout = await payoutInstance.save();

//     res.status(201).json({ success: true, data: payout });
//   } catch (error) {
//     console.log(error);
//     res.status(400).json({ success: false, message: error?.message });
//   }
// };

exports.getPayout = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const userId = req.user?._id;
    const userType = req.user?.userType;
    const status = req.query.status;

    if (!userId) {
      return res
        .status(401)
        .json({ success: false, message: "Unauthorized access" });
    }

    if (userType === "headAccountManager") {
      // See all payouts
      matchCondition = {};
    } else if (userType === "recruiter") {
      // Only payouts for this recruiter
      matchCondition = { recruiterId: new mongoose.Types.ObjectId(userId) };
    } else {
      // Default: account manager sees their payouts
      matchCondition = {
        accountManagerId: new mongoose.Types.ObjectId(userId),
      };
    }

    if (status === "pending") {
      matchCondition = { ...matchCondition, payoutStatus: "pending" };
    }

    const payout = await payOut.aggregate([
      {
        $match: matchCondition,
      },
      {
        $lookup: {
          from: "jobs",
          localField: "jobId",
          foreignField: "_id",
          as: "job",
        },
      },
      { $unwind: { path: "$job", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "candidates",
          localField: "candidateId",
          foreignField: "_id",
          as: "candidate",
        },
      },
      { $unwind: { path: "$candidate", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "recruiterId",
          foreignField: "_id",
          as: "recruiter",
        },
      },
      { $unwind: { path: "$recruiter", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "candidatesubmissions",
          let: { candidateId: "$candidateId", jobId: "$jobId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$candidate._id", "$$candidateId"] },
                    { $eq: ["$job._id", "$$jobId"] },
                  ],
                },
              },
            },
          ],
          as: "candidateSubmission",
        },
      },
      {
        $unwind: {
          path: "$candidateSubmission",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          payoutId: 1,
          bankReference: 1,
          commission: 1,
          payRate: 1,
          payRateAmount: 1,
          payoutReleaseDate: 1,
          guaranteePeriod: 1,
          payoutStatus: 1,
          createdAt: 1,
          updatedAt: 1,
          candidateSubmissionStatus: "$candidateSubmission.status",
          jobid: "$job.jobId",
          jobtitle: "$job.jobTitle",
          jobtype: "$job.jobType",
          candidateName: {
            $concat: [
              { $ifNull: ["$candidate.personalDetails.firstName", ""] },
              " ",
              { $ifNull: ["$candidate.personalDetails.lastName", ""] },
            ],
          },
          recruiterName: {
            $concat: [
              { $ifNull: ["$recruiter.name.firstName", ""] },
              " ",
              { $ifNull: ["$recruiter.name.lastName", ""] },
            ],
          },
          createdBy: "$accountManagerId",
        },
      },
      {
        $facet: {
          total: [{ $count: "count" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ]);

    const total = payout[0].total[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);
    const data = payout[0].data;

    res.status(200).json({
      success: true,
      data,
      totalRecords: total,
      currentPage: page,
      totalPages,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: error?.message });
  }
};
