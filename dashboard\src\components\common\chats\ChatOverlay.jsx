import { useEffect, useState } from "react";
import ChatHeader from "./ChatHeader";
import MessagesSection from "./MessagesSection";
import { getSubmissionDetails } from "../../../services/operations/candidateAPI";
import InputContainer from "./InputContainer";

const ChatOverlay = ({ job, submissionId, submission }) => {
  const [chatId, setChatId] = useState("");
  const [getMessages, setGetMessages] = useState(false);

  const fetchSubmissionDetails = async () => {
    const response = await getSubmissionDetails(submissionId);
    if (response?.success) {
      setChatId(response.data.chatId);
    }
  };

  useEffect(() => {
    submission && fetchSubmissionDetails();
    console.log("Submission details fetched:", submission?._id);
  }, [submission]);

  return (
    <div>
      <ChatHeader job={job} />
      {/* <Messages chatId={chatId} /> */}
      <MessagesSection
        newChatId={chatId}
        getMessages={getMessages}
        setGetMessages={setGetMessages}
        minHeight="70vh"
      />
      <InputContainer
        newChatId={chatId}
        setNewChatId={setChatId}
        setGetMessages={setGetMessages}
        accountManager={job?.accountManager}
        submissionId={submission?._id}
      />
    </div>
  );
};

export default ChatOverlay;
