import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  candidateID: null,
};

export const candidateSlice = createSlice({
  name: "candidate",
  initialState,
  reducers: {
    setCandidateID: (state, action) => {

      console.log("Setting candidate ID:", action.payload);
      
      state.candidateID = action.payload;
    },
    clearCandidateID: (state) => {
      state.candidateID = null;
    },
  },
});

export const { setCandidateID, clearCandidateID } = candidateSlice.actions;
export default candidateSlice.reducer;