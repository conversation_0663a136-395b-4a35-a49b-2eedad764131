import useAvatar from "../../../utils/AvatarGenerator";

const RecruiterCard = ({ recruiter }) => {
  const avatar = useAvatar(recruiter?.name?.firstName);

  return (
    <div className="flex py-3 px-[1.125rem] h-[4.375rem] items-center gap-4 self-stretch border border-[#F5F5F5]">
      <img src={avatar} alt="" className="h-11 w-11 rounded-full" />
      <div className="flex flex-col w-full gap-0.5">
        <div className="flex items-center w-full self-stretch justify-between">
          <span className=" text-[#252B37] font-semibold leading-[150%]">
            {recruiter?.name?.firstName} {recruiter?.name?.lastName}
          </span>
          {/* <span className="text-[#717680] text-[0.75rem] leading-[150%]">
            08:00
          </span> */}
        </div>
      </div>
    </div>
  );
};

export default RecruiterCard;
