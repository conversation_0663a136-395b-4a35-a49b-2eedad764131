import { useEffect, useState } from "react";
import StatsCard from "../../components/common/Dashboard/StatsCard";
import ContainerCard from "../../components/common/Dashboard/ContainerCard";
import ListCard from "../../components/common/Dashboard/ListCard";
import OverAllStats from "../../components/common/Dashboard/OverAllStats";
import { getAllSubmissions } from "../../services/operations/accountManagerAPI";
import { getTimeAgo } from "../../utils/getTimeAgo";
import { Link, useNavigate } from "react-router-dom";
import MyStatsChart from "../../components/core/AccountManager/MyStatsChart";
import {
  getAllJobDashboard,
  getAllSubmissionDashboard,
} from "../../services/operations/dashboardAPI";

const Dashboard = () => {
  const [jobs, setJobs] = useState([]);
  const [submissions, setSubmissions] = useState([]);
  const [recentJobs, setRecentJobs] = useState([]);
  const [recentSubmissions, setRecentSubmissions] = useState([]);
  const [submissionPerJob, setSubmissionPerJob] = useState([]);
  const [candidateStats, setCandidateStats] = useState([]);
  const [submissionStats, setSubmissionStats] = useState([]);

  const navigate = useNavigate();
  const getJobs = async () => {
    const response = await getAllJobDashboard();

    if (response?.success) {
      setJobs(response?.results || []);
    } else {
      console.error("Failed to fetch jobs:", response?.message);
    }
  };

  const getSubmissions = async () => {
    const response = await getAllSubmissionDashboard();

    if (response?.success) {
      setSubmissions(response?.results || []);
    } else {
      console.error("Failed to fetch submissions:", response?.message);
    }
  };

  useEffect(() => {
    getJobs();
    getSubmissions();
  }, []);

  useEffect(() => {
    const getRecentJobs = (jobs = []) => {
      const now = new Date();
      const twentyFourHoursAgo = new Date(
        now.getTime() - 3.5 * 24 * 60 * 60 * 1000
      );

      const recentJobs = jobs
        .filter((job) => new Date(job.createdAt) >= twentyFourHoursAgo)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      return recentJobs;
    };

    const recentJobs = getRecentJobs(jobs);
    setRecentJobs(recentJobs);
  }, [jobs]);

  useEffect(() => {
    const getRecentSubmissions = (submissions = []) => {
      const now = new Date();
      const twentyFourHoursAgo = new Date(
        now.getTime() - 3.5 * 24 * 60 * 60 * 1000
      );

      const recentSubmissions = submissions
        .filter(
          (submission) => new Date(submission.submittedAt) >= twentyFourHoursAgo
        )
        .sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));

      return recentSubmissions;
    };
    const recentSubmissions = getRecentSubmissions(submissions);
    setRecentSubmissions(recentSubmissions);
  }, [submissions]);

  useEffect(() => {
    const countSubmissionsPerJob = (submissions) => {
      const jobMap = {};

      submissions.forEach((submission) => {
        const jobTitle = submission?.job?.jobTitle || "Unknown Job";
        const jobId = submission?.job?.jobId || "Unknown ID";

        const key = `${jobId}__${jobTitle}`;

        if (jobMap[key]) {
          jobMap[key].count++;
        } else {
          jobMap[key] = {
            jobId,
            jobTitle,
            count: 1,
          };
        }
      });

      // Convert to array of objects
      const resultArray = Object.values(jobMap);

      return resultArray;
    };
    const submissionCounts = countSubmissionsPerJob(submissions);
    setSubmissionPerJob(submissionCounts);
    const candidateStats = calculateCandidateStats(submissions);
    setCandidateStats(candidateStats);
  }, [submissions]);

  useEffect(() => {
    const calculateSubmissionStats = (submissions) => {
      const stats = {
        total: submissions.length,
        shortlisted: 0,
        interviewed: 0,
        offered: 0,
        hired: 0,
        rejected: 0,
      };

      submissions.forEach(({ status }) => {
        const s = status?.toLowerCase();
        if (!s) return;

        // shortlisted logic
        if (
          [
            "selected",
            "interviewing",
            "reject after interview",
            "awaiting offer",
            "rejected",
            "offer released",
            "offer accepted",
            "offer rejected",
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(s)
        ) {
          stats.shortlisted++;
        }

        // interviewed logic
        if (
          [
            "interviewing",
            "reject after interview",
            "awaiting offer",
            "offer released",
            "offer accepted",
            "offer rejected",
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(s)
        ) {
          stats.interviewed++;
        }

        // offered logic
        if (
          [
            "offer released",
            "offer accepted",
            "offer rejected",
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(s)
        ) {
          stats.offered++;
        }

        // hired logic
        if (
          [
            "hired-under guarantee period",
            "guarantee period not completed",
            "guarantee period completed",
          ].includes(s)
        ) {
          stats.hired++;
        }

        // rejected logic
        if (
          [
            "offer rejected",
            "reject after interview",
            "guarantee period not completed",
          ].includes(s)
        ) {
          stats.rejected++;
        }
      });

      return stats;
    };

    const submissionStats = calculateSubmissionStats(submissions);
    setSubmissionStats(submissionStats);
  }, [submissions]);

  const calculateCandidateStats = (submissions) => {
    const stats = {
      shortlisted: 0,
      interviewed: 0,
      offered: 0,
      hired: 0,
      rejected: 0,
    };

    submissions.forEach(({ status }) => {
      const currentStatus = status?.toLowerCase();
      if (!currentStatus) return;

      const isRejected = currentStatus === "rejected";
      const isShortlisted = !["rejected", "reviewing"].includes(currentStatus);
      const isInterviewed = ![
        "rejected",
        "submitted",
        "reviewing",
        "selected",
      ].includes(currentStatus);
      const isOffered = [
        "offer released",
        "offer accepted",
        "offer rejected",
        "hired-under guarantee period",
        "guarantee period not completed",
        "guarantee period completed",
      ].includes(currentStatus);
      const isHired = [
        "hired-under guarantee period",
        "guarantee period not completed",
        "guarantee period completed",
      ].includes(currentStatus);

      if (isRejected) stats.rejected++;
      if (isShortlisted) stats.shortlisted++;
      if (isInterviewed) stats.interviewed++;
      if (isOffered) stats.offered++;
      if (isHired) stats.hired++;
    });

    return stats;
  };

  return (
    <>
      <div className="flex gap-3">
        <StatsCard
          onClick={(e) => {
            e.preventDefault();
            navigate("/jobs?tabs=1");
          }}
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          lable="Total Jobs Assigned"
          value={jobs?.length || "0"}
        />
        <StatsCard
          onClick={(e) => {
            e.preventDefault();
            navigate("/jobs?tabs=1");
          }}
          imageBackground="bg-[#D1E9FF]"
          image={"/assets/icons/file_color_blue.svg"}
          lable="Active Jobs"
          value={
            jobs?.filter((job) => job?.jobStatus?.toLowerCase() === "active")
              .length || "0"
          }
        />
        <StatsCard
          onClick={(e) => {
            e.preventDefault();
            navigate("/jobs?tabs=4");
          }}
          imageBackground="bg-[#FEE4E2]"
          image="/assets/icons/file_color_red.svg"
          lable="Close Jobs"
          value={
            jobs?.filter((job) => job?.jobStatus?.toLowerCase() === "close")
              .length || "0"
          }
        />
        <StatsCard
          onClick={(e) => {
            e.preventDefault();
            navigate("/jobs?tabs=0");
          }}
          imageBackground="bg-[#D1FADF]"
          image="/assets/icons/file_color_green.svg"
          lable="High Priority Jobs"
          value={
            jobs?.filter(
              (job) => job?.priority?.toLowerCase() === "high priority"
            ).length || "0"
          }
        />
        <StatsCard
          onClick={(e) => {
            e.preventDefault();
            navigate("/submissions?tabs=0&status=submitted&showFilters=true");
          }}
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          lable="Total Resume Submitted"
          value={submissions?.length || "0"}
        />
      </div>

      <div className="mt-2 flex flex-col md:flex-row gap-3">
        <ContainerCard
          width="w-full md:flex-grow md:basis-1/3"
          label="Recently Assigned Jobs"
          childrenHeight=" h-80 overflow-auto"
          children={
            recentJobs?.length === 0 ? (
              <div className="flex justify-center items-center h-20 text-gray-500 text-sm">
                No Data Found
              </div>
            ) : (
              recentJobs.map((job, index) => (
                <Link
                  key={index}
                  to={`/jobs/jobdetails?jobId=${job?.jobId}&tab=jobDetails`}
                  className="flex flex-col gap-1"
                >
                  <ListCard
                    time={getTimeAgo(job?.createdAt)}
                    value={job?.jobTitle}
                    varient="job"
                  />
                </Link>
              ))
            )
          }
        />

        <ContainerCard
          width="w-full md:flex-grow md:basis-1/3"
          label="Recent Submission"
          childrenHeight=" h-80 overflow-auto"
          children={
            recentSubmissions?.length === 0 ? (
              <div className="flex justify-center items-center h-20 text-gray-500 text-sm">
                No Data Found
              </div>
            ) : (
              recentSubmissions.map((submission, index) => (
                <Link
                  key={index}
                  to={`/submissions/submissiondetails?submissionId=${submission?.submissionId}&candidateId=${submission?.candidate?.candidateID}`}
                  className="flex flex-col gap-1"
                >
                  <ListCard
                    time={getTimeAgo(submission?.submittedAt)}
                    value={
                      (submission?.submissionId
                        ? submission?.submissionId + " - "
                        : "") +
                      submission?.candidate?.personalDetails?.firstName +
                      " " +
                      submission?.candidate?.personalDetails?.lastName
                    }
                    varient="update"
                  />
                </Link>
              ))
            )
          }
        />

        <ContainerCard
          width="w-full md:flex-grow md:basis-1/3"
          label="Submission per jobs"
          childrenHeight=" h-80 overflow-auto"
          children={
            submissionPerJob?.length === 0 ? (
              <div className="flex justify-center items-center h-20 text-gray-500 text-sm">
                No Data Found
              </div>
            ) : (
              submissionPerJob?.map((submission, index) => (
                <Link
                  key={index}
                  to={`/jobs/jobdetails?jobId=${submission?.jobId}&tab=jobDetails`}
                >
                  <ListCard
                    count={submission?.count}
                    value={`${submission?.jobId}-${submission?.jobTitle}`}
                    varient="submissioncount"
                  />
                </Link>
              ))
            )
          }
        />
      </div>

      <div className="mt-2 flex gap-3">
        <ContainerCard
          onChange={(e) => {}}
          value="This Month"
          // isDropDown={true}
          label="Submission Stats Overview"
          width="flex-grow-[1.5] basis-4/6"
          children={
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-y-4">
                {[
                  {
                    value: submissionStats.shortlisted || 0,
                    label: "Shortlisted Candidate",
                  },
                  {
                    value: submissionStats.interviewed || 0,
                    label: "Interviewed Candidate",
                  },
                  {
                    value: submissionStats.offered || 0,
                    label: "Offered Candidate",
                  },
                  {
                    value: submissionStats.hired || 0,
                    label: "Hired Candidates",
                  },
                  {
                    value: submissionStats.rejected || 0,
                    label: "Rejected Candidates",
                  },
                ].map((stat, index) => (
                  <OverAllStats key={index} {...stat} />
                ))}
              </div>
            </>
          }
        />
        <ContainerCard
          onChange={(e) => {}}
          // value="This Month"
          // isDropDown={true}
          label="My Stats"
          width="flex-grow basis-3/6"
          children={
            <>
              <MyStatsChart />
            </>
          }
        />
      </div>
    </>
  );
};

export default Dashboard;
