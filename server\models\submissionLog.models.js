const mongoose = require("mongoose");

const submissionLogSchema = new mongoose.Schema(
  {
    notes: {
      type: String,
    },
    status: {
      type: String,
      required: true,
    },
    submittedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    submissionID: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "candidateSubmission",
      required: true,
    },
    eventDate: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

const submissionLog = mongoose.model("submissionlog", submissionLogSchema);
module.exports = { submissionLog };
