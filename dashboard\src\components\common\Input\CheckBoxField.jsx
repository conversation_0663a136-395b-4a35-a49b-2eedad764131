import React from "react";

const CheckboxField = ({
  label,
  required = false,
  checked,
  onChange,
  name,
  id,
  disabled = false,
  css="peer appearance-none h-5 w-5 border border-gray-400 rounded-md checked:bg-blue-600 checked:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 cursor-pointer",
  errorMessage,
}) => {
  return (
    <div className="flex flex-col space-y-1">
      <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 cursor-pointer">
        <div className="relative">
          <input
            type="checkbox"
            id={id || name}
            name={name}
            checked={checked}
            onChange={onChange}
            disabled={disabled}
            className={css}
          />
          <svg
            className="absolute top-[2px] left-[2px] w-4 h-4 text-white opacity-0 peer-checked:opacity-100 pointer-events-none"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        {label && (
          <span>
            {label} {required && <span className="text-red-800">*</span>}
          </span>
        )}
      </label>
      {errorMessage && (
        <span className="text-sm text-red-600">{errorMessage}</span>
      )}
    </div>
  );
};

export default CheckboxField;
