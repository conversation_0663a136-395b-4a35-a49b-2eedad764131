import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { getAccountManagerSubmissionStats } from "../../../services/operations/dashboardAPI";

const MyStatsChart = () => {
  const [stats, setStats] = useState([
    { "Coverage Ratio": 0 },
    { "Submission to Interviewing": 0 },
    { "Interviewing to Offer": 0 },
    { "Response Rate": 0 },
  ]);

  const [averageResponseTime, setAverageResponseTime] = useState(0);
  const [fill, setFill] = useState(0);

  async function getStats() {
    const data = await getAccountManagerSubmissionStats();

    setStats(
      Object.keys(data.data)?.map((item) => {
        return { [item]: data.data[item] };
      })
    );
  }

  useEffect(() => {
    getStats();
  }, []);

  useEffect(() => {
    const MAX_RESPONSE_TIME = 90 * 60;

    const responseRateObj = stats.find(
      (stat) => stat["Response Rate"] !== undefined
    );
    const averageResponseTime = responseRateObj
      ? responseRateObj["Response Rate"]
      : 0;

    setAverageResponseTime(averageResponseTime);
    const fill =
      averageResponseTime <= 0
        ? 0
        : Math.min(
            100,
            (Math.log(averageResponseTime + 1) /
              Math.log(MAX_RESPONSE_TIME + 1)) *
              100
          );

    setFill(fill);
  }, [stats]);

  return (
    <div className=" w-full flex p-5 flex-col shrink-0 self-stretch gap-6">
      <div className="flex w-full flex-col gap-[0.88rem]">
        {stats &&
          stats.map((stat, index) => (
            <div
              className={`flex w-full flex-col gap-2 ${
                Object.keys(stat)[0] !== "Response Rate" ? "flex" : "hidden"
              } `}
              key={index}
            >
              <p className=" text-[#414651] text-[0.875rem] font-medium leading-[142.875%]">
                {Object.keys(stat)[0]}
              </p>
              <div className="flex w-full">
                <div
                  className={`flex rounded-l-lg h-[1.5rem] bg-blue-700 `}
                  style={{
                    minWidth: "2.5rem",
                    width: `${Object.values(stat)[0]}%`,
                  }}
                >
                  <span className="text-white pl-3">
                    {" "}
                    {Object.values(stat)[0]}%
                  </span>
                </div>
                <div
                  className={`flex rounded-r-lg h-[1.5rem] bg-blue-200   `}
                  style={{ width: `${100 - Object.values(stat)[0]}%` }}
                ></div>
              </div>
            </div>
          ))}
        <div className="flex w-full flex-col gap-2">
          <p className=" text-[#414651] text-[0.875rem] font-medium leading-[142.875%]">
            Average Response Time (In minutes)
          </p>
          <div className="flex w-full">
            <div
              className="flex rounded-l-lg h-[1.5rem] bg-blue-700"
              style={{
                minWidth: "2.5rem",
                width: `${fill}%`,
              }}
            >
              <p className="text-white pl-3">{averageResponseTime} mins</p>
            </div>
            <div
              className="flex rounded-r-lg h-[1.5rem] bg-blue-200"
              style={{ width: `${100 - fill}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyStatsChart;
