import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useFormikContext, getIn } from "formik";

const CalendarDropdownField = ({
  name,
  label,
  required,
  placeholder,
  errorMessage,
  minDate,
  maxDate,
  width = "w-[325px]",
  showTimeSelect = false,
}) => {
  const { setFieldValue, values, handleBlur } = useFormikContext();

  const selectedDate = getIn(values, name)
    ? new Date(getIn(values, name))
    : null;

  return (
    <div className="relative w-full">
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <DatePicker
        id={name}
        selected={selectedDate}
        onChange={(date) => {
          if (date) {
            const formatted = date.toISOString(); // Store full ISO with time
            setFieldValue(name, formatted);
          }
        }}
        onBlur={handleBlur}
        placeholderText={placeholder || "YYYY-MM-DD"}
        className={`w-[100%] border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500`}
        dateFormat={showTimeSelect ? "yyyy-MM-dd h:mm aa" : "yyyy-MM-dd"}
        minDate={minDate}
        maxDate={maxDate}
        showTimeSelect={showTimeSelect}
        timeIntervals={30}
        popperPlacement="bottom-end"
        calendarClassName="text-sm"
      />

      {errorMessage && (
        <p className="text-sm text-red-500 mt-1">{errorMessage}</p>
      )}
    </div>
  );
};

export default CalendarDropdownField;
