const mongoose = require("mongoose");

const workOnRequestSchema = new mongoose.Schema(
  {
    job: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Job",
        required: true,
      },
      jobId: {
        type: String,
        required: true,
      },
    },
    status: {
      type: String,
      enum: ["requestToWork", "accepted", "rejected", "revokeRequest"],
      default: "requestToWork",
    },
    statusLogs: [
      {
        from: String,
        to: String,
        changedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        changedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    recruiter: {
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
      },
      userId: {
        type: String,
        required: true,
      },
    },
  },
  {
    timestamps: true,
  }
);

const workOnRequest = mongoose.model("WorkRequest", workOnRequestSchema);

module.exports = { workOnRequest };
