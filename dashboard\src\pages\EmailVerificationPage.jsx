import React from "react";
import { useSearchParams } from "react-router-dom";
import EmailStatusCard from "../components/common/cards/EmailStatusCard";
import { resendVerification } from "../services/operations/userAPI";

const EmailVerificationPage = () => {
  const [searchParams] = useSearchParams();
  const userId = searchParams.get("userId");

  const handleResend = async () => {
    try {
      await resendVerification({ userId: userId });
    } catch (err) {
      console.error("Error resending verification email:", err);
    }
  };

  return (
    <EmailStatusCard
      title="Verify Your Email to Activate Your Account"
      image="/assets/images/verify-email.svg"
      message={
        <>
          We've sent a verification link to your email. Please check your inbox
          and click the link to activate your account.
          <div className="mt-4 text-sm text-gray-700">
            Didn&apos;t receive an email?{" "}
            <button
              onClick={handleResend}
              className="text-green-700 font-semibold underline hover:text-green-900 cursor-pointer"
              type="button"
            >
              Send again
            </button>
          </div>
        </>
      }
      footer={true}
    />
  );
};

export default EmailVerificationPage;
