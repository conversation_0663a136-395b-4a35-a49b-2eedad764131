import { useState } from "react";
import EmptyState from "../../components/common/EmptyState";

const Candidates = () => {
  const [candidates, setCandidates] = useState([]);
  // API integration ready - endpoint: /candidate/getallcandidate

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Candidates</h1>
        <p className="text-gray-600 mt-1">View and manage all candidates in the system</p>
      </div>

      {/* Data validation - show content if data exists, otherwise show EmptyState */}
      {candidates.length > 0 ? (
        <div className="bg-white rounded-lg shadow">
          {/* Table or content will go here when API is connected */}
          <div className="p-4">
            {candidates.map((candidate, index) => (
              <div key={index} className="border-b py-2">
                <p className="font-medium">{candidate.name}</p>
                <p className="text-sm text-gray-600">{candidate.email}</p>
                <p className="text-sm text-gray-500">{candidate.candidateID}</p>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <EmptyState
          title="No Candidates Found"
          description="There are no candidates to display at the moment. Candidates will appear here once they are added by recruiters."
        />
      )}
    </div>
  );
};

export default Candidates;
