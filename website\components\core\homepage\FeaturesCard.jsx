import Image from "next/image";

const FeaturesCard = ({ size, title, description, icon }) => {
  return (
    <div
      className={`bg-white py-4 px-7 flex flex-col justify-center gap-3 rounded-2xl ${
        size === "large" ? "h-[31.25rem]" : "h-[23.75rem]"
      } md:w-[15rem] lg:w-[18rem] xl:w-[24rem]`}
    >
      <Image src={icon} alt={title} />
      <h3 className="text-[1.25rem] md:text-[1.5rem] lg:text-[1.75rem] ">
        {title}
      </h3>
      <p className=" font-light">{description}</p>
    </div>
  );
};

export default FeaturesCard;
