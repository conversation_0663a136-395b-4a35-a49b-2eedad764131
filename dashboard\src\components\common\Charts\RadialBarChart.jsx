import React, { Suspense } from "react";

const LazyApexChart = React.lazy(() => import("./LazyApexChart"));

const RadialBarChart = ({
  series = [],
  labelNames = [],
  height = 220,
  countTotal,
  isCountVisible = true,
  colorCode = ["#2563EB", "#F97316", "#16A34A", "#A855F7", "#EC4899"],
}) => {
  const totalAmount = countTotal
    ? countTotal
    : series.reduce((acc, val) => acc + val, 0);

  const percentages = series.map((val) =>
    parseFloat(((val / totalAmount) * 100).toFixed(1))
  );
  const dynamicLabels = labelNames;

  const options = {
    chart: {
      type: "radialBar",
      height: 220,
    },
    plotOptions: {
      radialBar: {
        hollow: {
          margin: 5,
          size: "40%",
          background: "transparent",
        },
        track: {
          background: "#eee",
          strokeWidth: "80%",
          margin: 4,
        },
        dataLabels: {
          show: true,
          name: {
            show: true,
            fontSize: "22px",
          },
          value: {
            formatter: () => " ",
            fontSize: "20px",
            fontWeight: 600,
            color: "#000",
            offsetY: 8,
          },
          total: {
            show: true,
            label: "Total",
            formatter: () => totalAmount,
          },
        },
      },
    },
    labels: dynamicLabels,
    colors: colorCode,
    legend: {
      show: true,
      position: "right",
      fontSize: "13px",
      markers: {
        width: 10,
        height: 10,
        radius: 5,
      },
      itemMargin: {
        vertical: 4,
      },
      formatter: function (seriesName, opts) {
        const index = opts.seriesIndex;
        return isCountVisible
          ? `${seriesName} <span class="text-[12px] font-bold m-2 rounded-sm p-1 bg-[#FEEFC7] text-[#F79009] whitespace-nowrap">${series[index]}</span>`
          : seriesName;
      },
    },
    stroke: {
      lineCap: "round",
    },
  };

  return (
    <Suspense fallback={<div>Loading chart...</div>}>
      <LazyApexChart
        options={options}
        series={percentages}
        type="radialBar"
        height={height}
      />
    </Suspense>
  );
};

export default RadialBarChart;
