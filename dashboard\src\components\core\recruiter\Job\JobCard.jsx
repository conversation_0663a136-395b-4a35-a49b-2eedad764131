import Pills from "../../../common/Job/Pills";
import ResumeStatCard from "./ResumeStatCard";
import JobButton from "../../../common/Button/JobButton";
import { getRelativeTime } from "../../../../utils/RelativeTimeFormatter";

import { useNavigate } from "react-router-dom";
import Modal from "../../../common/Modal/Modal";
import AppButton from "../../../common/Button/AppButton";
import { toast } from "react-toastify";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateUserCoins } from "../../../../redux/reducer/auth.slice";

const JobCard = ({
  item,
  setBookMark,
  deleteBookMark,
  setJobs,
  jobType,
  selectJobToWork,
  unMappedJobs,
  workOnRequestUpdate,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userInfo = useSelector((state) => state.auth?.user);
  const [instantSubmitModel, setInstantSubmitModel] = useState(false);
  const [selectedType, setSelectedType] = useState("");

  const handleWorkOn = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      const data = await selectJobToWork(item?._id, "workOn");
      setJobs(data?.results);

      if (data?.user?.coinBalance !== undefined) {
        dispatch(updateUserCoins(data.user.coinBalance));
      } else {
        const newCoinBalance = userInfo.coinBalance - 1;
        dispatch(updateUserCoins(newCoinBalance));
      }

      toast.success(
        "Now you are working on this job. Job is listed under 'Working On' tab."
      );
    } catch (error) {
      console.error("Error working on job:", error);
      toast.error("Failed to work on job. Please try again.");
    }
  };

  const handleInstantSubmit = () => {
    if (selectedType === "new") {
      navigate(
        `/candidate/addcandidate?jobId=${item?.jobId}&isInstantSubmit=true`
      );
    } else if (selectedType === "existing") {
      navigate(
        `/jobs/select-Existing-Candidate?jobId=${item?.jobId}&isInstantSubmit=true`
      );
    }

    setInstantSubmitModel(false);
  };

  return (
    <>
      <div
        className="bg-white flex pt-6 flex-col self-stretch rounded-lg border border-solid border-[#E2E8F0] hover:border-[#3E9900] cursor-pointer"
        style={{
          boxShadow:
            "0px 1px 2px -1px rgba(0, 0, 0, 0.10), 0px 1px 3px 0px rgba(0, 0, 0, 0.10)",
        }}
        onClick={(e) => {
          e.preventDefault();
          navigate(`/jobs/jobdetails?tab=job-details&jobId=${item?.jobId}`);
        }}
      >
        <div className="flex flex-col items-center gap-6 self-stretch">
          <div className=" flex px-6 justify-between items-center self-stretch">
            <div className="flex flex-col gap-3.5 w-[70%]">
              <div className="flex items-center gap-3 self-stretch w-full">
                <div className="flex flex-col justify-center gap-2">
                  <div className=" flex items-center gap-2">
                    <div className="flex  items-center justify-center h-[2.8rem] w-[2.8rem] p-[0.18rem] gap-2.5 rounded-lg bg-[#F1F4F9]">
                      <img
                        src="/assets/icons/favIcon.svg"
                        alt="Company Logo"
                        className="h-[2.8rem] w-[2.8rem] object-contain flex-shrink-0"
                      />
                    </div>
                    <h3 className="text-[#1E2A3B] font-medium leading-[150%]">
                      {item?.jobTitle}
                      <span className=" text-[#64748B] font-normal">
                        {" "}
                        (ID:{item?.jobId}){" "}
                      </span>
                    </h3>
                    <div className="w-[1.5rem] h-[1.5rem]">
                      <img
                        src="/assets/icons/notificationsIcon.svg"
                        alt=""
                        className=" w-[1.5rem] h-[1.5rem] object-contain flex-shrink-0"
                      />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex gap-2">
                      <div>
                        <img src="/assets/icons/location.svg" alt="" />
                      </div>

                      <h4 className="text-[#64748B] text-[0.875rem] leading[142.857rem]">
                        {item?.location?.city},{item?.location?.state},
                        {item?.location?.country?.split("-")[0]}
                      </h4>
                    </div>
                    <div className="flex gap-2">
                      <div>
                        <img src="/assets/icons/email.svg" alt="" />
                      </div>
                      <h4 className="text-[#64748B] text-[0.875rem]">
                        {item?.accountManager?.email}
                      </h4>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.jobType == "full-time" ? (
                      <Pills title={"Full Time"} />
                    ) : (
                      <Pills title={"Contractual"} />
                    )}
                    {item.payRate && <Pills title={`${item.payRate}`} />}
                    {item.openings && (
                      <Pills title={`${item.openings} Openings`} />
                    )}
                    {item.experience && (
                      <Pills
                        title={`${item?.experience?.min}-${item?.experience?.max} Years`}
                      />
                    )}
                    {item.salary && (
                      <Pills
                        title={`${item?.salary?.min}-${item?.salary?.max} ${item?.salary?.currency}`}
                      />
                    )}
                    <Pills
                      title={`${item?.commission?.amount} Commission`}
                      type="commission"
                    />
                    <Pills
                      title={`${item?.priority
                        ?.split(" ")
                        .map(
                          (keyItem) =>
                            keyItem.slice(0, 1).toUpperCase() +
                            keyItem.slice(1).toLowerCase()
                        )
                        .join(" ")}`}
                      type="highPriority"
                    />
                  </div>
                  <div className=" flex flex-col gap-0.5">
                    <p className=" text-[#475569] text-[0.75rem] ms-1 leading-[150%] font-semibold">
                      Overall Recruiter Submission
                    </p>
                    <div className="flex items-center gap-2 w-full">
                      <ResumeStatCard
                        title={"Resume Submitted"}
                        stat={
                          item.submission.candidate
                            ?.filter((item) =>
                              [
                                "submitted",
                                "reviewing",
                                "submitted to client",
                                "selected",
                                "interviewing",
                                "reject after interview",
                                "awaiting offer",
                                "rejected",
                                "offer released",
                                "offer accepted",
                                "offer rejected",
                                "hired-under guarantee period",
                                "guarantee period not completed",
                                "guarantee period completed",
                              ].includes(item?.status)
                            )
                            .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0
                        }
                      />
                      <p>|</p>
                      <ResumeStatCard
                        title={"Resume Accepted"}
                        stat={
                          item.submission.candidate
                            ?.filter((item) =>
                              [
                                "selected",
                                "interviewing",
                                "reject after interview",
                                "awaiting offer",
                                "rejected",
                                "offer released",
                                "offer accepted",
                                "offer rejected",
                                "hired-under guarantee period",
                                "guarantee period not completed",
                                "guarantee period completed",
                              ].includes(item?.status)
                            )
                            .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0
                        }
                      />
                      <p>|</p>
                      <ResumeStatCard
                        title={"Recruiter Working"}
                        stat={item?.recruiterCount?.count || 0}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="h-[90%] my-auto flex items-center justify-center">
              <div className=" w-0.5 h-[9rem] bg-[#CBD4E1]"></div>
            </div>
            <div
              className={`w-[25%] flex flex-col ${
                ["workingon"].includes(jobType) ? "gap-2" : "gap-4"
              } items-center`}
            >
              {jobType == "workingon" ? (
                <>
                  <h3 className="text-md text-start font-semibold text-gray-700 w-full">
                    My Submission
                  </h3>
                  {[
                    {
                      label: "No. of Candidate",
                      value:
                        item?.submission?.candidateStatus
                          ?.filter((item) =>
                            [
                              "submitted",
                              "selected",
                              "interviewing",
                              "awaiting offer",
                              "offer released",
                              "offer accepted",
                              "hired-under guarantee period",
                              "guarantee period completed",
                            ].includes(item?.status)
                          )
                          .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0,
                    },
                    {
                      label: "Selected Candidate",
                      value:
                        item?.submission?.candidateStatus
                          ?.filter((item) =>
                            [
                              "selected",
                              "interviewing",
                              "awaiting offer",
                              "offer released",
                              "offer accepted",
                              "hired-under guarantee period",
                              "guarantee period completed",
                            ].includes(item?.status)
                          )
                          .reduce((sum, s) => sum + (s?.count ?? 0), 0) || 0,
                    },
                    {
                      label: "Rejected Candidate",
                      value:
                        item?.submission?.candidateStatus?.filter(
                          (item) => item?.status == "rejected"
                        )[0]?.count || 0,
                    },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center w-full">
                      <div className="flex items-center gap-2 w-1/2">
                        <img
                          src="/assets/icons/Container.svg"
                          className="w-4 h-4 text-gray-500"
                          alt="Container Icon"
                        />
                        <span className="whitespace-nowrap">{item.label}</span>
                        <span className="text-gray-500">:</span>
                        <span className="font-medium text-neutral-700">
                          {item.value}
                        </span>
                      </div>
                    </div>
                  ))}

                  <JobButton
                    variant="danger"
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const data = await unMappedJobs(item?._id);
                      setJobs(data?.results);
                    }}
                    label="Unmap me"
                  />
                </>
              ) : jobType == "workonrequest" ? (
                <>
                  <JobButton
                    variant="secondary"
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const data = await workOnRequestUpdate(
                        item?._id,
                        "accepted"
                      );
                      setJobs(data?.results);
                    }}
                    icon="/assets/icons/accept_icons.svg"
                    label="Accept"
                  />
                  <JobButton
                    variant="danger-500"
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const data = await workOnRequestUpdate(
                        item?._id,
                        "rejected"
                      );
                      setJobs(data?.results);
                    }}
                    icon="/assets/icons/cross_icons.svg"
                    label="Reject"
                  />
                </>
              ) : (
                <>
                  <JobButton
                    variant="primary"
                    onClick={handleWorkOn}
                    icon="/assets/icons/coin.svg"
                    label="Work On 1"
                  />
                  <JobButton
                    variant="secondary"
                    onClick={async (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setInstantSubmitModel(true);
                    }}
                    icon="/assets/icons/coin.svg"
                    label="Instant Submit 2"
                  />
                </>
              )}
            </div>
          </div>
          <div className="flex justify-between self-stretch items-center py-[0.4375rem] px-[1.125rem]  rounded-b-[0.4375rem] bg-[#F7F7F7] gap-2.5">
            <p className="text-[#64748B] text-[0.875rem]">
              Posted {getRelativeTime(item?.createdAt)}
            </p>
            {!item?.isBookmark ? (
              <img
                src="/assets/icons/save.svg"
                alt="bookmark"
                className="cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setBookMark(item?._id);

                  setJobs((prevJobs) =>
                    prevJobs.map((jobItem) => {
                      if (jobItem._id === item?._id) {
                        return { ...jobItem, isBookmark: !item?.isBookmark };
                      }
                      return jobItem;
                    })
                  );
                }}
              />
            ) : (
              <img
                src="/assets/icons/Bookmark-fill.svg"
                alt="bookmark fill"
                className="cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  deleteBookMark(item?._id);
                  setJobs((prevJobs) =>
                    prevJobs.map((jobItem) => {
                      if (jobItem._id === item?._id) {
                        return { ...jobItem, isBookmark: !item?.isBookmark };
                      }
                      return jobItem;
                    })
                  );
                }}
              />
            )}
          </div>
        </div>
      </div>
      <Modal
        title={"Are you sure want to add the job with an instant submission?"}
        isOpen={instantSubmitModel}
        onClose={() => {
          setInstantSubmitModel(false);
        }}
      >
        <div className="px-5  pb-4">
          <div className="text-sm text-blue-600 font-medium mb-4 text-start">
            NOTE: By selecting instant submit, you’ll spend 2 coins.
          </div>

          <div className="mb-4">
            <h3 className="text-mb font-semibold text-center mb-1">
              Select User Type
            </h3>
            <p className="text-gray-500 text-sm text-center mb-4">
              Select the type of candidate you want to add for instant
              submission.
            </p>
          </div>

          <div className="flex gap-4 mb-6">
            {/* New Candidate Card */}
            <div
              className={`border rounded-md p-4 flex-1 hover:border-blue-500 cursor-pointer text-center ${
                selectedType === "new"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-300"
              }`}
              onClick={() => setSelectedType("new")}
            >
              <img
                src="/assets/icons/add_candidate.svg"
                alt="New Candidate Icon"
                className="w-8 h-8 mx-auto mb-2"
              />
              <div className="font-medium mb-1">New Candidate</div>
              <div className="text-sm text-gray-500">
                Select this option to submit a new candidate
              </div>
            </div>

            {/* Existing Candidate Card */}
            <div
              className={`border rounded-md p-4 flex-1 cursor-pointer hover:border-blue-500 text-center ${
                selectedType === "existing"
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-300"
              }`}
              onClick={() => setSelectedType("existing")}
            >
              <img
                src="/assets/icons/add_candidate.svg"
                alt="New Candidate Icon"
                className="w-8 h-8 mx-auto mb-2"
              />
              <div className="font-medium mb-1">Existing Candidate</div>
              <div className="text-sm text-gray-500">
                Select this option to submit an existing candidate
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <AppButton
              label={"Cancel"}
              onClick={() => setInstantSubmitModel(false)}
              variant="secondary"
            />

            <AppButton
              label={"Next"}
              onClick={handleInstantSubmit}
              disabled={!selectedType}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default JobCard;
