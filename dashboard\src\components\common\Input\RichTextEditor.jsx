import React from "react";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

const TextAreaInput = ({
  label,
  required = false,
  placeholder,
  disable = false,
  value,
  errorMessage,
  onChange,
  row = 3,
  css = "rounded-lg",
}) => {
  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      [{ color: [] }, { background: [] }],
      ["bold", "italic", "underline"],
      [{ list: "bullet" }, { list: "ordered" }],
      ["link"],
      ["clean"],
    ],
  };

  const formats = [
    "header",
    "color",
    "background",
    "bold",
    "italic",
    "underline",
    "list",
    "bullet",
    "link",
  ];

  return (
    <>
      <div className="flex flex-col">
        {label && (
          <label className="text-sm font-medium text-gray-700">
            {label} {required && <span className="text-red-800">*</span>}
          </label>
        )}
        <ReactQuill
          theme="snow"
          value={value}
          onChange={onChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          className={css}
        />

        {errorMessage}
      </div>
    </>
  );
};

export default TextAreaInput;
