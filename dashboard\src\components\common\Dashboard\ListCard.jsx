import React from "react";
const ListCard = ({
  value = "",
  varient = "update",
  time = "",
  count,
  onClick,
}) => {
  return (
    <div className="flex items-center gap-3 cursor-pointer" onClick={onClick}>
      <div>
        {varient == "update" ? (
          <img alt={"update"} src={"/assets/icons/finance_icons.svg"} />
        ) : (
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: "#414651" }}
          ></div>
        )}
      </div>
      <div>
        <div className="text-[14px] text-[#252B37]">{value}</div>
        {varient == "update" && (
          <div className="text-[12px] text-[#717680]">{time}</div>
        )}
      </div>
      {varient !== "update" && varient !== "submissioncount" && (
        <div className="text-[12px] text-[#717680] text-nowrap">{time}</div>
      )}

      {varient == "submissioncount" && (
        <div className="text-[12px] font-bold m-2 rounded-sm p-1 bg-[#FEEFC7] text-[#F79009] text-nowrap">
          {count}
        </div>
      )}
    </div>
  );
};

export default ListCard;
