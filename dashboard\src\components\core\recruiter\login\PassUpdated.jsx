import React from "react";
import { useNavigate } from "react-router-dom";
import SignUpRightSection from "../../../common/SignUpRightSection";

const PassUpdated = () => {
  const navigate = useNavigate();

  return (
    <section className="w-[100%] md:w-[60%] lg:w-[50%] py-15 flex items-center justify-center">
      <div className="w-[80%] md:w-[60%] lg:w-[45%] mx-auto flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <img
            src="/assets/images/icon.png"
            alt="icon"
            width={50}
            height={100}
          />
          <h2 className="text-4xl font-bold">Password Updated Successfully!</h2>
          <p className="text-gray-600 text-sm leading-5">
            You may now login with your updated password.
          </p>
        </div>
        <button
          type="button"
          className="w-full py-2 px-3 rounded-md bg-[#65FF00] text-[#102108] font-medium cursor-pointer"
          onClick={() => navigate("/login")}
        >
          Login
        </button>
      </div>
      <SignUpRightSection />
    </section>
  );
};

export default PassUpdated;
