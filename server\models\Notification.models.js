const mongoose = require("mongoose");

const notificationSchema = new mongoose.Schema(
  {
    recipients: {
      type: [mongoose.Schema.Types.ObjectId],
      required: true,
      ref: "User",
    },
    title: {
      type: String,
    },
    message: {
      type: String,
    },
    type: {
      type: String,
      enum: [
        "job-assigned",
        "submission",
        "message-job",
        "message-candidate",
        "job-update",
        "coin-earn",
        "work-on-request",
        "custom",
      ],
      default: "custom",
    },
    isRead: {
      type: [mongoose.Schema.Types.ObjectId],
      ref: "User",
    },
    relatedJobId: {
      type: [String],
      ref: "Job",
    },
    relatedCandidateId: {
      type: [String],
      ref: "candidateSubmission",
    },
  },
  {
    timestamps: true,
    strict: "throw",
  }
);

notificationSchema.pre("save", function (next) {
  if (!this.message) {
    switch (this.type) {
      case "job-assigned":
        const jobCount = this.relatedJobId?.length || 0;
        this.message = `${jobCount} job${jobCount === 1 ? "" : "s"} assigned`;
        break;
      case "submission":
        const candidateCount = this.relatedCandidateId?.length || 0;
        this.message = this.message
          ? this.message
          : `${
              candidateCount === 1
                ? "A new candidate submission"
                : `${candidateCount} new candidate submissions`
            }`;
        break;
      case "message-job":
        this.message = "You have received a new message regarding a job.";
        break;
      case "message-candidate":
        this.message = "You have received a new message regarding a candidate.";
        break;
      case "job-update":
        this.message = "A job you are working on has been updated.";
        break;
      case "coin-earn":
        this.message = this.message || "Congratulations! You've earned coins.";
        break;
      case "work-on-request":
        const workonrequest = this.relatedJobId?.length || 0;
        this.message = this.message
          ? this.message
          : `${workonrequest} new work on request${
              workonrequest === 1 ? "" : "s"
            } have been sent to you.`;
        break;
      case "custom":
      default:
        this.message = this.message || "";
        break;
    }
  }
  next();
});

const Notification = mongoose.model("Notification", notificationSchema);

module.exports = { Notification };
