import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import SignUpRightSection from "../../../common/SignUpRightSection";
import {
  resetPassword,
  verifyPasswordResetToken,
} from "../../../../services/operations/authAPI";
import { toast } from "react-toastify";

const NewPassword = () => {
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [sent, setSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState(null);
  const [resetPasswordToken, setResetPasswordToken] = useState("");
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");

  const navigate = useNavigate();

  useEffect(() => {
    const verifyToken = async () => {
      try {
        const res = await verifyPasswordResetToken(token);

        setResetPasswordToken(res.token);

        setTokenValid(true);
      } catch (err) {
        setTokenValid(false);
        toast.error(err?.response?.data?.message || "Invalid or expired link.");
      }
    };
    if (token) verifyToken();
    else setTokenValid(false);
  }, [token]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await resetPassword({ token: resetPasswordToken, password });
      setSent(true);

      navigate("/password-Updated");
    } catch (err) {
      toast.error(err?.response?.data?.message || "Failed to reset password.");
    }
    setLoading(false);
  };

  if (tokenValid === null) {
    return <div className="text-center mt-10">Verifying link...</div>;
  }
  if (tokenValid === false) {
    return (
      <div className="text-center text-red-600 mt-10">
        Invalid or expired reset link.
      </div>
    );
  }

  return (
    <section className="w-[100%] md:w-[60%] lg:w-[50%] py-15 flex items-center justify-center">
      <div className="w-[80%] md:w-[60%] lg:w-[45%] mx-auto flex flex-col gap-8">
        <div className="flex flex-col gap-2">
          <img
            src={"/assets/images/icon.png"}
            alt="icon"
            width={50}
            height={100}
          />
          <h2 className="text-4xl font-bold">New Password</h2>
          <p className="text-gray-600 text-sm leading-5">Set new password.</p>
        </div>
        {sent ? (
          <div className="text-green-600 text-center">
            Your password has been reset successfully!
          </div>
        ) : (
          <form
            onSubmit={handleSubmit}
            className="flex flex-col items-center justify-center gap-6"
          >
            <div className="flex flex-col w-full gap-2">
              <label
                htmlFor="password"
                className="text-sm font-medium text-[#374151]"
              >
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="border border-[#D1D5DB] py-2 px-3 rounded-md focus:ring-indigo-500 focus:border-indigo-500 focus:outline-none w-full"
                  placeholder="Password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2"
                  onClick={() => setShowPassword((prev) => !prev)}
                  tabIndex={-1}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  <img
                    src={
                      showPassword
                        ? "/assets/icons/vis.svg"
                        : "/assets/icons/not_vis.svg"
                    }
                    alt={showPassword ? "Hide password" : "Show password"}
                    className="w-5 h-5"
                  />
                </button>
              </div>
            </div>
            <button
              type="submit"
              className={`w-full py-2 px-3 rounded-md ${
                password
                  ? "bg-[#65FF00] text-[#102108] font-medium cursor-pointer"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
              disabled={!password || loading}
            >
              {loading ? "Setting..." : "Set Password"}
            </button>
          </form>
        )}
      </div>
      <SignUpRightSection />
    </section>
  );
};

export default NewPassword;
