import React, { useState } from "react";
import TabNav from "../../components/common/TabNav/TabNav";
import CandidateDetailsPage from "../Recruiter/CandidateDetailsPage";
import Documents from "./Documents";
import Timeline from "./Timeline";
import CandidateCard from "../../components/common/cards/CandidateCard";
import CandidateChatAccountManager from "../../components/common/chats/CandidateChatAccountManager";

const SubmissionDetails = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [submission, setSubmission] = useState(null);
  const tabs = [
    { name: "Details" },
    { name: "Timeline" },
    { name: "Chat" },
    { name: "Documents" },
  ];

  return (
    <div>
      <CandidateCard
        name={
          submission
            ? `${submission[0]?.personalDetails?.firstName} ${submission[0]?.personalDetails?.lastName}`
            : ""
        }
        // recruiter={}
        email={submission ? submission[0]?.personalDetails?.emailAddress : ""}
        candidateStatus={
          submission?.submissions?.status
            ? submission?.submissions?.status
            : "Talent Pool"
        }
      />

      <TabNav nav={tabs} active={activeTab} setActive={setActiveTab} />
      {activeTab === 0 && (
        <CandidateDetailsPage
          getData={(data) => {
            setSubmission(data);
          }}
        />
      )}
      {activeTab === 1 && <Timeline />}
      {activeTab === 2 && (
        <CandidateChatAccountManager
          recruiterId={submission[0]?.createdBy?.userID}
          submission={submission?.submissions}
        />
      )}
      {activeTab === 3 && <Documents />}
    </div>
  );
};

export default SubmissionDetails;
