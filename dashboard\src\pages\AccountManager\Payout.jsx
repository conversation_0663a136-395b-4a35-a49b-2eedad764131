import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import AppButton from "../../components/common/Button/AppButton";
import TableHeader from "../../components/common/Table/TableHeader";
import PaginationFooter from "../../components/common/Table/TableFooter";
import {
  getPayoutDetails,
  updatePayoutDetails,
} from "../../services/operations/payoutAPI";
import ThreeDot from "../../components/common/Button/ThreeDot";
import Modal from "../../components/common/Modal/Modal";
import { toast } from "react-toastify";

const Payout = () => {
  const [active, setActive] = useState(0);
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  const [isUpdating, setIsUpdating] = useState(false);
  const Navigate = useNavigate();

  const tab = [
    { name: <span>Pending Payouts</span>, css: "" },
    { name: <span>All Payouts</span>, css: "" },
  ];

  const pendingColumns = [
    { label: "Job ID", key: "jobid" },
    { label: "Job Title", key: "jobtitle" },
    { label: "Job Type", key: "jobtype" },
    { label: "Candidate Name", key: "candidateName" },
    { label: "Recruiter Name", key: "recruiterName" },
    { label: "Commission", key: "commission" },
    { label: "Guarantee Period", key: "guaranteePeriod" },
    { label: "Pay Rate", key: "payRateAmount" },
    { label: "", key: "Action" },
  ];

  const allColumns = [
    { label: "Payout Date", key: "payoutReleaseDate" },
    { label: "Payout ID", key: "payoutId" },
    { label: "Job ID", key: "jobid" },
    { label: "Job Title", key: "jobtitle" },
    { label: "Candidate Name", key: "candidateName" },
    { label: "Recruiter Name", key: "recruiterName" },
    { label: "Pay Rate", key: "payRateAmount" },
    { label: "Commission", key: "commission" },
    { label: "Status", key: "payoutStatus" },
    { label: "", key: "Action" },
  ];

  const columns = active === 0 ? pendingColumns : allColumns;

  const fetchPayouts = async (page, limit) => {
    setLoading(true);
    try {
      const res = await getPayoutDetails(page, limit);
      if (res?.success) {
        let resultData = res.data || [];

        if (active === 0) {
          resultData = resultData.filter(
            (item) => item.payoutStatus?.toLowerCase() === "pending"
          );
        }

        setData(resultData);
        setTotal(resultData.length);
        setTotalPages(Math.ceil(resultData.length / pageSize));
      } else {
        setData([]);
        setTotal(0);
        setTotalPages(1);
      }
    } catch (err) {
      console.error("Error fetching payout data:", err.message);
      setData([]);
      setTotal(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPayouts(currentPage, pageSize);
    // eslint-disable-next-line
  }, [currentPage, pageSize, active]);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPayout(null);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedPayout(null);
    setEditFormData({});
  };

  // Helper function to format date for input field (YYYY-MM-DD)
  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toISOString().split("T")[0];
  };

  const handleEditClick = (payout) => {
    setSelectedPayout(payout);

    setEditFormData({
      jobid: payout.jobid || "",
      jobtitle: payout.jobtitle || "",
      jobtype: payout.jobtype || "",
      candidateName: payout.candidateName || "",
      recruiterName: payout.recruiterName || "",
      commission: payout.commission || "",
      guaranteePeriod: payout.guaranteePeriod || "",
      payRateAmount: payout.payRateAmount || "",
      payoutStatus: payout.payoutStatus || "",
      payoutReleaseDate: formatDateForInput(payout.payoutReleaseDate) || "",
      bankReference: payout.bankReference || "",
    });
    setIsEditModalOpen(true);
  };

  const handleEditFormChange = (field, value) => {
    setEditFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleUpdatePayout = async () => {
    if (!selectedPayout?.payoutId) return;

    setIsUpdating(true);
    try {
      const response = await updatePayoutDetails(
        selectedPayout.payoutId,
        editFormData
      );
      if (response?.success) {
        // Update the data in the table
        setData((prevData) =>
          prevData.map((item) =>
            item.payoutId === selectedPayout.payoutId
              ? { ...item, ...editFormData }
              : item
          )
        );
        handleCloseEditModal();
        // You might want to show a success message here
        toast.success("Payout updated successfully!");
      }
    } catch (error) {
      console.error("Error updating payout:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "—";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return "—";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
        return "text-green-600";
      case "pending":
        return "text-yellow-600";
      case "rejected":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const modalColumns = [
    [
      {
        key: "payoutReleaseDate",
        label: "Payout Released Date",
        formatter: formatDate,
      },
      { key: "jobid", label: "Job ID" },
      { key: "jobtitle", label: "Job Title" },
    ],
    [
      { key: "candidateName", label: "Candidate Name" },
      { key: "recruiterName", label: "Recruiter" },
      { key: "guaranteePeriod", label: "Guarantee Period" },
    ],
    [
      {
        key: "payRateAmount",
        label: "Pay Rate",
        formatter: formatCurrency,
        highlight: true,
      },
      {
        key: "commission",
        label: "Commission",
        formatter: formatCurrency,
        highlight: true,
      },
      { key: "jobtype", label: "Job Type" },
    ],
    [
      { key: "bankReference", label: "Bank Reference ID" },
      { key: "payoutId", label: "Payout ID" },
    ],
  ];

  const editFormFields = [
    [
      { key: "jobid", label: "Job ID" },
      { key: "jobtitle", label: "Job Title" },
      { key: "jobtype", label: "Job Type" },
    ],
    [
      { key: "candidateName", label: "Candidate Name" },
      { key: "recruiterName", label: "Recruiter Name" },
      { key: "guaranteePeriod", label: "Guarantee Period" },
    ],
    [
      { key: "payRateAmount", label: "Pay Rate", type: "number" },
      { key: "commission", label: "Commission", type: "number" },
      {
        key: "payoutStatus",
        label: "Status",
        type: "select",
        options: ["Pending", "Paid", "Rejected"],
      },
    ],
    [
      { key: "bankReference", label: "Bank Reference ID", isEmpty: true },
      { key: "payoutReleaseDate", label: "Payout Release Date", type: "date" },
    ],
  ];

  return (
    <div>
      <TabNav
        nav={tab}
        active={active}
        setActive={setActive}
        rightSidebar={
          <div className="flex gap-3 mr-3">
            <img
              src={"/assets/icons/search.svg"}
              alt="Search"
              className="w-6 h-8 cursor-pointer"
            />
            <img
              src={"/assets/icons/threedothorizontal.svg"}
              alt="More"
              className="w-6 h-8 cursor-pointer"
            />
          </div>
        }
      />

      <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full bg-white">
          <TableHeader columns={columns.map((col) => col.label)} />
          <tbody>
            {data.length > 0 ? (
              data.map((row, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
                >
                  {columns.map((col) => {
                    if (col.key === "Action") {
                      return (
                        <td key={col.key} className="px-4 py-3 text-start">
                          <ThreeDot
                            dropdownSize="w-32"
                            buttonDropDown={
                              <ul className="py-0" role="none">
                                {row?.payoutStatus !== "Paid" && (
                                  <li>
                                    <div
                                      onClick={(e) => {
                                        e.preventDefault();
                                        handleEditClick(row);
                                      }}
                                      className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-dark700 hover:bg-gray-100"
                                      role="menuitem"
                                    >
                                      <img
                                        src={"/assets/icons/edit.svg"}
                                        alt="Edit Details"
                                      />
                                      Edit Details
                                    </div>
                                  </li>
                                )}

                                <li>
                                  <div
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setSelectedPayout(row);
                                      setIsModalOpen(true);
                                    }}
                                    className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-dark700 hover:bg-gray-100"
                                    role="menuitem"
                                  >
                                    <img
                                      src={"/assets/icons/view.svg"}
                                      alt="View Details"
                                    />
                                    View Details
                                  </div>
                                </li>
                              </ul>
                            }
                          />
                        </td>
                      );
                    } else {
                      if (col.key === "payoutReleaseDate") {
                        return (
                          <td key={col.key} className="px-4 py-3">
                            {formatDate(row[col.key])}
                          </td>
                        );
                      }
                      if (
                        col.key === "commission" ||
                        col.key === "payRateAmount"
                      ) {
                        return (
                          <td key={col.key} className="px-4 py-3">
                            {formatCurrency(row[col.key])}
                          </td>
                        );
                      }
                      return (
                        <td key={col.key} className="px-4 py-3">
                          {row[col.key] ?? "—"}
                        </td>
                      );
                    }
                  })}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length}
                  className="text-center py-6 text-gray-500"
                >
                  No payout records found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <PaginationFooter
        currentPage={currentPage}
        totalPages={totalPages}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
        totalResults={total}
      />

      {/* Payout Details Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={
          selectedPayout ? (
            <div className="flex items-center gap-2 ">
              <span>Payout Details (Status: </span>
              <span
                className={`font-semibold text-green-600 ${getStatusColor(
                  selectedPayout.payoutStatus
                )}`}
              >
                {selectedPayout.payoutStatus || "-"}
              </span>
              <span>, {formatDate(selectedPayout.payoutReleaseDate)})</span>
            </div>
          ) : (
            "Payout Details"
          )
        }
        css="relative p-3 w-full max-w-4xl max-h-full "
      >
        {selectedPayout && (
          <div className="px-4 pb-4">
            <div className="mb-4  p-3 rounded-lg">
              {selectedPayout.payoutStatus?.toLowerCase() === "paid" && (
                <p className="text-sm text-gray-600">
                  Your payout has been processed and should be visible in your
                  bank account within 1-3 business days.
                </p>
              )}
            </div>

            <div className="grid gap-y-6 mb-1">
              {modalColumns.map((columnGroup, groupIndex) => (
                <div
                  key={groupIndex}
                  className="grid md:grid-cols-3 gap-6 w-full"
                >
                  {columnGroup.map((field) =>
                    field.isEmpty ? (
                      <div key={field.key} />
                    ) : (
                      <div key={field.key}>
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {field.label}
                        </p>
                        <p
                          className={`text-sm text-gray-900 ${
                            field.highlight ? "font-semibold" : ""
                          }`}
                        >
                          {field.formatter
                            ? field.formatter(selectedPayout[field.key])
                            : selectedPayout[field.key] || "—"}
                        </p>
                      </div>
                    )
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </Modal>

      {/* Edit Payout Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        title={
          <div className="flex items-center gap-2">
            <span>Edit Payout Details</span>
            {selectedPayout && (
              <span className="text-sm text-gray-500">
                (ID: {selectedPayout.payoutId})
              </span>
            )}
          </div>
        }
        css="relative p-3 w-full max-w-4xl max-h-full"
      >
        {selectedPayout && (
          <div className="px-4 pb-4">
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600">
                Update the payout details below.
              </p>
            </div>

            <div className="grid gap-y-6 mb-6">
              {editFormFields.map((fieldGroup, groupIndex) => (
                <div
                  key={groupIndex}
                  className="grid md:grid-cols-3 gap-6 w-full"
                >
                  {fieldGroup.map((field) => (
                    <div key={field.key}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {field.label}
                      </label>
                      {field.type === "select" ? (
                        <select
                          value={editFormData[field.key] || ""}
                          onChange={(e) =>
                            handleEditFormChange(field.key, e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select {field.label}</option>
                          {field.options.map((option) => (
                            <option key={option} value={option}>
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </option>
                          ))}
                        </select>
                      ) : field.type === "date" ? (
                        <input
                          type="date"
                          value={editFormData[field.key] || ""}
                          onChange={(e) =>
                            handleEditFormChange(field.key, e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-gray-500 focus:border-gray-500"
                        />
                      ) : field.type === "number" ? (
                        <input
                          type="number"
                          step="0.01"
                          value={editFormData[field.key] || ""}
                          onChange={(e) =>
                            handleEditFormChange(field.key, e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-gray-500 focus:border-gray-500"
                          placeholder={`Enter ${field.label.toLowerCase()}`}
                        />
                      ) : (
                        <input
                          type="text"
                          value={editFormData[field.key] || ""}
                          onChange={(e) =>
                            handleEditFormChange(field.key, e.target.value)
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-gray-500 focus:border-gray-500"
                          placeholder={`Enter ${field.label.toLowerCase()}`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
              <AppButton
                onClick={handleCloseEditModal}
                variant="secondary"
                label="Cancel"
                disabled={isUpdating}
              >
                Cancel
              </AppButton>
              <AppButton
                onClick={handleUpdatePayout}
                variant="primary"
                label={isUpdating ? "Updating..." : "Update Payout"}
                disabled={isUpdating}
              >
                {isUpdating ? "Updating..." : "Update Payout"}
              </AppButton>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Payout;
