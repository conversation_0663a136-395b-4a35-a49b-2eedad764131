//* @middlewares/validateFields.js
//* @Route level validator
//* @validate each request data

module.exports = function validateFields(config = {}) {
  return function (req, res, next) {
    const missingFields = [];

    for (const [fieldPath, rules] of Object.entries(config)) {
      if (!rules.required) continue;

      const keys = fieldPath.split(".");
      let value = req.body;

      for (const key of keys) {
        value = value?.[key];
        if (value === undefined || value === null || value === "") {
          missingFields.push(fieldPath);
          break;
        }
      }
    }

    if (missingFields.length > 0) {
      return res.status(400).json({
        error: "Missing required fields",
        fields: missingFields,
      });
    }

    next();
  };
};
