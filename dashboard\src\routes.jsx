import React from "react";

// pages
const AssignRecruiter = React.lazy(() => import("./pages/AssignRecruiter"));
const AddJobs = React.lazy(() => import("./pages/AddJobs"));
const Payouts = React.lazy(() => import("./pages/Recruiter/Payouts"));

// head account manager
const JobsPage = React.lazy(() =>
  import("./pages/HeadAccountManager/JobsPage")
);
const HDashboard = React.lazy(() =>
  import("./pages/HeadAccountManager/Dashboard")
);
const JobDetails = React.lazy(() => import("./pages/JobDetails"));
const AllSubmissions = React.lazy(() =>
  import("./pages/HeadAccountManager/AllSubmissions")
);
const SubmissionDetails = React.lazy(() =>
  import("./pages/HeadAccountManager/SubmissionDetails")
);
const Documents = React.lazy(() =>
  import("./pages/HeadAccountManager/Documents")
);
const Timeline = React.lazy(() =>
  import("./pages/HeadAccountManager/Timeline")
);

// Account Manager
const JobPage = React.lazy(() => import("./pages/AccountManager/JobPage"));
const ADashboard = React.lazy(() => import("./pages/AccountManager/Dashboard"));
const Payout = React.lazy(() => import("./pages/AccountManager/Payout"));

// Recruiters
const Dashboard = React.lazy(() => import("./pages/Recruiter/Dashboard"));
const CoinHistory = React.lazy(() => import("./pages/Recruiter/CoinHistory"));
const Jobs = React.lazy(() => import("./pages/Recruiter/Jobs"));
const Candidate = React.lazy(() => import("./pages/Recruiter/Candidates"));
const AddCandidates = React.lazy(() =>
  import("./pages/Recruiter/AddCandidates")
);
const RecruiterDetails = React.lazy(() =>
  import("./pages/HeadAccountManager/RecruiterDetails")
);
const Recruiters = React.lazy(() =>
  import("./pages/HeadAccountManager/Recruiters")
);
const CandidateDetailsPage = React.lazy(() =>
  import("./pages/Recruiter/CandidateDetailsPage")
);
const AccountManager = React.lazy(() =>
  import("./pages/HeadAccountManager/AccountManager")
);
const ManagersDetails = React.lazy(() =>
  import("./pages/HeadAccountManager/ManagersDetails")
);
const RecruitersList = React.lazy(() =>
  import("./pages/HeadAccountManager/AllRecruiters")
);

const SelectExistingCandidate = React.lazy(() =>
  import("./components/core/recruiter/Job/SelectExistingCandidate")
);

const route = {
  headAccountManager: [
    {
      name: "Dashboard",
      url: "/",
      element: <HDashboard />,
      icon: "/assets/icons/dashboard.svg",
    },
    {
      name: "Jobs",
      url: "/jobs",
      element: <JobsPage />,
      icon: "/assets/icons/jobs.svg",
      nestedRoutes: [
        {
          url: "/jobs/add-job",
          element: <AddJobs />,
        },
        {
          url: "/jobs/editjob",
          element: <AddJobs />,
        },
        {
          url: "/jobs/jobdetails",
          element: <JobDetails />,
        },
        {
          url: "/jobs/assignrecruiter",
          element: <AssignRecruiter />,
        },
      ],
    },
    {
      name: "Recruiters",
      url: "/recruiter",
      element: <RecruitersList />,
      icon: "/assets/icons/recruiter.svg",
      nestedRoutes: [
        { url: "/recruiter/recruiterDetails", element: <RecruiterDetails /> },
      ],
    },
    {
      name: "Managers",
      url: "/manager",
      element: <AccountManager />,
      icon: "/assets/icons/manager.svg",
      nestedRoutes: [
        {
          url: "/manager/managerdetail",
          element: <ManagersDetails />,
        },
      ],
    },
    {
      name: "Submissions",
      url: "/submissions",
      element: <AllSubmissions />,
      icon: "/assets/icons/submission.svg",
      nestedRoutes: [
        {
          url: "/submissions/submissionDetails",
          element: <SubmissionDetails />,
        },
        {
          url: "/submissions/documents",
          element: <Documents />,
        },
        {
          url: "/submissions/timeline",
          element: <Timeline />,
        },
        { url: "/recruiter/recruiterDetails", element: <RecruiterDetails /> },
      ],
    },
    {
      name: "Payout",
      url: "/payout",
      element: <Payout />,
      icon: "/assets/icons/payout.svg",
    },
  ],
  accountManager: [
    {
      name: "Dashboard",
      url: "/",
      element: <ADashboard />,
      icon: "/assets/icons/dashboard.svg",
    },
    {
      name: "Jobs",
      url: "/jobs",
      element: <JobPage />,
      icon: "/assets/icons/jobs.svg",
      nestedRoutes: [
        {
          url: "/jobs/editjob",
          element: <AddJobs />,
        },
        {
          url: "/jobs/jobdetails",
          element: <JobDetails />,
        },
        {
          url: "/jobs/assignrecruiter",
          element: <AssignRecruiter />,
        },
      ],
    },
    {
      name: "Submissions",
      url: "/submissions",
      element: <AllSubmissions />,
      icon: "/assets/icons/submission.svg",
      nestedRoutes: [
        {
          url: "/submissions/submissionDetails",
          element: <SubmissionDetails />,
        },
        {
          url: "/submissions/documents",
          element: <Documents />,
        },
        {
          url: "/submissions/timeline",
          element: <Timeline />,
        },
      ],
    },
    {
      name: "Payout",
      url: "/payout",
      element: <Payout />,
      icon: "/assets/icons/payout.svg",
    },
    // {
    //   name: "Messages",
    //   url: "/messages",
    //   element: <>hello</>,
    //   icon: messages,
    // },
  ],
  recruiter: [
    {
      name: "Dashboard",
      url: "/",
      element: <Dashboard />,
      icon: "/assets/icons/dashboard.svg",
      nestedRoutes: [
        {
          url: "/coin-history",
          element: <CoinHistory />,
        },
      ],
    },
    {
      name: "Jobs",
      url: "/jobs",
      element: <Jobs />,
      icon: "/assets/icons/jobs.svg",
      nestedRoutes: [
        {
          url: "/jobs/jobdetails",
          element: <JobDetails />,
        },
        {
          url: "/jobs/select-Existing-Candidate",
          element: <SelectExistingCandidate />,
        },
        {
          url: "/jobs/editjob",
          element: <AddJobs />,
        },
      ],
    },
    {
      name: "Candidates",
      url: "/candidate",
      element: <Candidate />,
      icon: "/assets/icons/candidate.svg",
      nestedRoutes: [
        {
          url: "/candidate/addcandidate",
          element: <AddCandidates />,
        },
        {
          url: "/candidate/candidatedetails",
          element: <CandidateDetailsPage />,
        },
      ],
    },
    {
      name: "Payout",
      url: "/payout",
      element: <Payouts />,
      icon: "/assets/icons/payout.svg",
    },
  ],
};

export default route;
