const mongoose = require("mongoose");

const recruiterProfileSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ["freelancer", "company"],
    required: true,
    default: "freelancer",
  },
  user: {
    _id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
  },
  linkedin: {
    type: String,
    required: true,
  },
  country: {
    type: String,
  },
  state: {
    type: String,
  },
  city: {
    type: String,
  },
  about: {
    type: String,
  },
  domain: {
    type: [String],
  },
  candidateRole: {
    type: [String],
  },
  resume: {
    type: String,
  },
  isUserEligibileForITAndHealthcare: {
    type: Boolean,
  },
  jobsWorkingOn: [
    {
      jobId: mongoose.Schema.Types.ObjectId,
      status: {
        type: String,
        enum: ["assigned", "removed"],
        required: true,
        default: "assigned",
      },
      assignedBy: {
        type: String,
        enum: ["self", "manager"],
        required: function () {
          return this.status === "assigned";
        },
      },
      isActive: {
        type: Boolean,
        default: true,
      },
      assignedAt: {
        type: Date,
        default: Date.now,
      },
      isSlotEmpty: {
        type: Boolean,
        default: false,
      },
      isInstantSubmit: {
        type: Boolean,
      },
      chatId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Chat",
        default: null,
      },
    },
  ],
  status: {
    type: String,
    enum: ["Active", "Inactive"],
    default: "Active",
  },
});

const recruiterProfile = mongoose.model("recruiter", recruiterProfileSchema);
module.exports = recruiterProfile;
