const User = require("../models/User.models");
const passwordService = require("./passwordService");

// Create a new user
exports.createUser = async (userData) => {
  // Hash password before saving
  
  userData.password = await passwordService.hashPassword(userData.password);

  const user = new User(userData);
  await user.save();

  // Return user without password
  const userObject = user.toObject();
  delete userObject.password;

  return userObject;
};

// Find user by email
exports.findUserByEmail = async (email) => {
  return await User.findOne({ email });
};

// Find user by ID
exports.findUserById = async (id) => {
  return await User.findById(id);
};

// Update user
exports.updateUser = async (id, updateData) => {
  // Hash password if it's being updated
  if (updateData.password) {
    updateData.password = await passwordService.hashPassword(
      updateData.password
    );
  }

  return await User.findByIdAndUpdate(id, updateData, { new: true }).select(
    "-password"
  );
};

// Verify user password
exports.verifyPassword = async (user, password) => {
  return await passwordService.comparePassword(password, user.password);
};

// Update user token version (for forced logout)
exports.incrementTokenVersion = async (userId) => {
  const user = await User.findById(userId);
  if (!user) {
    throw new Error("User not found");
  }

  user.tokenVersion += 1;
  await user.save();

  return user;
};

// Update last login time
exports.updateLastLogin = async (userId,deviceDetails) => {  
  return await User.findByIdAndUpdate(
    userId,
    { lastLogin: Date.now(),deviceInfo:deviceDetails },
    { new: true }
  ).select("-password");
};
