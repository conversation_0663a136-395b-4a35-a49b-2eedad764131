const { candidateSubmission } = require("../models/CandidateSubmission.models");
const Chat = require("../models/Chat");
const recruiterProfile = require("../models/RecruiterProfile.models");
const generateId = require("../utils/genreateId");
const mongoose = require("mongoose");

//* @Desc Create a new chat
//* @Route POST /api/chat/create-chat
//* @Access Private
exports.createChat = async (req, res) => {
  try {
    const user = req.user;

    const userType = user.userType;

    const {
      participants,
      chatFor,
      message,
      jobId,
      submissionId,
      recruiterId,
      accountManagerId,
    } = req.body;

    if (!participants || participants.length === 0 || !chatFor || !message) {
      return res
        .status(400)
        .json({ message: "Please provide all required fields" });
    }

    // Generate a unique chatId
    const chatId = await generateId("CH"); // e.g., CH-2025001

    // Check if chat already exists
    const existingChat = await Chat.findOne({ chatId });
    if (existingChat) {
      return res
        .status(400)
        .json({ success: false, message: "Chat already exists" });
    }

    // Create new chat
    const newChat = await Chat.create({
      chatId,
      participants,
      chatFor,
      messages: [
        {
          senderId: participants[0].userId, // Assuming the first participant is the sender
          content: message,
          timestamp: new Date(),
        },
      ],
      jobId: jobId ? jobId : undefined, // Only include jobId if provided
      submissionId: submissionId ? submissionId : undefined, // Only include submissionId if provided
    });

    if (!newChat) {
      return res
        .status(500)
        .json({ success: false, message: "Failed to create chat" });
    }

    let updateRecruiter = null;
    let updateSubmission = null;

    if (chatFor === "submission") {
      updateSubmission = await candidateSubmission.findOneAndUpdate(
        { _id: new mongoose.Types.ObjectId(submissionId) },
        { $set: { chatId: newChat._id } },
        { new: true }
      );

      if (!updateSubmission) {
        return res.status(400).json({
          success: false,
          message: "Failed to update submission with chatId",
        });
      }
    } else {
      updateRecruiter = await recruiterProfile.findOneAndUpdate(
        { "user._id": new mongoose.Types.ObjectId(recruiterId) },
        {
          $set: {
            "jobsWorkingOn.$[elem].chatId": newChat._id,
          },
        },
        {
          arrayFilters: [{ "elem.jobId": jobId }],
          new: true,
        }
      );
      if (!updateRecruiter) {
        return res.status(400).json({
          success: false,
          message: "Failed to update recruiter",
        });
      }
    }

    res.status(201).json({
      success: true,
      message: "Chat created successfully",
      chat: newChat,
      recruiter: updateRecruiter,
    });
  } catch (error) {
    console.error("Error creating chat:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

//* @Desc Send Message
//* @Route POST /api/chat/send-message
//* @Access Private
exports.sendMessage = async (req, res) => {
  try {
    const { chatId, senderId, content } = req.body;
    const chat = await Chat.findById(chatId);
    if (!chat) {
      return res
        .status(404)
        .json({ success: false, message: "Chat not found" });
    }

    chat.messages.push({ senderId, content });
    await chat.save();

    res
      .status(200)
      .json({ success: true, message: "Message sent successfully" });
  } catch (error) {
    console.error("Error sending message:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

//* @Desc Get Chat details
//* @Route GET /api/chat/chat-details
//* @Access Private
exports.getChatDetails = async (req, res) => {
  try {
    const { chatId } = req.query;

    const chatDetails = await Chat.aggregate([
      {
        $match: { _id: new mongoose.Types.ObjectId(chatId) },
      },
      {
        $unwind: "$messages",
      },
      {
        $lookup: {
          from: "users",
          localField: "messages.senderId",
          foreignField: "_id",
          as: "senderDetails",
        },
      },
      {
        $unwind: {
          path: "$senderDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          createdAt: { $first: "$createdAt" },
          messages: {
            $push: {
              content: "$messages.content",
              timestamp: "$messages.timestamp",
              senderId: "$messages.senderId",
              firstName: "$senderDetails.name.firstName",
              lastName: "$senderDetails.name.lastName",
              userType: "$senderDetails.userType",
            },
          },
        },
      },
    ]);

    if (!chatDetails.length) {
      return res
        .status(404)
        .json({ success: false, message: "Chat not found" });
    }

    res.status(200).json({ success: true, data: chatDetails[0] });
  } catch (error) {
    console.error("Error fetching chat details:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

//* @Desc Get all chats for a user
//* @Route GET /api/chat/user-chats
//* @Access Private
exports.getUserChats = async (req, res) => {
  try {
    const userId = req.user.id; // Get user ID from request
    const chats = await Chat.find({
      participants: { $elemMatch: { userId } },
    });

    if (!chats) {
      return res
        .status(404)
        .json({ success: false, message: "No chats found for this user" });
    }

    res.status(200).json({ success: true, data: chats });
  } catch (error) {
    console.error("Error fetching user chats:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

//* @Desc Get all chats for a job
//* @Route GET /api/chat/job-chats
//* @Access Private
exports.getJobChats = async (req, res) => {
  try {
    const { jobId } = req.query;
    const chats = await Chat.find({ jobId });

    if (!chats || chats.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "No chats found for this job" });
    }

    res.status(200).json({ success: true, data: chats });
  } catch (error) {
    console.error("Error fetching job chats:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

//* @Desc Get all chats for a candidate
//* @Route GET /api/chat/candidate-chats
//* @Access Private
exports.getCandidateChats = async (req, res) => {
  try {
    const { candidateId } = req.query;
    const chats = await Chat.find({ candidateId });

    if (!chats || chats.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "No chats found for this candidate" });
    }

    res.status(200).json({ success: true, data: chats });
  } catch (error) {
    console.error("Error fetching candidate chats:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};
