import { Navigate } from "react-router-dom";
import { useEffect, useState } from "react";
import Layout from "./layout/Layout";
import { loginUserInfo } from "./services/operations/authAPI";
import { useDispatch } from "react-redux";
import { userInfo } from "./redux/reducer/auth.slice";

const ProtectedRoute = () => {
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    const verifyUser = async () => {
      try {
        const response = await loginUserInfo();
        dispatch(userInfo({ ...response.data }));
        setAuthorized(true);
      } catch (err) {
        // try {
        // //   const tokenRes = await refreshToken();
        //   const profileRes = await loginUserInfo();
        // //   setUser(profileRes.data);
        //   setAuthorized(true);
        // } catch (refreshErr) {
        setAuthorized(false);
        // }
      } finally {
        setLoading(false);
      }
    };

    verifyUser();
  }, []);

  if (loading) return <div>Loading...</div>;

  return authorized ? <Layout /> : <Navigate to="/login" />;
};

export default ProtectedRoute;
