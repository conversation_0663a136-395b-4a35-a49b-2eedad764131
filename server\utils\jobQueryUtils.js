/**
 * Parse filter arrays from query strings
 * @param {string|array} filterValue - The filter value to parse
 * @returns {array|null} - Parsed array or null
 */
const parseFilterArray = (filterValue) => {
  if (!filterValue) return null;

  if (Array.isArray(filterValue)) {
    return filterValue.filter((item) => item && item.trim().length > 0);
  }

  if (typeof filterValue === "string") {
    return filterValue
      .split(",")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }

  return null;
};

/**
 * Enhanced regex escape function
 * @param {string} string - String to escape
 * @returns {string} - Escaped string
 */
const escapeRegex = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

/**
 * Build search conditions for MongoDB query
 * @param {string} searchTerm - The search term
 * @param {string} searchField - The field to search in
 * @returns {object} - MongoDB search conditions
 */
const buildSearchConditions = (searchTerm, searchField) => {
  if (!searchTerm || !searchTerm.trim()) return {};

  const searchValue = searchTerm.toLowerCase().trim();

  // Prevent dangerous regex patterns
  if (searchValue.match(/^[.*+?^${}()|[\]\\]+$/)) {
    // If search contains only regex special characters, return no results
    return { _id: null }; // This will return no results
  }

  // Safely escape the search value
  const searchRegex = new RegExp(escapeRegex(searchValue), "i");

  // Create minimal variations (only format differences)
  const variations = createSafeSearchVariations(searchValue);

  // Global search fields
  const globalSearchFields = [
    { jobTitle: searchRegex },
    { jobId: searchRegex },
    { jobType: searchRegex },
    { industry: searchRegex },
    { clientname: searchRegex },
    { "location.country": searchRegex },
    { "location.state": searchRegex },
    { "location.city": searchRegex },
    { primarySkills: { $in: [searchRegex] } },
    { priority: searchRegex },
    { jobStatus: searchRegex },
    { jobDescription: searchRegex },
  ];

  // Add safe variations
  variations.forEach((variation) => {
    const variationRegex = new RegExp(escapeRegex(variation), "i");
    globalSearchFields.push(
      { jobTitle: variationRegex },
      { jobType: variationRegex },
      { primarySkills: { $in: [variationRegex] } }
    );
  });

  // If no specific field or searching all fields
  if (!searchField || searchField === "all") {
    return { $or: globalSearchFields };
  }

  // Specific field search
  switch (searchField.toLowerCase()) {
    case "jobtitle":
    case "job_title":
      return {
        $or: [
          { jobTitle: searchRegex },
          ...variations.map((v) => ({
            jobTitle: new RegExp(escapeRegex(v), "i"),
          })),
        ],
      };

    case "jobid":
    case "job_id":
      return { jobId: searchRegex }; // Job IDs should be exact

    case "jobtype":
    case "job_type":
      return {
        $or: [
          { jobType: searchRegex },
          ...variations.map((v) => ({
            jobType: new RegExp(escapeRegex(v), "i"),
          })),
        ],
      };

    case "location":
      return {
        $or: [
          { "location.country": searchRegex },
          { "location.state": searchRegex },
          { "location.city": searchRegex },
        ],
      };

    case "industry":
    case "specialization":
      return { industry: searchRegex };

    case "clientname":
    case "client_name":
    case "client":
      return { clientname: searchRegex };

    case "skills":
    case "primaryskills":
    case "primary_skills":
      return {
        $or: [
          { primarySkills: { $in: [searchRegex] } },
          ...variations.map((v) => ({
            primarySkills: { $in: [new RegExp(escapeRegex(v), "i")] },
          })),
        ],
      };

    case "priority":
      return { priority: searchRegex };

    case "submissionscount":
    case "submissions_count":
      try {
        const count = parseInt(searchValue);
        if (!isNaN(count)) {
          return { submissionsCount: count };
        }
      } catch (error) {
        console.error("Error parsing submissionsCount:", error);
      }
      return { submissionsCount: searchRegex };

    case "recruitercount":
    case "recruiter_count":
      try {
        const count = parseInt(searchValue);
        if (!isNaN(count)) {
          return { recruiterCount: count };
        }
      } catch (error) {
        console.error("Error parsing recruiterCount:", error);
      }
      return { recruiterCount: searchRegex };

    case "jobstatus":
    case "job_status":
    case "status":
      return { jobStatus: searchRegex };

    default:
      return { $or: globalSearchFields };
  }
};

/**
 * Create safe search variations (no dangerous patterns)
 * @param {string} searchValue - The original search value
 * @returns {array} - Array of safe search variations
 */
const createSafeSearchVariations = (searchValue) => {
  const variations = [];

  // Skip variations for pure special character searches
  if (searchValue.match(/^[^a-zA-Z0-9\s]+$/)) {
    return [];
  }

  // Only create format variations for meaningful terms
  if (searchValue.length > 1) {
    // Space ↔ Hyphen ↔ Underscore variations
    if (searchValue.includes(" ")) {
      variations.push(searchValue.replace(/\s+/g, "-"));
      variations.push(searchValue.replace(/\s+/g, "_"));
    }

    if (searchValue.includes("-")) {
      variations.push(
        searchValue.replace(/-/g, " ").replace(/\s+/g, " ").trim()
      );
    }

    if (searchValue.includes("_")) {
      variations.push(
        searchValue.replace(/_/g, " ").replace(/\s+/g, " ").trim()
      );
    }

    // Dot ↔ Space variations (only for meaningful terms like "node.js")
    if (searchValue.includes(".") && searchValue.match(/[a-zA-Z0-9]/)) {
      const dotToSpace = searchValue
        .replace(/\./g, " ")
        .replace(/\s+/g, " ")
        .trim();
      if (dotToSpace.length > 0) {
        variations.push(dotToSpace);
      }
    }
  }

  // Remove duplicates and filter out empty/invalid strings
  return [...new Set(variations)].filter(
    (v) => v && v.length > 0 && v !== searchValue
  );
};

/**
 * Build filter conditions
 * @param {object} filters - Filter parameters
 * @returns {object} - MongoDB filter conditions
 */
const buildFilterConditions = (filters) => {
  const conditions = {};

  // Location filter
  if (filters.locations) {
    const locations = parseFilterArray(filters.locations);
    if (locations && locations.length > 0) {
      conditions["location.country"] = {
        $in: locations.map((loc) => new RegExp(escapeRegex(loc), "i")),
      };
    }
  }

  // Job type filter
  if (filters.jobTypes) {
    const jobTypes = parseFilterArray(filters.jobTypes);
    if (jobTypes && jobTypes.length > 0) {
      conditions.jobType = {
        $in: jobTypes.map((type) => new RegExp(`^${escapeRegex(type)}$`, "i")),
      };
    }
  }

  // Specialization filter
  if (filters.specialization) {
    const specializations = parseFilterArray(filters.specialization);
    if (specializations && specializations.length > 0) {
      conditions.industry = {
        $in: specializations.map((spec) => new RegExp(escapeRegex(spec), "i")),
      };
    }
  }

  // Job status filter (for closed jobs - only allow valid closed statuses)
  if (filters.jobStatus) {
    const statuses = parseFilterArray(filters.jobStatus);
    if (statuses && statuses.length > 0) {
      // For closed jobs, only allow filtering within closed job statuses
      if (filters.isClosedJobFilter) {
        const allowedClosedStatuses = ["Cancelled", "Closed", "Filled"];
        const validStatuses = statuses.filter((status) =>
          allowedClosedStatuses.includes(status)
        );

        if (validStatuses.length > 0) {
          conditions.jobStatus = { $in: validStatuses };
        } else {
          conditions.jobStatus = { $in: [] }; // Return no results for invalid statuses
        }
      } else {
        conditions.jobStatus = { $in: statuses };
      }
    }
  }

  // Visibility filter
  if (filters.visibility) {
    const visibilities = parseFilterArray(filters.visibility);
    if (visibilities && visibilities.length > 0) {
      const visibilityConditions = [];

      visibilities.forEach((vis) => {
        const visValue = vis.toLowerCase();
        if (visValue === "publish" || visValue === "published") {
          visibilityConditions.push(true);
        } else if (visValue === "unpublish" || visValue === "unpublished") {
          visibilityConditions.push(false);
        }
      });

      if (visibilityConditions.length > 0) {
        if (visibilityConditions.length === 1) {
          conditions.visibility = visibilityConditions[0];
        } else {
          conditions.visibility = { $in: visibilityConditions };
        }
      }
    }
  }

  return conditions;
};

/**
 * Build sort conditions
 * @param {string} sortBy - Field to sort by
 * @param {string} sortOrder - Sort order (asc/desc)
 * @param {string} postedDate - Date sorting preference
 * @returns {object} - MongoDB sort conditions
 */
const buildSortConditions = (sortBy, sortOrder, postedDate) => {
  const defaultSort = { updatedAt: -1 };

  // Handle postedDate parameter first (highest priority)
  if (postedDate) {
    const dateSort = postedDate.toLowerCase();
    if (["recent", "newest", "latest"].includes(dateSort)) {
      return { createdAt: -1 };
    } else if (["oldest", "earliest"].includes(dateSort)) {
      return { createdAt: 1 };
    }
  }

  if (!sortBy) return defaultSort;

  const sortField = sortBy.toLowerCase();
  const order = sortOrder && sortOrder.toLowerCase() === "asc" ? 1 : -1;

  const sortMappings = {
    posteddate: { createdAt: order },
    posted_date: { createdAt: order },
    createdat: { createdAt: order },
    created_at: { createdAt: order },
    recent: { createdAt: -1 },
    oldest: { createdAt: 1 },
    jobtitle: { jobTitle: order },
    job_title: { jobTitle: order },
    jobtype: { jobType: order },
    job_type: { jobType: order },
    industry: { industry: order },
    specialization: { industry: order },
    priority: { priority: order },
    updatedat: { updatedAt: order },
    updated_at: { updatedAt: order },
  };

  return sortMappings[sortField] || defaultSort;
};

/**
 * Build base match conditions
 * @param {object} user - User object
 * @param {string} jobType - Type of job query (all/unassigned/active/high-priority)
 * @returns {object} - Base MongoDB match conditions
 */
const buildBaseMatchConditions = (user, jobType = "all") => {
  let baseMatch = { isDeleted: false };

  // User-specific conditions
  if (user && user.userType === "accountManager") {
    baseMatch["accountManager.userID"] = user.userId;
  }

  // Job type specific conditions
  switch (jobType) {
    case "unassigned":
      baseMatch.$or = [
        { accountManager: { $exists: false } },
        { "accountManager._id": { $exists: false } },
        { "accountManager._id": null },
        { "accountManager.userID": { $exists: false } },
        { "accountManager.userID": null },
      ];
      break;

    case "active":
      baseMatch.jobStatus = "Active";
      break;

    case "high-priority":
      baseMatch.priority = { $in: ["High", "high priority"] };
      break;

    case "closed":
      baseMatch.jobStatus = { $in: ["Cancelled", "Closed", "Filled"] };
      break;

    case "unengaged":
      baseMatch.jobStatus = "Active";
      baseMatch.visibility = true;
      baseMatch.accountManager = { $exists: true };
      break;

    default:
      // 'all' - no additional conditions
      break;
  }

  return baseMatch;
};

/**
 * Execute aggregation pipeline with optional additional lookups
 * @param {object} model - Mongoose model
 * @param {object} matchConditions - Match conditions
 * @param {object} sortConditions - Sort conditions
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @param {array} additionalStages - Additional aggregation stages
 * @returns {object} - Aggregation results
 */
const executeJobAggregation = async (
  model,
  matchConditions,
  sortConditions,
  page,
  limit,
  additionalStages = []
) => {
  if (page && limit) {
    const skip = (page - 1) * limit;
    const pipeline = [
      { $match: matchConditions },
      ...additionalStages, // Insert additional stages before sorting
      { $sort: sortConditions },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      },
    ];

    const results = await model.aggregate(pipeline);
    const total = results[0]?.metadata[0]?.total || 0;
    const jobs = results[0]?.data || [];

    return {
      jobs,
      total,
      totalPages: Math.ceil(total / limit),
    };
  } else {
    const pipeline = [
      { $match: matchConditions },
      ...additionalStages, // Insert additional stages before sorting
      { $sort: sortConditions },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [],
        },
      },
    ];

    const results = await model.aggregate(pipeline);
    const total = results[0]?.metadata[0]?.total || 0;
    const jobs = results[0]?.data || [];

    return {
      jobs,
      total,
      totalPages: Math.ceil(total / limit),
    };
  }
};

/**
 * Build complex filter conditions with proper $or and $and logic
 * @param {object} baseMatch - Base match conditions
 * @param {object} searchConditions - Search conditions
 * @param {object} filterConditions - Filter conditions
 * @returns {object} - Combined match conditions
 */
const combineConditions = (baseMatch, searchConditions, filterConditions) => {
  let combinedMatch = { ...baseMatch };

  // Add search conditions
  if (searchConditions.$or) {
    combinedMatch.$or = searchConditions.$or;
  } else if (Object.keys(searchConditions).length > 0) {
    Object.assign(combinedMatch, searchConditions);
  }

  // Add filter conditions (only if search doesn't override them)
  if (!searchConditions.$or && Object.keys(filterConditions).length > 0) {
    Object.assign(combinedMatch, filterConditions);
  }

  return combinedMatch;
};

/**
 * Execute complex aggregation pipeline with special match conditions
 * @param {object} model - Mongoose model
 * @param {object} matchConditions - Initial match conditions
 * @param {object} sortConditions - Sort conditions
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @param {array} additionalStages - Additional aggregation stages
 * @param {object} finalMatchConditions - Final match conditions (for unengaged jobs)
 * @returns {object} - Aggregation results
 */
const executeComplexJobAggregation = async (
  model,
  matchConditions,
  sortConditions,
  page,
  limit,
  additionalStages = [],
  finalMatchConditions = null
) => {
  const skip = (page - 1) * limit;

  let pipeline = [
    { $match: matchConditions },
    { $sort: sortConditions },
    ...additionalStages,
  ];

  // Add final match conditions if provided (for unengaged jobs)
  if (finalMatchConditions) {
    pipeline.push({ $match: finalMatchConditions });
  }

  // Add pagination
  pipeline.push({
    $facet: {
      metadata: [{ $count: "total" }],
      data: [{ $skip: skip }, { $limit: limit }],
    },
  });

  const results = await model.aggregate(pipeline);
  const total = results[0]?.metadata[0]?.total || 0;
  const jobs = results[0]?.data || [];

  return {
    jobs,
    total,
    totalPages: Math.ceil(total / limit),
  };
};

module.exports = {
  parseFilterArray,
  escapeRegex,
  buildSearchConditions,
  buildFilterConditions,
  buildSortConditions,
  buildBaseMatchConditions,
  executeJobAggregation,
  executeComplexJobAggregation,
  combineConditions,
};
