const {
  candidateSubmission,
  submissionEnums,
} = require("../models/CandidateSubmission.models");
const job = require("../models/Job.models");
const User = require("../models/User.models");
const { accountManager } = require("../models/AccountManager.models");

//* @Desc get all managers
//* @Route POST /api/v1//headmanager/manager/getallmanager
//* @Access private, head manager
const getAllManager = async (req, res) => {
  try {
    // Extract filter, sorting, and search parameters from query
    const { status, sortBy, sortOrder, postedDate, search, searchField } =
      req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Helper function to parse filter arrays from query strings
    const parseFilterArray = (filterValue) => {
      if (!filterValue) return null;
      if (Array.isArray(filterValue)) {
        return filterValue.filter((item) => item && item.trim().length > 0);
      }
      if (typeof filterValue === "string") {
        return filterValue
          .split(",")
          .map((item) => item.trim())
          .filter((item) => item.length > 0);
      }
      return null;
    };

    // Safe regex escape function
    const escapeRegex = (string) => {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };

    // Build the base match condition (ONLY for fields available in users collection)
    const baseMatchCondition = {
      userType: "accountManager",
    };

    // Function to build status match condition (applied AFTER profile lookup)
    const buildStatusMatchCondition = () => {
      if (!status) return {};

      const statusArray = parseFilterArray(status);
      if (!statusArray || statusArray.length === 0) return {};

      const statusConditions = [];

      statusArray.forEach((stat) => {
        const statusValue = stat.toLowerCase().trim();

        switch (statusValue) {
          case "active":
            // Status is 'Active' and user is active
            statusConditions.push({
              $and: [
                { "profile.status": { $regex: /^active$/i } },
                { isActive: true },
              ],
            });
            break;

          case "inactive":
            // Status is 'Inactive' or isActive is false or missing
            statusConditions.push({
              $or: [
                { "profile.status": { $regex: /^inactive$/i } },
                { isActive: false },
                { isActive: { $exists: false } },
              ],
            });
            break;

          case "onleave":
          case "on leave":
          case "on_leave":
            statusConditions.push({
              "profile.status": { $regex: /^(onleave|on leave|on_leave)$/i },
            });
            break;

          case "pending":
            statusConditions.push({
              "profile.status": { $regex: /^pending$/i },
            });
            break;

          case "suspended":
            statusConditions.push({
              "profile.status": { $regex: /^suspended$/i },
            });
            break;

          case "blocked":
            statusConditions.push({
              "profile.status": { $regex: /^blocked$/i },
            });
            break;

          case "hold":
          case "on hold":
            statusConditions.push({
              "profile.status": { $regex: /^(hold|on hold)$/i },
            });
            break;

          default:
            // Unknown status – match directly on profile.status
            statusConditions.push({
              "profile.status": {
                $regex: new RegExp(`^${escapeRegex(stat)}$`, "i"),
              },
            });
        }
      });

      // Return the combined status condition
      if (statusConditions.length === 1) {
        return statusConditions[0];
      } else if (statusConditions.length > 1) {
        return { $or: statusConditions };
      }
      return {};
    };

    // Build search functionality (applied after profile lookup)
    const buildSearchMatchCondition = () => {
      if (!search || search.trim().length === 0) return {};

      const searchTerm = escapeRegex(search.trim());
      const searchRegex = new RegExp(searchTerm, "i");

      if (searchField && searchField !== "all") {
        // Search in specific field
        switch (searchField.toLowerCase()) {
          case "name":
          case "managername":
          case "manager_name":
            // ✅ FIXED: name is an object with firstName, lastName
            return {
              $or: [
                { "name.firstName": searchRegex },
                { "name.lastName": searchRegex },
                { "name.middleName": searchRegex },
              ],
            };

          case "email":
            // ✅ CORRECT: email is a direct field
            return { email: searchRegex };

          case "phone":
          case "phonenumber":
          case "phone_number":
            // ✅ FIXED: phone is an object with countryCode and number
            return {
              $or: [
                { "phone.number": searchRegex },
                { "phone.countryCode": searchRegex },
              ],
            };

          case "userid":
          case "user_id":
            // ✅ CORRECT: userId is a direct field
            return { userId: searchRegex };

          case "status":
            // ✅ CORRECT: status is in profile object
            return { "profile.status": searchRegex };

          case "profile":
          case "profilename":
            // ❌ ISSUE: There's no profile.name in your data structure
            // The profile has user info but no separate name field
            return {
              $or: [
                { "profile.user.userId": searchRegex },
                { "name.firstName": searchRegex },
                { "name.lastName": searchRegex },
              ],
            };

          case "location":
            // ✅ FIXED: location fields are directly in profile (city, state, country)
            return {
              $or: [
                { "profile.country": searchRegex },
                { "profile.state": searchRegex },
                { "profile.city": searchRegex },
              ],
            };

          case "domain":
          case "specialization":
            // ✅ FIXED: domain is an array in profile
            return { "profile.domain": { $in: [searchRegex] } };

          case "experience":
            // ❌ NOT AVAILABLE: No experience field in your data structure
            return {};

          case "skills":
            // ❌ NOT AVAILABLE: No skills field in your data structure
            return {};

          default:
            // Default to searching name
            return {
              $or: [
                { "name.firstName": searchRegex },
                { "name.lastName": searchRegex },
              ],
            };
        }
      } else {
        // Global search across multiple fields
        return {
          $or: [
            // Name fields (object structure)
            { "name.firstName": searchRegex },
            { "name.lastName": searchRegex },
            { "name.middleName": searchRegex },

            // Direct fields
            { email: searchRegex },
            { userId: searchRegex },

            // Phone fields (object structure)
            { "phone.number": searchRegex },
            { "phone.countryCode": searchRegex },

            // Profile fields
            { "profile.status": searchRegex },
            { "profile.country": searchRegex },
            { "profile.state": searchRegex },
            { "profile.city": searchRegex },
            { "profile.domain": { $in: [searchRegex] } },
            { "profile.user.userId": searchRegex },
          ],
        };
      }
    };

    // Build sorting object
    const buildSortObject = () => {
      const defaultSort = { createdAt: -1 };

      if (postedDate) {
        const dateSort = postedDate.toLowerCase();
        if (
          dateSort === "recent" ||
          dateSort === "newest" ||
          dateSort === "latest"
        ) {
          return { createdAt: -1 };
        } else if (dateSort === "oldest" || dateSort === "earliest") {
          return { createdAt: 1 };
        }
      }

      if (!sortBy) return defaultSort;

      const sortField = sortBy.toLowerCase();
      const order = sortOrder && sortOrder.toLowerCase() === "asc" ? 1 : -1;

      switch (sortField) {
        case "posteddate":
        case "posted_date":
        case "createdat":
        case "created_at":
          return { createdAt: order };
        case "recent":
          return { createdAt: -1 };
        case "oldest":
          return { createdAt: 1 };
        case "name":
        case "managername":
        case "manager_name":
          return { name: order };
        case "email":
          return { email: order };
        case "phone":
          return { phone: order };
        case "status":
          return { "profile.status": order };
        case "userid":
        case "user_id":
          return { userId: order };
        case "recruitercount":
        case "recruiter_count":
          return { totalRecruiterSum: order };
        default:
          console.warn(`Unknown sort field: ${sortField}, using default sort`);
          return defaultSort;
      }
    };

    const sortObject = buildSortObject();

    // CORRECTED: Enhanced aggregation pipeline with proper timing
    const pipeline = [
      // Stage 1: Base filtering (only fields available in users collection)
      {
        $match: baseMatchCondition,
      },
      {
        $lookup: {
          from: "accountmanagers",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // Stage 4: Apply status filtering (NOW profile data is available)
    const statusMatchCondition = buildStatusMatchCondition();
    const searchMatchCondition = buildSearchMatchCondition();

    // Combine status and search conditions
    const combinedMatchConditions = [];
    if (Object.keys(statusMatchCondition).length > 0) {
      combinedMatchConditions.push(statusMatchCondition);
    }
    if (Object.keys(searchMatchCondition).length > 0) {
      combinedMatchConditions.push(searchMatchCondition);
    }

    // Add combined match stage if there are conditions
    if (combinedMatchConditions.length > 0) {
      const combinedMatch =
        combinedMatchConditions.length === 1
          ? combinedMatchConditions[0]
          : { $and: combinedMatchConditions };

      pipeline.push({
        $match: combinedMatch,
      });
    }

    // Stage 5+: Continue with job lookup and calculations
    pipeline.push(
      {
        $lookup: {
          from: "jobs",
          let: {
            userID: "$userId",
            status: "Active",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$accountManager.userID", "$$userID"],
                    },
                    {
                      $eq: ["$jobStatus", "$$status"],
                    },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "_id",
                foreignField: "job._id",
                as: "submittions",
              },
            },
            {
              $addFields: {
                submittionCount: {
                  $size: "$submittions",
                },
              },
            },
            {
              $group: {
                _id: null,
                activeJobCount: { $sum: 1 },
                coverJobCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$submittionCount", 0],
                      },
                      1,
                      0,
                    ],
                  },
                },
                noOfSubmission: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$submittionCount", 0],
                      },
                      "$submittionCount",
                      0,
                    ],
                  },
                },
              },
            },
          ],
          as: "jobDetails",
        },
      },
      {
        $unwind: {
          path: "$jobDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          status: 1,
          isActive: 1,
          activeJobCount: { $ifNull: ["$jobDetails.activeJobCount", 0] },
          coverJobCount: { $ifNull: ["$jobDetails.coverJobCount", 0] },
          noOfSubmission: { $ifNull: ["$jobDetails.noOfSubmission", 0] },
        },
      },
      {
        $sort: sortObject,
      },
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [{ $skip: skip }, { $limit: limit }],
        },
      }
    );

    const results = await User.aggregate(pipeline);

    const totalmanager = results[0].metadata[0]?.total || 0;
    const manager = results[0].data || [];

    const validatedPage = Math.max(1, page);
    const totalPages = Math.ceil(totalmanager / limit);

    res.status(200).json({
      success: true,
      message: "Successfully retrieved account managers",
      total: totalmanager,
      page: validatedPage,
      totalPages,
      limit,
      results: manager,
      appliedFilters: {
        status: parseFilterArray(status),
        search: search || null,
        searchField: searchField || null,
      },
      appliedSorting: {
        sortBy: sortBy || "createdAt",
        sortOrder: sortOrder || "desc",
        postedDate: postedDate || null,
        sortObject: sortObject,
      },
    });
  } catch (error) {
    console.error("Error in getAllManager:", error);
    res.status(500).json({
      success: false,
      message: "Something went wrong while fetching managers",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

//* @Desc Update manager details
//* @Route POST /api/v1/headmanager/manager/update/:managerID
//* @Access Private - head manager
const updateManager = async (req, res) => {
  try {
    const { managerID } = req.params;
    const updateData = req.body;

    // Validate required parameter
    if (!managerID) {
      return res.status(400).json({
        success: false,
        message: "Manager ID is required",
      });
    }

    // Validate update data
    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: "Update data is required",
      });
    }

    // Check if manager exists
    const existingManager = await User.findOne({
      userId: managerID,
      userType: "accountManager",
    });

    if (!existingManager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Separate profile data from user data
    const { profile, ...userData } = updateData;

    // Update User details (excluding profile)
    if (Object.keys(userData).length > 0) {
      await User.updateOne({ userId: managerID }, { $set: userData });
    }

    // Update account manager profile if provided
    if (profile && Object.keys(profile).length > 0) {
      await accountManager.updateOne(
        { "user.userId": managerID },
        { $set: profile }
      );
    }

    return res.status(200).json({
      success: true,
      message: "Manager updated successfully",
    });
  } catch (error) {
    console.error("Error updating manager:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal server error",
    });
  }
};

//* @Desc get all managers
//* @Route POST /api/v1//headmanager/manager/getallmanagerworkhistory
//* @Access private, head manager
const getAllManagerWorkHistory = async (req, res) => {
  try {
    const manager = await User.aggregate([
      {
        $match: {
          userType: "accountManager",
        },
      },
      {
        $lookup: {
          from: "accountmanagers",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "jobs",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$accountManager.userID", "$$userID"],
                },
              },
            },
          ],
          as: "workonJobs",
        },
      },
      {
        $project: {
          profile: 1,
          createdAt: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
          workonJobs: 1,
          workonJobCount: {
            $size: "$workonJobs",
          },
        },
      },
    ]);

    return res.status(200).json({
      message: "get all manager work history",
      success: true,
      data: manager,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: error.message });
  }
};

//* @Desc get managers
//* @Route POST /api/v1//headmanager/manager/getmanager/:managerID
//* @Access private, head manager
const getManager = async (req, res) => {
  try {
    const { managerID } = req.params;

    if (!managerID) {
      return res.status(400).json({
        success: false,
        message: "Recruiter are requires.",
      });
    }

    const manager = await User.aggregate([
      {
        $match: {
          userType: "accountManager",
          userId: managerID,
        },
      },
      {
        $lookup: {
          from: "accountmanagers",
          localField: "userId",
          foreignField: "user.userId",
          as: "profile",
        },
      },
      {
        $unwind: {
          path: "$profile",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "jobs",
          let: {
            userID: "$userId",
            status: "Active",
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ["$accountManager.userID", "$$userID"],
                    },
                    {
                      $eq: ["$jobStatus", "$$status"],
                    },
                  ],
                },
              },
            },
            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "_id",
                foreignField: "job._id",
                as: "submittions",
              },
            },
            {
              $addFields: {
                submittionCount: {
                  $size: "$submittions",
                },
                totalsubmittion: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: ["$$sub.status", submissionEnums],
                      },
                    },
                  },
                },
                reviewed: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          submissionEnums.filter(
                            (item) => !["submitted"].includes(item)
                          ),
                        ],
                      },
                    },
                  },
                },
                submittedToClient: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          submissionEnums.filter(
                            (item) => !["submitted", "reviewing"].includes(item)
                          ),
                        ],
                      },
                    },
                  },
                },
                interviewing: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          submissionEnums.filter(
                            (item) =>
                              ![
                                "submitted",
                                "reviewing",
                                "submitted to client",
                                "selected",
                              ].includes(item)
                          ),
                        ],
                      },
                    },
                  },
                },
                offerReleased: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          submissionEnums.filter(
                            (item) =>
                              ![
                                "submitted",
                                "reviewing",
                                "submitted to client",
                                "selected",
                                "interviewing",
                                "reject after interview",
                                "awaiting offer",
                                "rejected",
                              ].includes(item)
                          ),
                        ],
                      },
                    },
                  },
                },
                offerAccepted: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          [
                            "offer accepted",
                            "hired-under guarantee period",
                            "guarantee period not completed",
                            "guarantee period completed",
                          ],
                        ],
                      },
                    },
                  },
                },
                hiredUnderGuaranteePeriod: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: ["$$sub.status", ["hired-under guarantee period"]],
                      },
                    },
                  },
                },
                guaranteePeriodCompleted: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: ["$$sub.status", ["guarantee period completed"]],
                      },
                    },
                  },
                },
                guaranteePeriodNotCompleted: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          ["guarantee period not completed"],
                        ],
                      },
                    },
                  },
                },
                offerRejected: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: ["$$sub.status", ["offer rejected"]],
                      },
                    },
                  },
                },
                rejected: {
                  $size: {
                    $filter: {
                      input: "$submittions",
                      as: "sub",
                      cond: {
                        $in: [
                          "$$sub.status",
                          ["reject after interview", "rejected"],
                        ],
                      },
                    },
                  },
                },
              },
            },
            {
              $group: {
                _id: null,
                activeJobCount: { $sum: 1 },
                coverJobCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$submittionCount", 0],
                      },
                      1,
                      0,
                    ],
                  },
                },
                reviewedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$reviewed", 0],
                      },
                      "$reviewed",
                      0,
                    ],
                  },
                },
                totalsubmittionCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$totalsubmittion", 0],
                      },
                      "$totalsubmittion",
                      0,
                    ],
                  },
                },
                submittedToClientCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$submittedToClient", 0],
                      },
                      "$submittedToClient",
                      0,
                    ],
                  },
                },
                interviewingCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$interviewing", 0],
                      },
                      "$interviewing",
                      0,
                    ],
                  },
                },
                offerReleasedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$offerReleased", 0],
                      },
                      "$offerReleased",
                      0,
                    ],
                  },
                },
                offerAcceptedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$offerAccepted", 0],
                      },
                      "$offerAccepted",
                      0,
                    ],
                  },
                },
                hiredUnderGuaranteePeriodCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$hiredUnderGuaranteePeriod", 0],
                      },
                      "$hiredUnderGuaranteePeriod",
                      0,
                    ],
                  },
                },
                guaranteePeriodCompletedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$guaranteePeriodCompleted", 0],
                      },
                      "$guaranteePeriodCompleted",
                      0,
                    ],
                  },
                },
                guaranteePeriodNotCompletedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$guaranteePeriodNotCompleted", 0],
                      },
                      "$guaranteePeriodNotCompleted",
                      0,
                    ],
                  },
                },
                offerRejectedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$offerRejected", 0],
                      },
                      "$offerRejected",
                      0,
                    ],
                  },
                },
                rejectedCount: {
                  $sum: {
                    $cond: [
                      {
                        $gt: ["$rejected", 0],
                      },
                      "$rejected",
                      0,
                    ],
                  },
                },
              },
            },
          ],
          as: "jobDetails",
        },
      },
      {
        $unwind: {
          path: "$jobDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      {
        $lookup: {
          from: "jobs",
          let: { userID: "$userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ["$accountManager.userID", "$$userID"],
                },
              },
            },
            {
              $lookup: {
                from: "recruiters",
                let: { jobId: "$_id" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $gt: [
                          {
                            $size: {
                              $filter: {
                                input: "$jobsWorkingOn",
                                as: "job",
                                cond: {
                                  $and: [
                                    { $eq: ["$$job.jobId", "$$jobId"] },
                                    { $eq: ["$$job.status", "assigned"] },
                                    { $eq: ["$$job.isActive", true] },
                                  ],
                                },
                              },
                            },
                          },
                          0,
                        ],
                      },
                    },
                  },
                ],
                as: "recruter",
              },
            },
            {
              $addFields: {
                recruterCount: { $size: "$recruter" },
              },
            },

            {
              $lookup: {
                from: "candidatesubmissions",
                localField: "jobId",
                foreignField: "job.jobID",
                as: "candidatesubmissions",
              },
            },
            {
              $addFields: {
                candidateSubmissionCount: {
                  $size: "$candidatesubmissions",
                },
              },
            },
            {
              $project: {
                workonrequests: 0,
                candidatesubmissions: 0,
              },
            },
          ],
          as: "jobs",
        },
      },
      {
        $project: {
          profile: 1,
          createdAt: 1,
          jobs: 1,
          name: 1,
          userId: 1,
          email: 1,
          phone: 1,
          isActive: 1,
          activeJobCount: { $ifNull: ["$jobDetails.activeJobCount", 0] },
          coverJobCount: { $ifNull: ["$jobDetails.coverJobCount", 0] },

          totalsubmittionCount: {
            $ifNull: ["$jobDetails.totalsubmittionCount", 0],
          },
          submittedToClientCount: {
            $ifNull: ["$jobDetails.submittedToClientCount", 0],
          },
          interviewingCount: { $ifNull: ["$jobDetails.interviewingCount", 0] },
          offerReleasedCount: {
            $ifNull: ["$jobDetails.offerReleasedCount", 0],
          },
          offerAcceptedCount: {
            $ifNull: ["$jobDetails.offerAcceptedCount", 0],
          },
          hiredUnderGuaranteePeriodCount: {
            $ifNull: ["$jobDetails.hiredUnderGuaranteePeriodCount", 0],
          },
          guaranteePeriodCompletedCount: {
            $ifNull: ["$jobDetails.guaranteePeriodCompletedCount", 0],
          },
          guaranteePeriodNotCompletedCount: {
            $ifNull: ["$jobDetails.guaranteePeriodNotCompletedCount", 0],
          },
          offerRejectedCount: {
            $ifNull: ["$jobDetails.offerRejectedCount", 0],
          },
          rejectedCount: { $ifNull: ["$jobDetails.rejectedCount", 0] },
          reviewedCount: { $ifNull: ["$jobDetails.reviewedCount", 0] },
        },
      },
    ]);

    return res.status(200).json({
      message: "get all manager",
      success: true,
      data: manager,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ success: false, message: error.message });
  }
};

//* @Desc dashboard stats
//* @Route POST /api/v1/recruiter/dashboard-stats
//* @Access Private - Recruiter
const getDashboardData = async (req, res) => {
  try {
    const user = req.user;
    if (!user) {
      return res.status(400).json({
        success: false,
        message: "User not found",
      });
    }

    const jobs = await job.aggregate([
      [
        {
          $match: {
            isDeleted: false,
            visibility: true,
            accountManager: { $exists: true },
          },
        },

        {
          $facet: {
            status: [
              {
                $group: {
                  _id: "$jobStatus",
                  count: {
                    $sum: 1,
                  },
                },
              },
            ],
            totaljobcount: [
              {
                $count: "count",
              },
            ],
            priority: [
              {
                $group: {
                  _id: "$priority",
                  count: {
                    $sum: 1,
                  },
                },
              },
            ],
            jobPerSubmittion: [
              {
                $lookup: {
                  from: "candidatesubmissions",
                  localField: "_id",
                  foreignField: "job._id",
                  as: "candidatesubmissions",
                },
              },
              {
                $addFields: {
                  submissionCount: {
                    $size: "$candidatesubmissions",
                  },
                },
              },
              {
                $sort: {
                  submissionCount: -1,
                },
              },
              {
                $project: {
                  submissionCount: 1,
                  jobId: 1,
                  jobTitle: 1,
                },
              },
            ],
          },
        },
      ],
    ]);

    const submission = await candidateSubmission.aggregate([
      {
        $facet: {
          status: [
            {
              $group: {
                _id: "$status",
                count: {
                  $sum: 1,
                },
              },
            },
          ],
          count: [
            {
              $count: "count",
            },
          ],
          submitted: [
            {
              $match: {
                createdAt: {
                  $gte: new Date(Date.now() - 3.5 * 24 * 60 * 60 * 1000),
                },
              },
            },
            {
              $lookup: {
                from: "jobs",
                localField: "job._id",
                foreignField: "_id",
                as: "job",
              },
            },
            {
              $unwind: "$job",
            },
          ],
        },
      },
    ]);

    const userCount = await User.aggregate([
      {
        $match: {
          isActive: true,
        },
      },
      {
        $group: {
          _id: null,
          accountManagerCount: {
            $sum: {
              $cond: [
                {
                  $eq: ["$userType", "accountManager"],
                },
                1,
                0,
              ],
            },
          },
          recruiterCount: {
            $sum: {
              $cond: [{ $eq: ["$userType", "recruiter"] }, 1, 0],
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          accountManagerCount: 1,
          recruiterCount: 1,
        },
      },
    ]);

    return res.status(200).json({
      success: true,
      message: "dashboard stats find",
      data: {
        totalJob: jobs[0]?.totaljobcount[0]?.count,
        ...jobs[0]?.priority?.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        ...jobs[0]?.status?.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        jobPerSubmittion: jobs[0]?.jobPerSubmittion,
        recentSubmissions: submission[0]?.submitted,
        submissionCount: submission[0]?.count[0]?.count,
        submission: {
          ...submission[0].status?.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
        },
        ...userCount[0],
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: error.message, success: false });
  }
};

module.exports = {
  getAllManager,
  getManager,
  updateManager,
  getAllManagerWorkHistory,
  getDashboardData,
};
