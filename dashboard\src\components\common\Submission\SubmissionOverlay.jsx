import { useState } from "react";
import Messages from "../chats/Messages";
import { getSubmissionDetails } from "../../../services/operations/candidateAPI";

const SubmissionOverlay = ({ currentTab, submissionID }) => {
  const [submissionDetails, setSubmissionDetails] = useState("");


  const fetchSubmissionDetails = async () => {
    // Placeholder for fetching submission details based on submissionID

    const response = await getSubmissionDetails(submissionID);

    if (response) {
      setSubmissionDetails(response?.data);
    }
  };

  return (
    <>
      {currentTab === "chat" && (
        <Messages
          submissionId={submissionDetails?.submissionID}
          name={"Durgesh Maurya"}
        />
      )}
    </>
  );
};

export default SubmissionOverlay;
