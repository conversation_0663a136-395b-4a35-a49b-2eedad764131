import { Mail, User } from "lucide-react";
import { JobStatusOptions } from "../../../utils/JobStatusOptions";
import ColorSelectionInput from "../Input/ColorSelectionInput";

const CandidateCard = ({
  name,
  email,
  recruiter,
  candidateStatus,
  domain,
  template,
}) => {
  return (
    <>
      <div className=" bg-white rounded-xl shadow border border-[#E9EAEB] w-full">
        <div className="flex items-center justify-between p-4 ">
          {/* Left section: Avatar & Info */}
          <div className="flex items-center gap-4">
            {/* Avatar Circle with initials */}
            <div className="bg-blue-500 text-white font-semibold w-10 h-10 flex items-center justify-center rounded-full">
              {name &&
                name
                  ?.split(" ")
                  .map((item) => item.slice(0, 1).toUpperCase())
                  .join("")}
            </div>
            {/* Candidate Info */}
            <div>
              <p className="text-sm font-medium text-gray-900">
                {name && name}
              </p>
              <div className="flex items-center text-gray-500 text-sm gap-4">
                <span className="flex items-center gap-1">
                  <Mail className="w-4 h-4" />
                  {email && email}
                </span>
                {recruiter && (
                  <span className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {recruiter}
                  </span>
                )}
                {domain && domain.length > 0 && (
                  <div className="flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                    >
                      <path
                        d="M10.0026 10.8346C10.8082 10.8346 11.4957 10.5499 12.0651 9.98047C12.6345 9.41102 12.9193 8.72352 12.9193 7.91797C12.9193 7.11241 12.6345 6.42491 12.0651 5.85547C11.4957 5.28602 10.8082 5.0013 10.0026 5.0013C9.19705 5.0013 8.50955 5.28602 7.9401 5.85547C7.37066 6.42491 7.08594 7.11241 7.08594 7.91797C7.08594 8.72352 7.37066 9.41102 7.9401 9.98047C8.50955 10.5499 9.19705 10.8346 10.0026 10.8346ZM10.0026 16.5846C10.822 16.3207 11.5479 15.9077 12.1801 15.3455C12.8123 14.7832 13.3643 14.1477 13.8359 13.4388C13.2387 13.1332 12.617 12.9007 11.9709 12.7413C11.3248 12.5819 10.6687 12.5019 10.0026 12.5013C9.33649 12.5007 8.6801 12.5807 8.03344 12.7413C7.38677 12.9019 6.76538 13.1344 6.16927 13.4388C6.64149 14.1471 7.19371 14.7827 7.82594 15.3455C8.45816 15.9082 9.18372 16.3213 10.0026 16.5846ZM10.0026 18.2513C9.90538 18.2513 9.8151 18.2444 9.73177 18.2305C9.64844 18.2166 9.5651 18.1957 9.48177 18.168C7.60677 17.543 6.11372 16.3869 5.0026 14.6996C3.89149 13.0124 3.33594 11.1963 3.33594 9.2513V5.3138C3.33594 4.96658 3.43677 4.65408 3.63844 4.3763C3.8401 4.09852 4.10038 3.89714 4.41927 3.77214L9.41927 1.89714C9.61372 1.82769 9.80816 1.79297 10.0026 1.79297C10.197 1.79297 10.3915 1.82769 10.5859 1.89714L15.5859 3.77214C15.9054 3.89714 16.1659 4.09852 16.3676 4.3763C16.5693 4.65408 16.6698 4.96658 16.6693 5.3138V9.2513C16.6693 11.1957 16.1137 13.0119 15.0026 14.6996C13.8915 16.3874 12.3984 17.5435 10.5234 18.168C10.4401 18.1957 10.3568 18.2166 10.2734 18.2305C10.1901 18.2444 10.0998 18.2513 10.0026 18.2513Z"
                        fill="#717680"
                      />
                    </svg>
                    <div className="truncate flex  gap-x-2">
                      {domain.join(",")}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Status Badge */}
          {candidateStatus && (
            <div>
              <ColorSelectionInput
                value={candidateStatus}
                width={"w-40"}
                disabled={true}
                options={[
                  ...JobStatusOptions,
                  {
                    value: "Talent Pool",
                    label: "Talent Pool",
                    color: "bg-[#CEFAFE] text-[#00B8DB]",
                  },
                ]}
              />
            </div>
          )}
        </div>
        {template && template}
      </div>
    </>
  );
};

export default CandidateCard;
