import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import InputField from "../../../../common/Input/InputField";

const SkillExperienceForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
  } = useFormikContext();

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <div>
        <label className="text-sm font-medium text-gray-700">
          Total Years of Experience {<span className="text-red-800">*</span>}
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            // label="Total Years of Experience"
            name="skillsAndExperience.totalYearsOfExperience.year"
            value={values.skillsAndExperience.totalYearsOfExperience?.year || 0}
            onChange={handleChange}
            onBlur={handleBlur}
            required
            minmax="0-60"
            placeholder="Enter year"
            type={"number"}
          />
          <InputField
            name="skillsAndExperience.totalYearsOfExperience.month"
            value={
              values.skillsAndExperience.totalYearsOfExperience?.month || 0
            }
            onChange={handleChange}
            onBlur={handleBlur}
            required
            minmax="0-11"
            placeholder="Enter month"
            type={"number"}
          />
        </div>
        <div>
          <small className="text-red-600">
            {touched.skillsAndExperience?.totalYearsOfExperience?.year &&
              errors.skillsAndExperience?.totalYearsOfExperience?.year}
          </small>
        </div>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-700">
          Relevant Experience {<span className="text-red-800">*</span>}
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            name="skillsAndExperience.relevantExperience.year"
            value={values.skillsAndExperience.relevantExperience?.year || 0}
            onChange={handleChange}
            onBlur={handleBlur}
            required
            minmax="0-60"
            placeholder="Enter year"
            type="number"
          />
          <InputField
            name="skillsAndExperience.relevantExperience.month"
            value={values.skillsAndExperience.relevantExperience?.month || 0}
            onChange={handleChange}
            onBlur={handleBlur}
            required
            minmax="0-11"
            placeholder="Enter month"
            type="number"
          />
        </div>
        <div>
          <small className="text-red-600">
            {touched.skillsAndExperience?.relevantExperience?.year &&
              errors.skillsAndExperience?.relevantExperience?.year}
          </small>
        </div>
      </div>

      <InputField
        label="Other Skills"
        name="skillsAndExperience.otherSkills"
        value={values.skillsAndExperience.otherSkills}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder="Enter other skills"
      />
    </form>
  );
};

export default SkillExperienceForm;
