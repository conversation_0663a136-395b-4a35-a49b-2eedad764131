import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import PaginationFooter from "../../components/common/Table/TableFooter";
import TableHeader from "../../components/common/Table/TableHeader";
import ColorSelectionInput from "../../components/common/Input/ColorSelectionInput";
import EmptyState from "../../components/common/EmptyState";
import {
  createAccountManager,
  getAllManager,
} from "../../services/operations/headAccountAPI";
import { useSelector } from "react-redux";
import AppButton from "../../components/common/Button/AppButton";
import Modal from "../../components/common/Modal/Modal";
import { useFormik } from "formik";
import PhoneInput from "react-phone-input-2";
import InputField from "../../components/common/Input/InputField";
import SelectField from "../../components/common/Input/SelectionField";
import industries from "../../data/industries.json";
import MultiSelectionField from "../../components/common/Input/MultiSelectionField";
import SearchDropdown from "../../components/common/filter/SearchDropdown";
import ViewOptionsDropdown from "../../components/common/filter/ViewOptionsDropdown";
import FiltersPanel from "../../components/common/filter/FiltersPanel";
import SortingPanel from "../../components/common/filter/SortingPanel";
import { useURLState } from "../../components/common/filter/useURLState";
import "react-phone-input-2/lib/style.css";
import ThreeDot from "../../components/common/Button/ThreeDot";
import { updateAccountManager } from "../../services/operations/headAccountAPI";

const AccountManager = () => {
  const [activeTab, setActiveTab] = useState(0);
  const tab = [{ name: "All Managers" }];
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [manager, setManager] = useState([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [addAccountManager, setAddAccountManager] = useState(false);
  const [editManager, setEditManager] = useState(null);

  // Refs to track previous values and prevent unnecessary calls
  const prevFiltersRef = useRef("");
  const prevSortingRef = useRef("");
  const prevSearchRef = useRef("");
  const isInitialMount = useRef(true);
  const fetchTimeoutRef = useRef(null);

  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  const { parseURLParams, updateURLParams } = useURLState();
  const initialURLState = parseURLParams();

  // Search and Filter States
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const [searchData, setSearchData] = useState(
    initialURLState.search || {
      searchTerm: "",
      searchField: "all",
    }
  );
  const [active, setActive] = useState(initialURLState.activeTab || 0);

  // View Options and Filter States
  const [showDropdown, setShowDropdown] = useState(false);
  const [showFilters, setShowFilters] = useState(
    initialURLState.showFilters || false
  );
  const [showSorting, setShowSorting] = useState(
    initialURLState.showSorting || false
  );

  // Filter and Sorting States
  const [filters, setFilters] = useState(
    initialURLState.filters || {
      status: [],
    }
  );

  const [sorting, setSorting] = useState(() => {
    if (initialURLState.sorting.postedDate) {
      return initialURLState.sorting.postedDate === "recent"
        ? "Recent"
        : "Oldest";
    } else if (initialURLState.sorting.sortBy) {
      return `${initialURLState.sorting.sortBy}_${initialURLState.sorting.sortOrder}`;
    }
    return "";
  });

  // Available filters for managers
  const availableFilters = ["status"];
  const availableSortOptions = ["postedDate"];

  const columns = [
    { label: "EMPLOYEE ID", key: "userID" },
    { label: "Name", key: "name" },
    { label: "Email", key: "email" },
    { label: "Active Assigned Jobs", key: "activeJobCount" },
    { label: "Coverage Jobs", key: "coverJobCount" },
    { label: "No. Of Submission", key: "noOfSubmission" },
    { label: "", key: "actions" },
  ];

  const statusOptions = [
    {
      value: "active",
      label: "Active",
      color: "text-[#12B76A] bg-[#D1FADF]",
    },
    {
      value: "inactive",
      label: "Inactive",
      color: "text-[#EF4444] bg-[#FEE2E2]",
    },
    {
      value: "onleave",
      label: "On Leave",
      color: "text-[#F79009] bg-[#FEEFC7]",
    },
    {
      value: "pending",
      label: "Pending",
      color: "text-[#6B7280] bg-[#F3F4F6]",
    },
    {
      value: "suspended",
      label: "Suspended",
      color: "text-[#DC2626] bg-[#FEE2E2]",
    },
    {
      value: "blocked",
      label: "Blocked",
      color: "text-[#991B1B] bg-[#FEE2E2]",
    },
  ];

  // Clean filters to remove empty arrays
  const cleanFilters = (filtersObj) => {
    const cleaned = {};
    Object.keys(filtersObj).forEach((key) => {
      const value = filtersObj[key];
      if (Array.isArray(value) && value.length > 0) {
        cleaned[key] = value;
      } else if (typeof value === "string" && value.trim()) {
        cleaned[key] = value;
      }
    });
    return cleaned;
  };

  // Check if there are any active filters or search
  const hasActiveFiltersOrSearch = () => {
    const hasFilters = Object.values(filters).some((filter) =>
      Array.isArray(filter) ? filter.length > 0 : filter
    );
    const hasSearch =
      searchData.searchTerm && searchData.searchTerm.trim() !== "";
    const hasSorting = sorting && sorting !== "";

    return hasFilters || hasSearch || hasSorting;
  };

  // Main API call function with debouncing
  const getData = async (
    page = 1,
    limit = 10,
    appliedFilters = {},
    appliedSorting = "",
    appliedSearch = {}
  ) => {
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Debounce the API call
    fetchTimeoutRef.current = setTimeout(async () => {
      try {
        setLoading(true);

        // Convert sorting string to params object
        let sortingParams = {};
        if (appliedSorting) {
          switch (appliedSorting) {
            case "Recent":
              sortingParams = { postedDate: "recent" };
              break;
            case "Oldest":
              sortingParams = { postedDate: "oldest" };
              break;
            case "name_asc":
              sortingParams = { sortBy: "name", sortOrder: "asc" };
              break;
            case "name_desc":
              sortingParams = { sortBy: "name", sortOrder: "desc" };
              break;
            case "email_asc":
              sortingParams = { sortBy: "email", sortOrder: "asc" };
              break;
            case "email_desc":
              sortingParams = { sortBy: "email", sortOrder: "desc" };
              break;
            default:
              if (appliedSorting.includes("_")) {
                const [sortBy, sortOrder] = appliedSorting.split("_");
                sortingParams = { sortBy, sortOrder };
              }
          }
        }

        // Clean filters before sending
        const cleanedFilters = cleanFilters(appliedFilters);

        // Call API
        const response = await getAllManager(
          page,
          limit,
          cleanedFilters,
          sortingParams,
          appliedSearch
        );
        const results = response?.results || [];

        const mapped = results.map((item) => ({
          id: item?._id,
          userID: item?.userId || "—",
          name:
            `${item.name?.firstName || ""} ${
              item.name?.lastName || ""
            }`.trim() || "—",
          email: `${item?.email}`.trim() || "—",
          activeJobCount: item?.activeJobCount,
          coverJobCount: item?.coverJobCount,
          noOfSubmission: item?.noOfSubmission,
          status: item?.status || "active",
          actions: (
            <ThreeDot
              dropdownSize="w-32"
              buttonDropDown={
                <ul className="py-0" role="none">
                  <li>
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setEditManager(item);
                        setAddAccountManager(true);
                        formik.setValues({
                          email: item.email || "",
                          phoneNumber:
                            item.phone?.number || item.phoneNumber || "",
                          countryCode:
                            item.phone?.countryCode || item.countryCode || "",
                          domain: Array.isArray(item.domain)
                            ? item.domain
                            : Array.isArray(item.profile?.domain)
                            ? item.profile.domain
                            : [],
                          fullname: `${item.name?.firstName || ""} ${
                            item.name?.lastName || ""
                          }`.trim(),
                        });
                      }}
                      className="flex items-center gap-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900  px-4 py-2 cursor-pointer"
                      role="menuitem"
                    >
                      <img src="/assets/icons/edit.svg" alt="Edit" />
                      Edit Manager
                    </div>
                  </li>
                </ul>
              }
            />
          ),
        }));

        setManager(mapped);
        setTotalResults(response?.total || 0);
        setTotalPages(response?.totalPages || 1);
      } catch (err) {
        console.error("Error fetching managers:", err);
        setManager([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms debounce
  };

  // Search handlers
  const handleSearchClick = () => {
    setShowSearchDropdown(!showSearchDropdown);
  };

  const handleSearchClose = () => {
    setShowSearchDropdown(false);
  };

  const handleSearch = (searchInputs) => {
    setSearchData(searchInputs);
    if (currentPage !== 1) {
      setCurrentPage(1); // This will trigger the useEffect
    } else {
      // If already on page 1, manually call getData
      getData(1, pageSize, filters, sorting, searchInputs);
    }
  };

  // Filter and Sorting handlers
  const handleFilterChange = (filterType, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [filterType]: value,
    }));
  };

  const handleTabChange = (newActiveTab) => {
    setActiveTab(newActiveTab);
    setActive(newActiveTab);
  };

  const handleSortingChange = (sortValue) => {
    setSorting(sortValue);
  };

  const handleClearAllFilters = () => {
    const clearedFilters = { status: [] };
    const clearedSearch = { searchTerm: "", searchField: "all" };

    setFilters(clearedFilters);
    setSorting("");
    setSearchData(clearedSearch);

    if (currentPage !== 1) {
      setCurrentPage(1); // Will trigger useEffect
    } else {
      getData(1, pageSize, clearedFilters, "", clearedSearch);
    }
  };

  const handleApplyFilters = () => {
    setShowDropdown(false);
    setShowSearchDropdown(false);

    if (currentPage !== 1) {
      setCurrentPage(1); // Will trigger useEffect with current state
    } else {
      getData(1, pageSize, filters, sorting, searchData);
    }
  };

  // Update manager status
  const updatemanagertatus = async (submissionId, newStatus) => {
    try {
      console.log(
        `Updating manager status ${submissionId} to status: ${newStatus}`
      );
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  // Effect for pagination changes
  useEffect(() => {
    getData(currentPage, pageSize, filters, sorting, searchData);
  }, [currentPage, pageSize]);

  // Effect for filters, sorting, search changes
  useEffect(() => {
    const filtersStr = JSON.stringify(filters);
    const searchStr = JSON.stringify(searchData);

    // Check if values actually changed
    const filtersChanged = filtersStr !== prevFiltersRef.current;
    const sortingChanged = sorting !== prevSortingRef.current;
    const searchChanged = searchStr !== prevSearchRef.current;

    // Skip if nothing changed and not initial mount
    if (
      !isInitialMount.current &&
      !filtersChanged &&
      !sortingChanged &&
      !searchChanged
    ) {
      return;
    }

    // Update refs
    prevFiltersRef.current = filtersStr;
    prevSortingRef.current = sorting;
    prevSearchRef.current = searchStr;

    // Reset to page 1 when filters change (but not on initial mount)
    if (
      !isInitialMount.current &&
      (filtersChanged || sortingChanged || searchChanged)
    ) {
      if (currentPage !== 1) {
        setCurrentPage(1); // Will trigger pagination useEffect
      } else {
        getData(1, pageSize, filters, sorting, searchData);
      }
    } else if (isInitialMount.current) {
      // Initial fetch
      getData(currentPage, pageSize, filters, sorting, searchData);
      isInitialMount.current = false;
    }
  }, [filters, sorting, searchData, pageSize]);

  // Update URL when state changes (keep this separate)
  useEffect(() => {
    const sortingForURL = {};
    if (sorting) {
      if (sorting === "Recent") {
        sortingForURL.postedDate = "recent";
      } else if (sorting === "Oldest") {
        sortingForURL.postedDate = "oldest";
      } else if (sorting.includes("_")) {
        const [sortBy, sortOrder] = sorting.split("_");
        sortingForURL.sortBy = sortBy;
        sortingForURL.sortOrder = sortOrder;
      }
    }

    updateURLParams({
      activeTab: active,
      filters: filters,
      sorting: sortingForURL,
      search: searchData.searchTerm ? searchData : {},
      showFilters: showFilters,
      showSorting: showSorting,
    });
  }, [
    active,
    filters,
    sorting,
    searchData,
    showFilters,
    showSorting,
    updateURLParams,
  ]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  // Form handling
  const formik = useFormik({
    initialValues: {
      email: "",
      phoneNumber: "",
      countryCode: "",
      domain: [],
      fullname: "",
    },
    validate: (data) => {
      let errors = {};
      if (!data.email) errors.email = "Email is required.";
      if (!data.phoneNumber) errors.phoneNumber = "Phone Number is required.";
      if (!data.fullname) errors.fullname = "Full Name is required.";
      if (!data.domain) errors.domain = "Domain is required.";
      return errors;
    },
    onSubmit: async (data) => {
      try {
        if (editManager) {
          // Split fullname into firstName and lastName
          const [firstName, ...rest] = data.fullname.trim().split(" ");
          const lastName = rest.join(" ");
          const payload = {
            email: data.email,
            name: {
              firstName: firstName || "",
              lastName: lastName || "",
            },
            phone: {
              countryCode: data.countryCode,
              number: data.phoneNumber,
            },
            profile: {
              domain: data.domain,
            },
          };
          await updateAccountManager(
            payload,
            editManager.userId || editManager._id
          );
        } else {
          await accountManager({
            domain: data.domain,
            email: data?.email,
            name: data?.fullname,
            countryCode: data?.countryCode,
            phone: data?.phoneNumber,
            userType: "accountManager",
          });
        }
        setAddAccountManager(false);
        setEditManager(null);
        formik.resetForm();
        // Just call getData directly
        getData(currentPage, pageSize, filters, sorting, searchData);
      } catch (error) {
        console.log(error);
      }
    },
  });

  const isFormFieldValid = (name) =>
    !!(formik.touched[name] && formik.errors[name]);
  const getFormErrorMessage = (name) => {
    return (
      isFormFieldValid(name) && (
        <small className="text-red-500">{formik.errors[name]}</small>
      )
    );
  };

  async function accountManager(data) {
    try {
      await createAccountManager(data);
      setAddAccountManager(false);
      formik.resetForm();
      // Just call getData directly
      getData(currentPage, pageSize, filters, sorting, searchData);
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <>
      <TabNav
        nav={tab}
        active={activeTab}
        setActive={handleTabChange}
        rightSidebar={
          <div className="flex gap-3">
            <div className="relative">
              <img
                src={"/assets/icons/search.svg"}
                alt="Search"
                className="w-6 h-8 cursor-pointer"
                onClick={handleSearchClick}
              />
              <SearchDropdown
                isOpen={showSearchDropdown}
                onClose={handleSearchClose}
                onSearch={handleSearch}
                activeTab="manager"
              />
            </div>

            <ViewOptionsDropdown
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              setShowFilters={setShowFilters}
              setShowSorting={setShowSorting}
              showFilters={showFilters}
              showSorting={showSorting}
            />

            <AppButton
              label={"+ Add Manager"}
              onClick={(e) => {
                e.preventDefault();
                setAddAccountManager(true);
              }}
            />
          </div>
        }
      />

      <FiltersPanel
        showFilters={showFilters}
        filters={filters}
        setShowFilters={setShowFilters}
        handleFilterChange={handleFilterChange}
        handleClearAllFilters={handleClearAllFilters}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableFilters={availableFilters}
        pageType="manager"
      />

      <SortingPanel
        showSorting={showSorting}
        setShowSorting={setShowSorting}
        sorting={sorting}
        setSorting={handleSortingChange}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableSortOptions={availableSortOptions}
      />

      {/* Show loading state */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-2 text-gray-600">Loading managers...</span>
        </div>
      )}

      {/* Show content based on data availability */}
      {!loading && (
        <>
          {manager.length > 0 ? (
            <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
              <table className="min-w-full bg-white">
                <TableHeader noWrap columns={columns.map((col) => col.label)} />
                <tbody>
                  {manager?.map((sub, index) => (
                    <tr
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(
                          `/manager/managerdetail?managerid=${sub?.userID}&tab=active`
                        );
                      }}
                      key={index}
                      className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800 cursor-pointer"
                    >
                      {columns.map((col) => {
                        if (col.key === "status") {
                          return (
                            <td className="px-4 py-3" key={col.key}>
                              <ColorSelectionInput
                                value={sub.status}
                                onChange={(newStatus) => {
                                  const updated = [...manager];
                                  updated[index] = {
                                    ...updated[index],
                                    status: newStatus,
                                  };
                                  setManager(updated);
                                  updatemanagertatus(sub.id, newStatus);
                                }}
                                options={statusOptions}
                                disabled={true}
                                width="w-44"
                              />
                            </td>
                          );
                        } else {
                          return (
                            <td className="px-4 py-3" key={col.key}>
                              {sub[col.key]}
                            </td>
                          );
                        }
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>

              <PaginationFooter
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                setPageSize={setPageSize}
                setCurrentPage={setCurrentPage}
                totalResults={totalResults}
              />
            </div>
          ) : (
            <EmptyState
              title={
                hasActiveFiltersOrSearch()
                  ? "No Managers Found"
                  : "No Account Managers"
              }
              description={
                hasActiveFiltersOrSearch()
                  ? "No managers match your current search criteria or filters. Try adjusting your search or clearing filters to see all managers."
                  : "There are no account managers in the system yet. Account managers will appear here once they are added."
              }
              showClearButton={hasActiveFiltersOrSearch()}
              onClearFilters={handleClearAllFilters}
            />
          )}
        </>
      )}

      <Modal
        css="relative p-4 w-2xl max-w-2xl max-h-full"
        isOpen={addAccountManager}
        children={
          <div className="p-4">
            <form
              className="grid grid-cols-2 gap-4"
              onSubmit={formik.handleSubmit}
            >
              <InputField
                label="Full Name"
                name="fullname"
                value={formik.values.fullname}
                onChange={formik.handleChange}
                required
                placeholder="Enter Full Name"
                errorMessage={getFormErrorMessage("fullname")}
              />
              <InputField
                label="Email"
                name="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                required
                placeholder="Enter Email Address"
                errorMessage={getFormErrorMessage("email")}
              />
              <MultiSelectionField
                label="Specialization"
                required={true}
                errorMessage={getFormErrorMessage("domain")}
                placeholder={"Select"}
                options={industries.map((industry) => industry.name)}
                selected={formik.values.domain}
                setSelected={(e) => {
                  formik.setFieldValue("domain", e);
                }}
                name="domain"
              />
              <div className="w-full">
                <label
                  htmlFor="personalDetails.phoneNumber"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Contact <span className="text-red-500">*</span>
                </label>
                <PhoneInput
                  country="us"
                  value={`${formik.values.countryCode}${formik.values.phoneNumber}`}
                  onChange={(value, country, event, formattedValue) => {
                    const dialCode = country?.dialCode || "";
                    const phoneNumber = value.slice(dialCode.length);
                    formik.setFieldValue("countryCode", `+${dialCode}`);
                    formik.setFieldValue("phoneNumber", phoneNumber);
                  }}
                  enableSearch
                  inputProps={{
                    name: "phoneNumber",
                    required: true,
                    autoFocus: false,
                    id: "phoneNumber",
                  }}
                  containerClass="w-full"
                  inputClass="!h-[2.4rem] !w-full pl-14 pr-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  buttonClass="!h-[2.4rem] !border-r border-gray-300"
                  placeholder="Enter your phone number"
                />
                {
                  <span className="text-xs text-red-600">
                    {getFormErrorMessage("phoneNumber")}
                  </span>
                }
              </div>
              <div></div>
              <div className="flex gap-2 justify-end mb-2">
                <AppButton
                  label={"Cancel"}
                  variant="secondary"
                  onClick={(e) => {
                    e.preventDefault();
                    setAddAccountManager(false);
                    setEditManager(null);
                    formik.resetForm();
                  }}
                />
                <AppButton
                  label={editManager ? "Update" : "Save"}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    formik.handleSubmit(e);
                  }}
                />
              </div>
            </form>
          </div>
        }
        title={editManager ? "Edit Manager" : "Add Manager"}
        onClose={() => {
          formik.resetForm();
          setAddAccountManager(false);
          setEditManager(null);
        }}
      />
    </>
  );
};

export default AccountManager;
