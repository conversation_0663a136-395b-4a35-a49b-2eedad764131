import React, { useState } from "react";

const DocumentIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M6.9849 10.2567H13.0099V9.42333H6.9849V10.2567ZM6.9849 12.5642H13.0099V11.7308H6.9849V12.5642ZM6.9849 14.8725H10.5099V14.0392H6.9849V14.8725ZM5.5099 17.5C5.12656 17.5 4.80656 17.3717 4.5499 17.115C4.29323 16.8583 4.16462 16.5381 4.16406 16.1542V3.84583C4.16406 3.4625 4.29267 3.1425 4.5499 2.88583C4.80712 2.62917 5.1274 2.50056 5.51073 2.5H12.0807L15.8307 6.25V16.1542C15.8307 16.5375 15.7024 16.8578 15.4457 17.115C15.1891 17.3722 14.8685 17.5006 14.4841 17.5H5.5099ZM11.6641 6.66667H14.9974L11.6641 3.33333V6.66667Z"
      fill="#27364B"
    />
  </svg>
);

const JobCard = ({ title, children, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="bg-white rounded-lg shadow-md transition-all duration-300 w-full overflow-hidden">
      {/* Header */}
      <button
        className="flex justify-between items-center w-full bg-[#F1F4F9] px-4 py-2 cursor-pointer focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls="job-card-content"
        type="button"
      >
        <span className="text-sm font-semibold flex items-center gap-2">
          <DocumentIcon />
          {title}
        </span>
        {isOpen ? (
          <svg
            className="w-4 h-4 text-gray-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 15l7-7 7 7"
            />
          </svg>
        ) : (
          <svg
            className="w-4 h-4 text-gray-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        )}
      </button>

      {/* Collapsible Content */}
      <div
        id="job-card-content"
        className={`transition-all duration-300 ease-in-out px-4 ${
          isOpen ? "py-3 opacity-100" : "py-0 opacity-0 max-h-0 overflow-hidden"
        }`}
      >
        {children}
      </div>
    </div>
  );
};

export default JobCard;
