import { useState, useEffect } from "react";
import TabNav from "../../components/common/TabNav/TabNav";
import StatsCard from "../../components/common/Dashboard/StatsCard";
import TableHeader from "../../components/common/Table/TableHeader";
import PaginationFooter from "../../components/common/Table/TableFooter";
import ThreeDot from "../../components/common/Button/ThreeDot";
import { getPayoutDetails } from "../../services/operations/payoutAPI";
import Modal from "../../components/common/Modal/Modal";

const Payouts = () => {
  const [active, setActive] = useState(0);
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [upcomingPayoutSum, setUpcomingPayoutSum] = useState(0);
  const [receivedPayoutSum, setReceivedPayoutSum] = useState(0);
  const [nextPayoutCount, setNextPayoutCount] = useState(0);
  const [receivedPayouts, setReceivedPayouts] = useState([]);
  const [nextPayoutEligible, setNextPayoutEligible] = useState([]);

  const tabs = [
    { name: <span>Upcoming Payouts</span>, css: "" },
    { name: <span>All Payouts</span>, css: "" },
  ];

  const columns = [
    { label: "Amount", key: "commission" },
    { label: "Payout Date", key: "payoutReleaseDate" },
    { label: "Recruiter Name", key: "recruiterName" },
    { label: "Jobe Title", key: "jobtitle" },
    { label: "Job Type", key: "jobtype" },
    { label: "Status", key: "payoutStatus" },
    { label: "Bank Reference ID", key: "bankReference" },
    { label: "", key: "actions" },
  ];

  const modalColumns = [
    [
      {
        key: "payoutReleaseDate",
        label: "Payout Released Date",
      },
      { key: "jobid", label: "Job ID" },
      { key: "jobtitle", label: "Job Title" },
    ],
    [
      { key: "candidateName", label: "Candidate Name" },
      { key: "recruiterName", label: "Recruiter" },
      { key: "guaranteePeriod", label: "Guarantee Period" },
    ],
    [
      {
        key: "payRateAmount",
        label: "Pay Rate",

        highlight: true,
      },
      {
        key: "commission",
        label: "Commission",

        highlight: true,
      },
      { key: "jobtype", label: "Job Type" },
    ],
    [
      { key: "bankReference", label: "Bank Reference ID" },
      { key: "payoutId", label: "Payout ID" },
    ],
  ];

  const formatDate = (dateStr) => {
    if (!dateStr) return "—";
    const date = new Date(dateStr);
    return isNaN(date)
      ? "—"
      : date.toLocaleDateString("en-IN", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
  };
  const getStatusColor = (status) => {
    if (!status) return "";
    if (status.toLowerCase() === "pending") return "text-yellow-600";
    if (status.toLowerCase() === "paid") return "text-green-600";
    if (status.toLowerCase() === "rejected") return "text-red-600";
    return "";
  };

  useEffect(() => {
    const fetchPayouts = async () => {
      try {
        const res = await getPayoutDetails();
        if (res?.success) {
          let payouts = res.data || [];

          const upcomingPayouts = payouts.filter(
            (item) => item.payoutStatus?.toLowerCase() === "pending"
          );

          const received = payouts.filter(
            (item) => item.payoutStatus?.toLowerCase() === "paid"
          );
          setReceivedPayouts(received);

          const receivedSum = received.reduce((total, item) => {
            const amount = parseFloat(item.commission) || 0;
            return total + amount;
          }, 0);
          setReceivedPayoutSum(receivedSum);

          const nextEligible = payouts.filter(
            (item) =>
              item.payoutStatus?.toLowerCase() === "pending" &&
              item.candidateSubmissionStatus?.toLowerCase() ===
                "guarantee period completed"
          );
          setNextPayoutEligible(nextEligible);
          setNextPayoutCount(nextEligible.length);

          const nextPayoutSum = nextEligible.reduce(
            (sum, item) => sum + (parseFloat(item.commission) || 0),
            0
          );
          setUpcomingPayoutSum(nextPayoutSum);

          if (active === 0) {
            payouts = upcomingPayouts;
          } else if (active === 1) {
            payouts = payouts.filter(
              (item) =>
                item.payoutStatus?.toLowerCase() === "paid" ||
                item.payoutStatus?.toLowerCase() === "pending" ||
                item.payoutStatus?.toLowerCase() === "rejected"
            );
          }
          setData(payouts);
        } else {
          setData([]);
          setUpcomingPayoutSum(0);
          setReceivedPayoutSum(0);
          setNextPayoutCount(0);
          setReceivedPayouts([]);
          setNextPayoutEligible([]);
        }
      } catch (err) {
        setData([]);
        setUpcomingPayoutSum(0);
        setReceivedPayoutSum(0);
        setNextPayoutCount(0);
        setReceivedPayouts([]);
        setNextPayoutEligible([]);
      }
    };
    fetchPayouts();
  }, [active]);
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPayout(null);
  };

  return (
    <div className="p-4">
      <div className="flex gap-6 ">
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image={"/assets/icons/file_color_yellow.svg"}
          lable="Next Payout"
          value={`${upcomingPayoutSum.toLocaleString()}`}
          rightStats={{
            label: "Hires",
            value: nextPayoutEligible.length.toString(),
          }}
        />
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image={"/assets/icons/file_color_yellow.svg"}
          lable="Recieved Payout"
          value={`${receivedPayoutSum.toLocaleString()}`}
          rightStats={{
            label: "Hires",
            value: receivedPayouts.length.toString(),
          }}
        />
        <StatsCard
          imageBackground="bg-[#FEEFC7]"
          image={"/assets/icons/file_color_yellow.svg"}
          lable="Total Payout"
          value={`${(upcomingPayoutSum + receivedPayoutSum).toLocaleString()}`}
          rightStats={{
            label: "Hires",
            value: (
              nextPayoutEligible.length + receivedPayouts.length
            ).toString(),
          }}
        />
      </div>

      <TabNav
        nav={tabs}
        active={active}
        setActive={setActive}
        className="mb-4"
      />

      <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full bg-white">
          <TableHeader columns={columns.map((col) => col.label)} />
          <tbody>
            {data.length > 0 ? (
              data.map((row, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 hover:bg-gray-50 text-sm text-gray-800"
                >
                  {columns.map((col) => {
                    if (col.key === "actions") {
                      return (
                        <td key={col.key} className="px-4 py-3 text-start">
                          <ThreeDot
                            dropdownSize="w-32"
                            buttonDropDown={
                              <ul className="py-0" role="none">
                                <li>
                                  <div
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setSelectedPayout(row);
                                      setIsModalOpen(true);
                                    }}
                                    className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-dark700 hover:bg-gray-100"
                                    role="menuitem"
                                  >
                                    <img
                                      src={"/assets/icons/view.svg"}
                                      alt="View Details"
                                    />
                                    View Details
                                  </div>
                                </li>
                              </ul>
                            }
                          />
                        </td>
                      );
                    } else {
                      return (
                        <td key={col.key} className="px-4 py-3">
                          {row[col.key] ?? "—"}
                        </td>
                      );
                    }
                  })}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length}
                  className="text-center py-6 text-gray-500"
                >
                  No payout records found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <PaginationFooter
        currentPage={currentPage}
        totalPages={totalPages}
        setCurrentPage={setCurrentPage}
        pageSize={pageSize}
        setPageSize={setPageSize}
        totalResults={total}
      />

      {/* Payout Details Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={
          selectedPayout ? (
            <div className="flex items-center gap-2 ">
              <span>Payout Details (Status: </span>
              <span
                className={`font-semibold ${getStatusColor(
                  selectedPayout.payoutStatus
                )}`}
              >
                {selectedPayout.payoutStatus || "-"}
              </span>
              <span>, {formatDate(selectedPayout.payoutReleaseDate)})</span>
            </div>
          ) : (
            "Payout Details"
          )
        }
        css="relative p-3 w-full max-w-4xl max-h-full "
      >
        {selectedPayout && (
          <div className="px-4 pb-4">
            <div className="mb-4 p-3  rounded-lg">
              {selectedPayout.payoutStatus?.toLowerCase() === "paid" && (
                <p className="text-sm text-gray-600">
                  Once the payout is processed, it may take 1-3 business days to
                  reflect in your bank account.
                </p>
              )}
            </div>

            <div className="grid gap-y-6 mb-1">
              {modalColumns.map((columnGroup, groupIndex) => (
                <div
                  key={groupIndex}
                  className="grid md:grid-cols-3 gap-6 w-full"
                >
                  {columnGroup.map((field) =>
                    field.isEmpty ? (
                      <div key={field.key} />
                    ) : (
                      <div key={field.key}>
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {field.label}
                        </p>
                        <p
                          className={`text-sm text-gray-900 ${
                            field.highlight ? "font-semibold" : ""
                          }`}
                        >
                          {field.formatter
                            ? field.formatter(selectedPayout[field.key])
                            : selectedPayout[field.key] || "—"}
                        </p>
                      </div>
                    )
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Payouts;
