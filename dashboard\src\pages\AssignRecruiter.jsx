import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import JobCard from "../components/common/cards/JobCard";
import JobOverviewCard from "../components/common/Job/JobOverviewCard";
import TabNav from "../components/common/TabNav/TabNav";
import {
  getAssignRecruiter,
  removeWorkOnRequest,
  workOnRequest,
} from "../services/operations/jobAPI";
import RecruiterTable from "../components/common/Job/Assignrecruiter/RectruiterTable";
import AppButton from "../components/common/Button/AppButton";
import Modal from "../components/common/Modal/Modal";

const AssignRecruiter = () => {
  const tabs = [
    { name: <span>Job Details</span>, css: "" },
    { name: <span>Assign Recruiter</span>, css: "" },
  ];
  const tabKeys = ["Job Details", "Assign Recruiter"];
  const defaultTab = "Job Details";

  const [job, setJob] = useState(null);
  const [recruiters, setRecruiters] = useState([]);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedRecruiters, setSelectedRecruiters] = useState({});
  const [searchParams, setSearchParams] = useSearchParams();
  const jobId = searchParams.get("jobID");
  const currentTab = searchParams.get("tab");

  useEffect(() => {
    if (!currentTab) {
      setSearchParams({ jobID: jobId, tab: defaultTab }, { replace: true });
    }
  }, [currentTab, setSearchParams, jobId]);

  const [activeTab, setActiveTab] = useState(() =>
    tabKeys.includes(currentTab) ? currentTab : defaultTab
  );

  useEffect(() => {
    const tab = searchParams.get("tab") || defaultTab;
    setActiveTab(tabKeys.includes(tab) ? tab : defaultTab);
  }, [searchParams]);

  const handleTabChange = (tabIndex) => {
    setSearchParams({ jobID: jobId, tab: tabKeys[tabIndex] });
  };

  const fetchJob = async () => {
    try {
      setError("");
      const response = await getAssignRecruiter(jobId);
      if (response?.success) {
        // If response.data is an array, use the first item
        const jobData = Array.isArray(response.data)
          ? response.data[0]
          : response.data;
        setJob(jobData);
        setRecruiters(
          (jobData.recruiters || []).map((rec) => {
            return {
              userID: rec.userId,
              _id: rec._id,
              name: `${rec.name?.firstName || ""} ${
                rec.name?.lastName || ""
              }`.trim(),
              specialization: rec?.recruiterInfo?.domain || "",
              jobsWorkingOn: rec.recruiterInfo?.jobsWorkingOnCount || 0,
              totalSubmissions: rec.candidatesubmissionCount || 0,
              status: rec.assignStatus ? rec.assignStatus : null,
            };
          })
        );
        setSelectedRecruiters({});
      } else {
        setError(response?.message || "Failed to fetch job details.");
      }
    } catch (error) {
      setError("Error fetching job details.");
    }
  };
  useEffect(() => {
    if (jobId) fetchJob();
  }, [jobId]);

  async function assignRecruiter(recruiter) {
    try {
      const isRecruiterSelect = Object.keys(recruiter)?.filter(
        (item) => recruiter[item]
      );

      const recruiterItem = recruiters
        ?.filter((item) => {
          return isRecruiterSelect.includes(item?.userID);
        })
        ?.map((item) => {
          return { userID: item?.userID, _id: item?._id };
        });
      const postData = {
        jobId: jobId,
        recruiters: recruiterItem,
      };
      await workOnRequest(postData);
      setShowModal(false);
      fetchJob();
    } catch (error) {
      console.log(error);
      setError("Error assign recruiters.");
    }
  }

  async function removeRecruiter(recruiter) {
    try {
      await removeWorkOnRequest({
        jobId: jobId,
        recruiter: recruiter,
      });
      fetchJob();
    } catch (error) {
      console.log(error);
      setError("Error remove recruiters.");
    }
  }

  return (
    <div className="w-full mx-auto p-2 space-y-6">
      <JobOverviewCard job={job} />
      <TabNav
        nav={tabs}
        active={tabKeys.indexOf(activeTab)}
        setActive={handleTabChange}
        rightSidebar={
          <>
            <AppButton
              disable={Object.values(selectedRecruiters).every((v) => !v)}
              onClick={() => setShowModal(true)}
              label={"Assign Job"}
            />
            <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
              <div className="pb-6 text-center">
                <div className="text-lg font-semibold">
                  Are you sure to assign the recruiter?
                </div>
                <div className="flex justify-end me-5 gap-4 mt-6">
                  <AppButton
                    label={"Cancel"}
                    variant="secondary"
                    onClick={() => {
                      setShowModal(false);
                    }}
                  />
                  <AppButton
                    label={"Yes, I am"}
                    onClick={() => {
                      assignRecruiter(selectedRecruiters);
                    }}
                  />
                </div>
              </div>
            </Modal>
          </>
        }
      />

      {!error && activeTab === "Job Details" && job && (
        <>
          <JobCard title="Details" defaultOpen={true}>
            <div className="grid grid-cols-5 gap-x-6 gap-y-4 text-sm text-gray-800 mb-4 text-left">
              <div>
                <div className="font-medium">Country:</div>
                <div>{job.location?.country || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">State:</div>
                <div>{job.location?.state || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Location:</div>
                <div>{job.location?.city || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Zip Code:</div>
                <div>{job.location?.zipCode || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Job Status:</div>
                <div>{job.jobStatus || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Hours/Week:</div>
                <div>{job.requiredHoursPerWeek || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Account Manager:</div>
                {job.accountManager && job.accountManager.email
                  ? job.accountManager.email
                  : "N/A"}
              </div>
              <div>
                <div className="font-medium">Client Bill Rate:</div>
                <div>{job.payRate || "N/A"}</div>
              </div>
              <div>
                <div className="font-medium">Pay Rate</div>
                {job.salary?.min && job.salary?.max
                  ? `${job.salary.min} - ${job.salary.max} ${
                      job.salary.currency || ""
                    }`
                  : "N/A"}
              </div>
              <div>
                <div className="font-medium">Remote Job:</div>
                <div>{job.remote ? "Yes" : "No"}</div>
              </div>
            </div>
          </JobCard>

          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <JobCard title="Benefits" defaultOpen={true}>
                <ul className="list-disc pl-5 space-y-1">
                  {(job?.benefits || []).map((benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </JobCard>
            </div>
            <div className="flex-1">
              <JobCard title="Skills" defaultOpen={true}>
                <ul className="list-disc pl-5 space-y-1">
                  {(job?.primarySkills || []).map((skill, i) => (
                    <li key={i}>{skill}</li>
                  ))}
                </ul>
              </JobCard>
            </div>
          </div>
          <JobCard title="Job Description" defaultOpen={true}>
            <p className="leading-relaxed">
              <span
                dangerouslySetInnerHTML={{
                  __html: job?.jobDescription || "N/A",
                }}
              />
            </p>
          </JobCard>
        </>
      )}
      {!error && activeTab === "Assign Recruiter" && (
        <RecruiterTable
          assignRecruiter={assignRecruiter}
          removeRecruiter={removeRecruiter}
          recruiterData={recruiters}
          setRecruiters={setRecruiters}
          selectedRecruiters={selectedRecruiters}
          setSelectedRecruiters={setSelectedRecruiters}
        />
      )}
    </div>
  );
};

export default AssignRecruiter;
