import React, { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

const DetailCard = ({ details }) => {
  const [open, setOpen] = useState(false);

  const detailItem = (label, value) => (
    <div className="flex flex-col min-w-[140px]">
      <span className="text-sm text-gray-500">{label}</span>
      <span className="text-sm font-medium text-gray-800">{value}</span>
    </div>
  );

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm w-full max-w-3xl">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setOpen(!open)}
      >
        <h3 className="text-lg font-semibold">Job Details</h3>
        {open ? (
          <ChevronUp className="text-gray-500" />
        ) : (
          <ChevronDown className="text-gray-500" />
        )}
      </div>

      {open && (
        <div className="mt-4 space-y-4">
          {/* Row 1: Country, State, Location, Zip Code, Job Status */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            {detailItem("Country", details.country)}
            {detailItem("State", details.state)}
            {detailItem("Location", details.location)}
            {detailItem("Zip Code", details.zipCode)}
            {detailItem("Job Status", details.status)}
          </div>

          {/* Row 2: Required Hours/Week, Account Manager, Bill Rate, Pay Rate, Remote Job */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            {detailItem("Required Hours/Week", details.hoursPerWeek)}
            {detailItem("Account Manager", details.accountManager)}
            {detailItem("Client Bill Rate", details.clientBillRate)}
            {detailItem("Pay Rate", details.payRate)}
            {detailItem("Remote Job", details.remote ? "Yes" : "No")}
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailCard;
