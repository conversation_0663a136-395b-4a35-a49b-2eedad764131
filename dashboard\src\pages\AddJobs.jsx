import React, { useEffect, useState } from "react";
import InputField from "../components/common/Input/InputField";
import { useFormik } from "formik";
import SelectField from "../components/common/Input/SelectionField";
import Select from "react-select";
import countries from "../data/countries.json";
import ChipField from "../components/common/Input/ChipField";
import MultiSelectionField from "../components/common/Input/MultiSelectionField";
import TextAreaInput from "../components/common/Input/RichTextEditor";
import AppButton from "../components/common/Button/AppButton";
import candidateRoles from "../data/candidateRoles.json";
import industries from "../data/industries.json";

import {
  createJOB,
  getJobByID,
  updateJob,
} from "../services/operations/jobAPI";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const AddJobs = () => {
  const userInfo = useSelector((item) => item.auth.user);
  const [visibility, setVisibility] = useState(true);
  const [loading, setLoading] = useState(false);

  const [jobData, setJobData] = useState(null);
  const navigate = useNavigate();

  const { search } = useLocation();

  const queryParams = new URLSearchParams(search);
  const jobID = queryParams.get("jobID");

  const jobFormik = useFormik({
    initialValues: {
      domain: "",
      jobProfile: "",
      externalJobId: "",
      jobType: "",
      jobTitle: "",
      remoteJob: "",
      country: "",
      state: "",
      location: "",
      zipCode: "",
      jobStatus: "",
      requiredHoursWeek: "",
      client: "",
      priority: "",
      primarySkill: [],
      benefits: "",
      noOfPosition: "",
      minMonthexperience: "",
      minYearexperience: "",
      maxMonthexperience: "",
      maxYearexperience: "",
      payRateSalaryType: "",
      payRateSalaryMin: "",
      payRateSalaryMax: "",
      clientBillRateType: "",
      clientBillRateMin: "",
      clientBillRateMax: "",
      jobDescription: "",
      commissionAmount: 1500,
      guaranteePeriod: 60,
    },
    validate: (data) => {
      let errors = {};

      if (!data.jobType) {
        errors.jobType = "Job type is required.";
      }

      if (!data.jobTitle) {
        errors.jobTitle = "Job title is required.";
      }

      if (!data.remoteJob) {
        errors.remoteJob = "Remote job status is required.";
      }

      if (!data.commissionAmount) {
        errors.commissionAmount = "commission Amount is required.";
      }
      if (!data.guaranteePeriod) {
        errors.guaranteePeriod = "guarantee Period is required.";
      }

      if (!data.country) {
        errors.country = "Country is required.";
      }

      if (!data.location) {
        errors.location = "Location is required.";
      }

      if (!data.jobStatus) {
        errors.jobStatus = "Job status is required.";
      }

      if (!data.priority) {
        errors.priority = "Priority is required.";
      }
      if (!data.primarySkill || data.primarySkill.length === 0) {
        errors.primarySkill = "At least one primary skill is required.";
      }

      if (!data.benefits) {
        errors.benefits = "Benefits are required.";
      }
      if (!data.domain) {
        errors.domain = "Domain are required.";
      }
      if (!data.jobProfile) {
        errors.jobProfile = "Job profile is required.";
      }
      if (!data.noOfPosition || data.noOfPosition < 1) {
        errors.noOfPosition =
          "Number of positions is required and must be at least 1.";
      }

      const minMonth = data.minMonthexperience;
      const minYear = data.minYearexperience;

      const hasMinMonth = minMonth !== "" && minMonth != null;
      const hasMinYear = minYear !== "" && minYear != null;

      if (!hasMinMonth && !hasMinYear) {
        errors.minMonthexperience =
          "At least one of year or month for minimum experience is required.";
      } else {
        if (hasMinMonth && Number(minMonth) < 0) {
          errors.minMonthexperience = "Month experience cannot be negative.";
        }
        if (hasMinYear && Number(minYear) < 0) {
          errors.minYearexperience = "Year experience cannot be negative.";
        }
      }

      // Maximum Experience Validation
      const maxMonth = data.maxMonthexperience;
      const maxYear = data.maxYearexperience;

      const hasMaxMonth = maxMonth !== "" && maxMonth != null;
      const hasMaxYear = maxYear !== "" && maxYear != null;

      if (!hasMaxMonth && !hasMaxYear) {
        errors.maxMonthexperience =
          "At least one of year or month for maximum experience is required.";
      } else {
        if (hasMaxMonth && Number(maxMonth) < 0) {
          errors.maxMonthexperience = "Month experience cannot be negative.";
        }
        if (hasMaxYear && Number(maxYear) < 0) {
          errors.maxYearexperience = "Year experience cannot be negative.";
        }
      }

      if (!data.payRateSalaryMin) {
        errors.payRateSalaryMin = "Minimum pay rate/salary is required.";
      }

      if (!data.payRateSalaryMax) {
        errors.payRateSalaryMax = "Maximum pay rate/salary is required.";
      }

      if (data.jobType === "contract") {
        if (!data.payRateSalaryType) {
          errors.payRateSalaryType =
            "Pay rate/salary type is required for contract jobs.";
        }
      }

      if (!data.jobDescription) {
        errors.jobDescription = "Job description is required.";
      }
      return errors;
    },
    onSubmit: (data) => {
      if (jobID) {
        updateJobs({ ...data, remoteJob: data.remoteJob ? true : false });
      } else {
        creatJob({ ...data, remoteJob: data.remoteJob ? true : false });
      }
    },
  });

  const isFormFieldValid = (name) =>
    !!(jobFormik.touched[name] && jobFormik.errors[name]);
  const getFormErrorMessage = (name) => {
    return (
      isFormFieldValid(name) && (
        <small className="text-red-500">{jobFormik.errors[name]}</small>
      )
    );
  };

  async function creatJob(data) {
    try {
      setLoading(true);

      const resbody = {
        jobTitle: data?.jobTitle,
        domain: data?.domain,
        commissionAmount: data?.commissionAmount,
        commissionCurrency: "USD",
        jobCountry: data?.country,
        jobCity: data?.location,
        jobState: data?.state ? data?.state : "N/A",
        JobZipCode: data?.zipCode,
        experience: {
          min: `${data?.minYearexperience}.${data?.minMonthexperience}`,
          max: `${data?.maxYearexperience}.${data?.maxMonthexperience}`,
          unit: "years",
        },
        jobStatus: data?.jobStatus,
        jobType: data?.jobType,
        remote: data?.remoteJob == "true" ? true : false,
        requiredHoursPerWeek: data.requiredHoursWeek
          ? data.requiredHoursWeek
          : 40,
        priority: data?.priority,
        openings: data?.noOfPosition,
        salary: {
          min: data?.payRateSalaryMin,
          max: data?.payRateSalaryMax,
          currency: "USD",
        },
        payRate: data?.payRateSalaryType,
        clinetName: data?.client,
        primarySkills: data?.primarySkill,
        benefits: data?.benefits,
        jobDescription: data?.jobDescription,
        jobID: data?.externalJobId,
        guaranteePeriod: data?.guaranteePeriod,
        jobProfile: data?.jobProfile,
      };

      const response = await createJOB(resbody);

      if (response.success) {
        navigate(`/jobs?tabs=3`);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }

  async function getJobByJobID(jobID) {
    try {
      const job = await getJobByID(jobID);
      const jobData = job?.data[0];

      setVisibility(jobData?.visibility ?? true);
      setJobData(jobData);

      jobFormik.setFieldValue("country", jobData?.location?.country);

      // --- Handle state (just use name directly if available) ---
      jobFormik.setFieldValue("state", jobData?.location?.state || "");

      jobFormik.setFieldValue("jobTitle", jobData?.jobTitle);
      jobFormik.setFieldValue("domain", jobData?.industry);
      jobFormik.setFieldValue("commissionAmount", jobData?.commission?.amount);
      // jobFormik.setFieldValue("country", jobData?.location?.country);
      jobFormik.setFieldValue("location", jobData?.location?.city);
      jobFormik.setFieldValue("zipCode", jobData?.location?.zipCode);
      // jobFormik.setFieldValue("state", jobData?.location?.state);
      jobFormik.setFieldValue(
        "guaranteePeriod",
        jobData?.guaranteePeriod ? jobData?.guaranteePeriod : 0
      );
      jobFormik.setFieldValue("externalJobId", jobData?.externalJobId);
      jobFormik.setFieldValue("jobType", jobData?.jobType);
      jobFormik.setFieldValue("remoteJob", jobData?.remote ? "yes" : "no");
      jobFormik.setFieldValue("jobStatus", jobData?.jobStatus);
      jobFormik.setFieldValue(
        "requiredHoursWeek",
        jobData?.requiredHoursPerWeek
      );
      jobFormik.setFieldValue("client", jobData?.clientname);
      jobFormik.setFieldValue("primarySkill", jobData?.primarySkills);
      jobFormik.setFieldValue("benefits", jobData?.benefits);
      jobFormik.setFieldValue("noOfPosition", jobData?.openings);
      jobFormik.setFieldValue(
        "minMonthexperience",
        jobData?.experience?.min?.toString()?.split(".")[1]
          ? +jobData?.experience?.min?.toString()?.split(".")[1]
          : 0
      );
      jobFormik.setFieldValue(
        "minYearexperience",
        jobData?.experience?.min?.toString()?.split(".")[0]
          ? +jobData?.experience?.min?.toString()?.split(".")[0]
          : 0
      );
      jobFormik.setFieldValue(
        "maxMonthexperience",
        jobData?.experience?.max?.toString()?.split(".")[1]
          ? +jobData?.experience?.max?.toString()?.split(".")[1]
          : 0
      );
      jobFormik.setFieldValue(
        "maxYearexperience",
        jobData?.experience?.max?.toString()?.split(".")[0]
          ? +jobData?.experience?.max?.toString()?.split(".")[0]
          : 0
      );
      jobFormik.setFieldValue("payRateSalaryType", jobData?.payRate);
      jobFormik.setFieldValue(
        "payRateSalaryMin",
        jobData?.salary?.min ? jobData?.salary?.min : 0
      );
      jobFormik.setFieldValue(
        "payRateSalaryMax",
        jobData?.salary?.max ? jobData?.salary?.max : 0
      );
      jobFormik.setFieldValue("jobDescription", jobData?.jobDescription);
      jobFormik.setFieldValue("priority", jobData?.priority);
      jobFormik.setFieldValue("jobProfile", jobData?.jobProfile || "");
    } catch (error) {
      console.log(error);
    }
  }

  async function updateJobs(data) {
    try {
      setLoading(true);

      const resbody = {
        jobID: jobData?.jobId,
        jobTitle: data?.jobTitle,
        domain: data?.domain,
        commissionAmount: data?.commissionAmount,
        commissionCurrency: "USD",
        jobCountry: data?.country,
        jobCity: data?.location,
        jobState: data?.state ? data?.state : "N/A",
        JobZipCode: data?.zipCode,
        experience: {
          min: `${data?.minYearexperience}.${data?.minMonthexperience}`,
          max: `${data?.maxYearexperience}.${data?.maxMonthexperience}`,
          unit: "years",
        },
        jobStatus: data?.jobStatus,
        jobType: data?.jobType,
        remote: data?.remoteJob == "true" ? true : false,
        requiredHoursPerWeek: data.requiredHoursWeek
          ? data.requiredHoursWeek
          : 40,
        priority: data?.priority,
        openings: data?.noOfPosition,
        salary: {
          min: data?.payRateSalaryMin,
          max: data?.payRateSalaryMax,
          currency: "USD",
        },
        payRate: data?.payRateSalaryType,
        clinetName: data?.client,
        primarySkills: data?.primarySkill,
        benefits: data?.benefits,
        jobDescription: data?.jobDescription,
        externalJobId: data?.externalJobId,
        guaranteePeriod: data?.guaranteePeriod,
        jobProfile: data?.jobProfile,
      };

      const response = await updateJob(resbody, userInfo);
      if (response.success) {
        navigate(`/jobs?tabs=3`);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }

  useEffect(() => {
    if (jobID) {
      getJobByJobID(jobID);
    }
  }, [jobID]);

  return (
    <>
      <div className="grid grid-cols-3 gap-4 p-3">
        <InputField
          label="External Job ID"
          placeholder="Enter Job ID"
          value={jobFormik.values.externalJobId}
          onChange={jobFormik.handleChange}
          name={"externalJobId"}
          id={"externalJobId"}
        />
        <SelectField
          label="Job Type"
          required={true}
          errorMessage={getFormErrorMessage("jobType")}
          placeholder={"Select Job Type"}
          options={[
            { value: "full-time", label: "Full Time" },
            { value: "contract", label: "Contract" },
          ]}
          value={jobFormik.values.jobType}
          onChange={(e) => {
            jobFormik.handleChange(e);
            jobFormik.setFieldValue("payRateSalaryType", "");
          }}
          name={"jobType"}
          id={"jobType"}
        />
        <InputField
          label="Job Title"
          required={true}
          errorMessage={getFormErrorMessage("jobTitle")}
          placeholder="Enter Job Title"
          value={jobFormik.values.jobTitle}
          onChange={jobFormik.handleChange}
          name={"jobTitle"}
          id={"jobTitle"}
        />

        <SelectField
          label="Domain"
          required={true}
          errorMessage={getFormErrorMessage("domain")}
          placeholder={"Select Domain"}
          options={industries.map((industry) => ({
            label: industry.name,
            value: industry.name, // Ensure ID is a string if your backend expects it
          }))}
          value={jobFormik.values.domain}
          onChange={jobFormik.handleChange}
          name={"domain"}
          id={"domain"}
        />
        <SelectField
          label="Job Profile"
          required={true}
          errorMessage={getFormErrorMessage("jobProfile")}
          placeholder={"Select Job Profile"}
          options={candidateRoles.map((role) => ({
            label: role.name,
            value: role.name,
          }))}
          value={jobFormik.values.jobProfile}
          onChange={jobFormik.handleChange}
          name={"jobProfile"}
          id={"jobProfile"}
        />

        <SelectField
          label="Remote Job"
          required={true}
          placeholder={"Select Remote Job"}
          errorMessage={getFormErrorMessage("remoteJob")}
          options={[
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" },
          ]}
          value={jobFormik.values.remoteJob}
          onChange={jobFormik.handleChange}
          name={"remoteJob"}
          id={"remoteJob"}
        />

        {/* country , state  */}
        <SelectField
          label="Country"
          required
          placeholder={"Select State"}
          options={countries.map((country) => ({
            value: country.name,
            label: country.name,
          }))}
          errorMessage={getFormErrorMessage("country")}
          value={jobFormik.values.country}
          onChange={jobFormik.handleChange}
          name={"country"}
          id={"country"}
        />

        <SelectField
          label="State"
          required
          placeholder={"Select State"}
          disable={jobFormik.values.country ? false : true}
          options={
            countries
              .find((country) => country.name === jobFormik.values.country)
              ?.states.map((state) => ({
                value: state.name,
                label: state.name,
              })) || []
          }
          value={jobFormik.values.state}
          onChange={jobFormik.handleChange}
          errorMessage={getFormErrorMessage("state")}
          name={"state"}
          id={"state"}
        />

        <InputField
          label="Location"
          required={true}
          placeholder="Enter Location"
          errorMessage={getFormErrorMessage("location")}
          value={jobFormik.values.location}
          onChange={jobFormik.handleChange}
          name={"location"}
          id={"location"}
        />

        <InputField
          label="Zip Code"
          placeholder="Enter Zip Code"
          value={jobFormik.values.zipCode}
          onChange={jobFormik.handleChange}
          name={"zipCode"}
          id={"zipCode"}
        />

        <SelectField
          label="Job Status"
          required={true}
          errorMessage={getFormErrorMessage("jobStatus")}
          placeholder={"Select Job Status"}
          options={[
            { value: "Active", label: "Active" },
            { value: "Inactive", label: "Inactive" },
            { value: "Onhold", label: "Onhold" },
            { value: "Holdbyclient", label: "Holdbyclient" },
            { value: "Filled", label: "Filled" },
            { value: "Cancelled", label: "Cancelled" },
            { value: "Closed", label: "Closed" },
          ]}
          value={jobFormik.values.jobStatus}
          onChange={jobFormik.handleChange}
          name={"jobStatus"}
          id={"jobStatus"}
        />

        <InputField
          label="Required Hours/Week"
          placeholder="Enter Required Hours/Week"
          value={jobFormik.values.requiredHoursWeek}
          onChange={jobFormik.handleChange}
          name={"requiredHoursWeek"}
          type="number"
          id={"requiredHoursWeek"}
        />

        <InputField
          label="Client "
          placeholder="Enter Client"
          value={jobFormik.values.client}
          onChange={jobFormik.handleChange}
          name={"client"}
          id={"client"}
        />

        <SelectField
          label="Job Priority"
          required={true}
          errorMessage={getFormErrorMessage("priority")}
          placeholder={"Select Job Priority"}
          options={[
            { value: "low priority", label: "low priority" },
            { value: "medium priority", label: "medium priority" },
            { value: "high priority", label: "high priority" },
          ]}
          value={jobFormik.values.priority}
          onChange={jobFormik.handleChange}
          name={"priority"}
          id={"priority"}
        />

        <ChipField
          label={"Primary Skill"}
          onChange={(e) => {
            jobFormik.setFieldValue("primarySkill", e);
          }}
          placeholder={"Primary Skill"}
          value={jobFormik.values.primarySkill}
          required={true}
          errorMessage={getFormErrorMessage("primarySkill")}
        />
        <MultiSelectionField
          label="Benefits"
          options={[
            "Life Insurance",
            "Dental Insurance",
            "Retirement",
            "401K",
            "Medical Insurance",
            "Vision Insurance",
            "Paid Time Off",
            "Equity",
            "Wellness Programs",
            "Employee Assistance Programs",
            "Full Benefits",
          ]}
          required={true}
          errorMessage={getFormErrorMessage("benefits")}
          selected={jobFormik.values.benefits}
          setSelected={(e) => {
            jobFormik.setFieldValue("benefits", e);
          }}
        />
        <InputField
          label="No. of Position"
          required={true}
          type="number"
          placeholder="Enter No"
          value={jobFormik.values.noOfPosition}
          onChange={jobFormik.handleChange}
          name={"noOfPosition"}
          errorMessage={getFormErrorMessage("noOfPosition")}
          id={"noOfPosition"}
        />
        <div>
          <label className="text-sm font-medium text-gray-700">
            Min Experience <span className="text-red-800">*</span>
          </label>
          <div className="grid grid-cols-2 gap-4">
            <InputField
              required={true}
              type="number"
              placeholder="Year"
              value={jobFormik.values.minYearexperience}
              onChange={jobFormik.handleChange}
              name={"minYearexperience"}
              id={"minYearexperience"}
            />
            <InputField
              required={true}
              type="number"
              placeholder="Month"
              value={jobFormik.values.minMonthexperience}
              onChange={jobFormik.handleChange}
              name={"minMonthexperience"}
              id={"minMonthexperience"}
            />
          </div>
          <div>{getFormErrorMessage("minMonthexperience")}</div>
        </div>

        <div>
          <label className="text-sm font-medium text-gray-700">
            Max Experience <span className="text-red-800">*</span>
          </label>
          <div className="grid grid-cols-2 gap-4">
            <InputField
              required={true}
              type="number"
              placeholder="Year"
              value={jobFormik.values.maxYearexperience}
              onChange={jobFormik.handleChange}
              name={"maxYearexperience"}
              id={"maxYearexperience"}
            />
            <InputField
              required={true}
              type="number"
              placeholder="Month"
              value={jobFormik.values.maxMonthexperience}
              onChange={jobFormik.handleChange}
              name={"maxMonthexperience"}
              id={"maxMonthexperience"}
            />
          </div>
          {getFormErrorMessage("maxMonthexperience")}
        </div>
        {jobFormik.values.jobType == "contract" && (
          <SelectField
            label="Pay Rate/Salary"
            required={jobFormik.values.jobType == "contract" ? true : false}
            errorMessage={getFormErrorMessage("payRateSalaryType")}
            placeholder={"Select"}
            options={[
              { value: "Hourly", label: "Hourly" },
              { value: "Bi Weekly", label: "Bi Weekly" },
              { value: "Monthly", label: "Monthly" },
              { value: "Yearly", label: "Yearly" },
            ]}
            value={jobFormik.values.payRateSalaryType}
            onChange={jobFormik.handleChange}
            name={"payRateSalaryType"}
            id={"payRateSalaryType"}
          />
        )}

        <div>
          <label className="text-sm font-medium text-gray-700">
            Pay Rate/Salary USD($)
            <span className="text-red-800">*</span>
          </label>
          <div className="grid grid-cols-2 gap-4">
            <InputField
              type="number"
              placeholder="Min"
              value={jobFormik.values.payRateSalaryMin}
              onChange={jobFormik.handleChange}
              errorMessage={getFormErrorMessage("payRateSalaryMin")}
              name={"payRateSalaryMin"}
              id={"payRateSalaryMin"}
            />
            <InputField
              type="number"
              placeholder="Max"
              value={jobFormik.values.payRateSalaryMax}
              onChange={jobFormik.handleChange}
              errorMessage={getFormErrorMessage("payRateSalaryMax")}
              name={"payRateSalaryMax"}
              id={"payRateSalaryMax"}
            />
          </div>
        </div>

        <SelectField
          label="Client Bill Rate/Salary "
          placeholder={"Select"}
          options={[
            { value: "Hourly", label: "Hourly" },
            { value: "Bi Weekly", label: "Bi Weekly" },
            { value: "Monthly", label: "Monthly" },
            { value: "Yearly", label: "Yearly" },
          ]}
          value={jobFormik.values.clientBillRateType}
          onChange={jobFormik.handleChange}
          name={"clientBillRateType"}
          id={"clientBillRateType"}
        />

        <div>
          <label className="text-sm font-medium text-gray-700">
            Client Bill Rate/Salary
          </label>
          <div className="grid grid-cols-2 gap-4">
            <InputField
              required={true}
              type="number"
              placeholder="Min"
              value={jobFormik.values.clientBillRateMin}
              onChange={jobFormik.handleChange}
              name={"clientBillRateMin"}
              id={"clientBillRateMin"}
            />
            <InputField
              required={true}
              type="number"
              placeholder="Max"
              value={jobFormik.values.clientBillRateMax}
              onChange={jobFormik.handleChange}
              name={"clientBillRateMax"}
              id={"clientBillRateMax"}
            />
          </div>
        </div>
        <InputField
          label="Commission"
          required={true}
          type="number"
          placeholder="Enter Commission"
          value={jobFormik.values.commissionAmount}
          onChange={jobFormik.handleChange}
          name={"commissionAmount"}
          errorMessage={getFormErrorMessage("commissionAmount")}
          id={"commissionAmount"}
        />

        <InputField
          label="Guarantee Period"
          required={true}
          type="number"
          placeholder="Enter guarantee Period"
          value={jobFormik.values.guaranteePeriod}
          onChange={jobFormik.handleChange}
          name={"guaranteePeriod"}
          errorMessage={getFormErrorMessage("guaranteePeriod")}
          id={"guaranteePeriod"}
        />
      </div>

      <div className="p-3">
        <TextAreaInput
          label={"Job Description"}
          onChange={(e) => {
            jobFormik.setFieldValue("jobDescription", e);
          }}
          errorMessage={getFormErrorMessage("jobDescription")}
          value={jobFormik.values.jobDescription}
          placeholder={"Enter text here....."}
          row={10}
          required={true}
        />

        <div className="flex gap-4 mt-4">
          <AppButton
            label={visibility ? "Save" : "Save & Publish"}
            onClick={(e) => {
              e.preventDefault();
              jobFormik.handleSubmit();
            }}
            type="button"
            variant={visibility ? "primary" : "primary"}
          />

          <AppButton
            label="Cancel"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/jobs?tabs=0`);
              jobFormik.resetForm();
            }}
            variant="secondary"
          />
        </div>
      </div>
    </>
  );
};

export default AddJobs;
