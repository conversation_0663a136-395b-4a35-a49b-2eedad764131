import { apiConnector } from "../apiConnector";
import { chatEndpoints } from "../apis";

const { CREATE_CHAT_API, GET_CHAT_DETAILS_API, SEND_MESSAGE_API } =
  chatEndpoints;

export const createChat = async (payload) => {
  const response = await apiConnector("POST", CREATE_CHAT_API, payload);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getChatDetails = async (chatId) => {
  const response = await apiConnector(
    "GET",
    `${GET_CHAT_DETAILS_API}`,
    {},
    {},
    { chatId }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const sendMessage = async (payload) => {
  const response = await apiConnector("POST", SEND_MESSAGE_API, payload);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
