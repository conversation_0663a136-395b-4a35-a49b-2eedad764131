import React, { useEffect } from "react";
import { useFormikContext, FieldArray } from "formik";
import InputField from "../../../../common/Input/InputField";
import SelectField from "../../../../common/Input/SelectionField";

const currentYear = new Date().getFullYear();
const graduationYears = Array.from({ length: 50 }, (_, i) => ({
  label: `${currentYear - i}`,
  value: `${currentYear - i}`,
}));

const initialEducation = { degree: "", collegeName: "", graduationYear: "" };

const EducationForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
    setFieldValue,
  } = useFormikContext();

  // Ensure at least one card always exists and filter out nulls
  useEffect(() => {
    if (!values.education || values.education.length === 0) {
      setFieldValue("education", [initialEducation]);
    } else if (values.education.some((e) => e === null)) {
      setFieldValue("education", values.education.filter(Boolean));
    }
  }, [values.education, setFieldValue]);

  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  return (
    <form>
      <FieldArray name="education">
        {({ push, remove }) => (
          <div>
            {(values.education || []).filter(Boolean).map((edu, idx) => (
              <div key={idx}>
                {idx > 0 && <hr className="my-6 border-slate-300" />}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2 p-4 rounded">
                  <InputField
                    label="Degree"
                    name={`education[${idx}].degree`}
                    value={edu.degree}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    required
                    placeholder="Enter degree"
                    errorMessage={
                      touched.education &&
                      touched.education[idx] &&
                      errors.education &&
                      errors.education[idx] &&
                      errors.education[idx].degree
                    }
                  />
                  <InputField
                    label="School Attended"
                    name={`education[${idx}].collegeName`}
                    value={edu.collegeName}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    required
                    placeholder="Enter school name"
                    errorMessage={
                      touched.education &&
                      touched.education[idx] &&
                      errors.education &&
                      errors.education[idx] &&
                      errors.education[idx].collegeName
                    }
                  />
                  <SelectField
                    label="Graduation Year"
                    name={`education[${idx}].graduationYear`}
                    value={edu.graduationYear}
                    onChange={handleChange}
                    // onBlur={handleBlur}
                    // required
                    placeholder="Select year"
                    options={graduationYears}
                    // errorMessage={
                    //   touched.education &&
                    //   touched.education[idx] &&
                    //   errors.education &&
                    //   errors.education[idx] &&
                    //   errors.education[idx].graduationYear
                    // }
                  />
                  {values.education.length > 1 && (
                    <div className="col-span-2">
                      <button
                        type="button"
                        onClick={() => remove(idx)}
                        className="text-red-600 text-sm px-2 py-1 font-semibold rounded hover:underline"
                      >
                        Delete Education
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <button
              type="button"
              className="mt-4 px-4 py-2 rounded bg-white text-blue-600 text-sm cursor-pointer hover:underline"
              onClick={() => push(initialEducation)}
            >
              + Add Education
            </button>
            {typeof errors.education === "string" && (
              <div className="text-red-600 text-sm mt-2">
                {errors.education}
              </div>
            )}
          </div>
        )}
      </FieldArray>
    </form>
  );
};

export default EducationForm;
