import React, { useEffect, useState } from "react";
import { unstable_batchedUpdates } from "react-dom";
import TabNav from "../../components/common/TabNav/TabNav";
import { useLocation, useNavigate } from "react-router-dom";
import CandidateTable from "../../components/core/recruiter/candidates/CandidateTable";
import AppButton from "../../components/common/Button/AppButton";
import {
  getSubmission,
  getMaxExperience,
} from "../../services/operations/candidateAPI";
import { JobStatusOptions } from "../../utils/JobStatusOptions";
import EnhancedThreeDot from "../../components/common/Button/EnhancedThreeDot";
import LocationDropdown from "../../components/core/recruiter/candidates/LocationDropdown";
import StatusDropdown from "../../components/core/recruiter/candidates/StatusDropdown";
import JobTypeDropdown from "../../components/core/recruiter/candidates/JobTypeDropdown";
import ExperienceLevelDropdown from "../../components/core/recruiter/candidates/ExperienceLevelDropdown";
import FilterBar from "../../components/core/recruiter/candidates/FilterBar";
import UniversalSearchDropdown from "../../components/core/recruiter/shared/UniversalSearchDropdown";
import SubmissionDateSortPill from "../../components/core/recruiter/candidates/SubmissionDateSortPill";

const Candidates = () => {
  const { search, pathname } = useLocation();
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(search);
  const currentTabs = searchParams.get("tabs");

  const [active, setActive] = useState(() => {
    const tabFromURL = searchParams.get("tabs");
    return tabFromURL ? Number(tabFromURL) : 0;
  });

  // Helper function to parse URL parameters immediately for initial state
  const parseInitialURLState = () => {
    try {
      // Safely handle search parameter
      const searchParam = search || "";
      const params = new URLSearchParams(searchParam);
      const initialActive = params.get("tabs") ? Number(params.get("tabs")) : 0;
      const tabPrefix = `tab${initialActive}_`;

      const parsedState = {
        appliedFilters: {},
        isFiltersApplied: false,
        appliedSorting: "",
        globalSearchState: {
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false,
        }
      };

      const location = params.get(`${tabPrefix}location`);
      if (location) {
        parsedState.appliedFilters.location = location.split(",");
        parsedState.isFiltersApplied = true;
      }

      const status = params.get(`${tabPrefix}status`);
      if (status) {
        parsedState.appliedFilters.status = status.split(",");
        parsedState.isFiltersApplied = true;
      }

      const jobType = params.get(`${tabPrefix}jobType`);
      if (jobType) {
        parsedState.appliedFilters.jobType = jobType.split(",");
        parsedState.isFiltersApplied = true;
      }

      const experienceLevel = params.get(`${tabPrefix}experienceLevel`);
      if (experienceLevel) {
        parsedState.appliedFilters.experienceLevel = [experienceLevel];
        parsedState.isFiltersApplied = true;
      }

      const submissionDate = params.get("submissionDate");
      if (submissionDate) {
        parsedState.appliedSorting = submissionDate;
      }

      const searchTerm = params.get("searchTerm");
      const searchField = params.get("searchField");
      if (searchTerm && searchField) {
        parsedState.globalSearchState = {
          searchTerm,
          searchField,
          isSearchApplied: true,
        };
      }

      return parsedState;
    } catch (error) {
      console.error("Error parsing initial URL state:", error);

      return {
        appliedFilters: {},
        isFiltersApplied: false,
        appliedSorting: "",
        globalSearchState: {
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false,
        }
      };
    }
  };

  // Helper function to update URL with current state 
  const updateURL = (newParams = {}, tabIndex = active) => {
    const urlParams = new URLSearchParams(search);

    if (currentTabs !== null) {
      urlParams.set("tabs", currentTabs);
    }

    const tabPrefix = `tab${tabIndex}_`;
    Array.from(urlParams.keys()).forEach((key) => {
      if (key.startsWith(tabPrefix)) {
        urlParams.delete(key);
      }
    });

    Object.entries(newParams).forEach(([key, value]) => {
      if (
        value &&
        value !== "" &&
        !(Array.isArray(value) && value.length === 0)
      ) {

        if (key === 'submissionDate' || key === 'searchTerm' || key === 'searchField') {
          if (Array.isArray(value)) {
            urlParams.set(key, value.join(","));
          } else {
            urlParams.set(key, value);
          }
        } else {
          const tabSpecificKey = `${tabPrefix}${key}`;
          if (Array.isArray(value)) {
            urlParams.set(tabSpecificKey, value.join(","));
          } else {
            urlParams.set(tabSpecificKey, value);
          }
        }
      }
    });

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });
  };

  // Helper function to parse URL parameters and restore state (tab-specific)
  const parseURLParams = (tabIndex = active) => {
    const params = new URLSearchParams(search);
    const parsedState = {};
    const tabPrefix = `tab${tabIndex}_`;

    const location = params.get(`${tabPrefix}location`);
    if (location) {
      parsedState.locationSelected = location.split(",");
    }
    const status = params.get(`${tabPrefix}status`);
    if (status) {
      parsedState.statusSelected = status.split(",");
    }
    const jobType = params.get(`${tabPrefix}jobType`);
    if (jobType) {
      parsedState.jobTypeSelected = jobType.split(",");
    }

    const experienceLevel = params.get(`${tabPrefix}experienceLevel`);
    if (experienceLevel) {
      const [min, max] = experienceLevel.split("-").map(Number);
      if (!isNaN(min) && !isNaN(max)) {
        parsedState.experienceLevelSelected = [min, max];
      }
    }

    const submissionDate = params.get('submissionDate');
    if (submissionDate) {
      parsedState.sortValue = submissionDate;
    }

    const searchTerm =
      params.get("searchTerm") || params.get(`${tabPrefix}searchTerm`);
    const searchField =
      params.get("searchField") || params.get(`${tabPrefix}searchField`);
    if (searchTerm && searchField) {
      parsedState.searchTerm = searchTerm;
      parsedState.searchField = searchField;
    }

    return parsedState;
  };

  // Helper function to save current tab state
  const saveCurrentTabState = (tabIndex = active) => {
    const currentState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      appliedFilters,
      isFiltersApplied,
    };

    setTabStates((prev) => ({
      ...prev,
      [tabIndex]: currentState,
    }));
  };


  // Enhanced inheritance function that preserves filters across tabs even when passing through unsupported tabs
  const inheritCompatibleFiltersWithGlobalState = (
    fromTabState,
    fromTabIndex,
    toTabIndex,
    updatedTabStates
  ) => {
    const toTabOptions = tabFilterOptions[toTabIndex];

    updateGlobalInheritanceState(fromTabState, fromTabIndex);

    if (!toTabOptions || toTabOptions.length === 0) {
      return {
        locationSelected: [],
        statusSelected: [],
        jobTypeSelected: [],
        experienceLevelSelected: [1, 12],
        appliedFilters: {},
        isFiltersApplied: false,
      };
    }

    const targetTabState = updatedTabStates[toTabIndex] || {
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      sortValue: "recent",
      appliedFilters: {},
      isFiltersApplied: false,
    };
    const inheritedState = { ...targetTabState };
    const inheritanceSource = globalInheritanceState;
    let hasInheritedFilters = false;
    const inheritedFilters = { ...targetTabState.appliedFilters };

    if (
      toTabOptions.includes("location") &&
      inheritanceSource.locationSelected?.length > 0
    ) {
      inheritedState.locationSelected = [...inheritanceSource.locationSelected];
      inheritedFilters.location = [...inheritanceSource.locationSelected];
      hasInheritedFilters = true;
    }
    if (
      toTabOptions.includes("status") &&
      inheritanceSource.statusSelected?.length > 0
    ) {
      inheritedState.statusSelected = [...inheritanceSource.statusSelected];
      inheritedFilters.status = [...inheritanceSource.statusSelected];
      hasInheritedFilters = true;
    }
    if (
      toTabOptions.includes("jobType") &&
      inheritanceSource.jobTypeSelected?.length > 0
    ) {
      inheritedState.jobTypeSelected = [...inheritanceSource.jobTypeSelected];
      inheritedFilters.jobType = [...inheritanceSource.jobTypeSelected];
      hasInheritedFilters = true;
    }
    if (
      toTabOptions.includes("experienceLevel") &&
      inheritanceSource.experienceLevelSelected &&
      (inheritanceSource.experienceLevelSelected[0] !== 1 ||
        inheritanceSource.experienceLevelSelected[1] !== 12)
    ) {
      inheritedState.experienceLevelSelected = [
        ...inheritanceSource.experienceLevelSelected,
      ];
      inheritedFilters.experienceLevel = [
        `${inheritanceSource.experienceLevelSelected[0]}-${inheritanceSource.experienceLevelSelected[1]}`,
      ];
      hasInheritedFilters = true;
    }

    const targetHadFilters = targetTabState.isFiltersApplied;
    if (hasInheritedFilters || targetHadFilters) {
      inheritedState.appliedFilters = inheritedFilters;
      inheritedState.isFiltersApplied = true;
    }
    updateGlobalInheritanceState(fromTabState, fromTabIndex);

    return inheritedState;
  };

  const updateGlobalInheritanceState = (tabState, tabIndex) => {
    const tabOptions = tabFilterOptions[tabIndex];

    setGlobalInheritanceState(prev => {
      const updated = { ...prev };

      if (!tabOptions || tabOptions.length === 0) {
        updated.lastUpdatedTab = tabIndex;
        return updated;
      }

      return updated;
    });

    if (!tabOptions || tabOptions.length === 0) return;

    setGlobalInheritanceState((prev) => {
      const updated = { ...prev };
      if (
        tabOptions.includes("location") &&
        tabState.locationSelected?.length > 0
      ) {
        updated.locationSelected = [...tabState.locationSelected];
        updated.appliedFilters = {
          ...updated.appliedFilters,
          location: [...tabState.locationSelected],
        };
      }

      if (
        tabOptions.includes("status") &&
        tabState.statusSelected?.length > 0
      ) {
        updated.statusSelected = [...tabState.statusSelected];
        updated.appliedFilters = {
          ...updated.appliedFilters,
          status: [...tabState.statusSelected],
        };
      }

      if (
        tabOptions.includes("jobType") &&
        tabState.jobTypeSelected?.length > 0
      ) {
        updated.jobTypeSelected = [...tabState.jobTypeSelected];
        updated.appliedFilters = {
          ...updated.appliedFilters,
          jobType: [...tabState.jobTypeSelected],
        };
      }

      if (
        tabOptions.includes("experienceLevel") &&
        tabState.experienceLevelSelected &&
        (tabState.experienceLevelSelected[0] !== 1 ||
          tabState.experienceLevelSelected[1] !== 12)
      ) {
        updated.experienceLevelSelected = [...tabState.experienceLevelSelected];
        updated.appliedFilters = {
          ...updated.appliedFilters,
          experienceLevel: [
            `${tabState.experienceLevelSelected[0]}-${tabState.experienceLevelSelected[1]}`,
          ],
        };
      }

      if (tabState.sortValue && tabState.sortValue !== "recent") {
        updated.sortValue = tabState.sortValue;
      }

      const hasFilters = Object.keys(updated.appliedFilters).some((key) =>
        Array.isArray(updated.appliedFilters[key])
          ? updated.appliedFilters[key].length > 0
          : updated.appliedFilters[key]
      );
      updated.isFiltersApplied =
        hasFilters || (updated.sortValue && updated.sortValue !== "recent");
      updated.lastUpdatedTab = tabIndex;

      return updated;
    });
  };

  const tabs = [
    { name: <span>Active Submissions</span>, css: "" },
    { name: <span>Hired Candidates</span>, css: "" },
    { name: <span>Rejected Candidates</span>, css: "" },
    { name: <span>All Submissions</span>, css: "" },
  ];
  const [showFilter, setShowFilter] = useState(false);

  const [locationSelected, setLocationSelected] = useState(() => {
    try {
      return initialURLState?.appliedFilters?.location || [];
    } catch (error) {
      console.error("Error initializing locationSelected:", error);
      return [];
    }
  });

  const [statusSelected, setStatusSelected] = useState(() => {
    try {
      return initialURLState?.appliedFilters?.status || [];
    } catch (error) {
      console.error("Error initializing statusSelected:", error);
      return [];
    }
  });

  const [jobTypeSelected, setJobTypeSelected] = useState(() => {
    try {
      return initialURLState?.appliedFilters?.jobType || [];
    } catch (error) {
      console.error("Error initializing jobTypeSelected:", error);
      return [];
    }
  });

  const [experienceLevelSelected, setExperienceLevelSelected] = useState(() => {
    try {
      if (initialURLState?.appliedFilters?.experienceLevel && initialURLState.appliedFilters.experienceLevel[0]) {
        const [min, max] = initialURLState.appliedFilters.experienceLevel[0].split('-').map(Number);
        return [min, max];
      }
      return [1, 12];
    } catch (error) {
      console.error("Error initializing experienceLevelSelected:", error);
      return [1, 12];
    }
  });

  const [showSort, setShowSort] = useState(false);
  const [sortValue, setSortValue] = useState(() => {
    try {
      return initialURLState?.appliedSorting || "";
    } catch (error) {
      console.error("Error initializing sortValue:", error);
      return "";
    }
  });
  const [maxExperience, setMaxExperience] = useState(12);
  const [showSearch, setShowSearch] = useState(false);

  const initialURLState = parseInitialURLState();

  const [appliedFilters, setAppliedFilters] = useState(() => {
    try {
      return initialURLState?.appliedFilters || {};
    } catch (error) {
      console.error("Error initializing appliedFilters:", error);
      return {};
    }
  });

  const [isFiltersApplied, setIsFiltersApplied] = useState(() => {
    try {
      return initialURLState?.isFiltersApplied || false;
    } catch (error) {
      console.error("Error initializing isFiltersApplied:", error);
      return false;
    }
  });

  const [shouldShowFilterBar, setShouldShowFilterBar] = useState(false);

  const [appliedSorting, setAppliedSorting] = useState(() => {
    try {
      return initialURLState?.appliedSorting || "";
    } catch (error) {
      console.error("Error initializing appliedSorting:", error);
      return "";
    }
  });

  const shouldShowFilters = isFiltersApplied && Object.keys(appliedFilters).length > 0;
  const shouldShowSorting = appliedSorting && appliedSorting.trim() !== "";

  const [globalSearchState, setGlobalSearchState] = useState(() => {
    try {
      return initialURLState?.globalSearchState || {
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false,
      };
    } catch (error) {
      console.error("Error initializing globalSearchState:", error);
      return {
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false,
      };
    }
  });


  const tabFilterOptions = {
    0: ["location", "status"], 
    1: ["location", "jobType", "experienceLevel"], 
    2: [], 
    3: ["location", "status", "jobType", "experienceLevel"],
  };

  const [globalInheritanceState, setGlobalInheritanceState] = useState({
    locationSelected: [],
    statusSelected: [],
    jobTypeSelected: [],
    experienceLevelSelected: [1, 12],
    appliedFilters: {},
    isFiltersApplied: false,
    lastUpdatedTab: null, 
  });


  const [tabStates, setTabStates] = useState({
    0: {
      
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
    },
    1: {
    
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
    },
    2: {
    
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
    },
    3: {
    
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
    },
  });

  const filterOptions = {
    0: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status",
      },
    ],
    1: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location1",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType1",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel1",
      },
    ],
    3: [
      {
        label: "Location",
        component: (
          <LocationDropdown
            selected={locationSelected}
            setSelected={setLocationSelected}
          />
        ),
        selected: locationSelected,
        placeholder: "Select Location",
        key: "location3",
      },
      {
        label: "Status",
        component: (
          <StatusDropdown
            selected={statusSelected}
            setSelected={setStatusSelected}
          />
        ),
        selected: statusSelected,
        placeholder: "Select Status",
        key: "status3",
      },
      {
        label: "Job Type",
        component: (
          <JobTypeDropdown
            selected={jobTypeSelected}
            setSelected={setJobTypeSelected}
          />
        ),
        selected: jobTypeSelected,
        placeholder: "Select Job Type",
        key: "jobType3",
      },
      {
        label: "Experience Level",
        component: (
          <ExperienceLevelDropdown
            selected={experienceLevelSelected}
            setSelected={setExperienceLevelSelected}
            min={1}
            max={maxExperience}
          />
        ),
        selected: experienceLevelSelected,
        placeholder: "Select Experience Level",
        key: "experienceLevel3",
      },
    ],
  };


  const [isManualTabSwitch, setIsManualTabSwitch] = useState(false);


  useEffect(() => {
    if (isManualTabSwitch) {
      setIsManualTabSwitch(false);
      return;
    }

    const urlState = parseURLParams(active);

    if (Object.keys(urlState).length > 0) {
      if (urlState.locationSelected) {
        setLocationSelected(urlState.locationSelected);
      }
      if (urlState.statusSelected) {
        setStatusSelected(urlState.statusSelected);
      }
      if (urlState.jobTypeSelected) {
        setJobTypeSelected(urlState.jobTypeSelected);
      }
      if (urlState.experienceLevelSelected) {
        setExperienceLevelSelected(urlState.experienceLevelSelected);
      }
      if (urlState.sortValue) {
        setSortValue(urlState.sortValue);
        setAppliedSorting(urlState.sortValue);
      }

      const filters = {};

      if (urlState.locationSelected)
        filters.location = urlState.locationSelected;
      if (urlState.statusSelected) filters.status = urlState.statusSelected;
      if (urlState.jobTypeSelected) filters.jobType = urlState.jobTypeSelected;
      if (urlState.experienceLevelSelected) {
        const [min, max] = urlState.experienceLevelSelected;
        if (min !== 1 || max !== 12) {
          filters.experienceLevel = [`${min}-${max}`];
        }
      }
      if (urlState.searchTerm && urlState.searchField) {
        setGlobalSearchState({
          searchTerm: urlState.searchTerm,
          searchField: urlState.searchField,
          isSearchApplied: true,
        });
      } else {

        setGlobalSearchState({
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false,
        });
      }


      if (Object.keys(filters).length > 0) {
        setAppliedFilters(filters);
        setIsFiltersApplied(true);
        const hasActualFilters =
          filters.location ||
          filters.status ||
          filters.jobType ||
          filters.experienceLevel ||
          (filters.sortBy && filters.sortBy !== "recent");
        setShouldShowFilterBar(hasActualFilters);
      }
      saveCurrentTabState(active);
    }
  }, [search, active]); 


  useEffect(() => {
    if (!currentTabs) {
      setShowFilter(false);
      setShowSort(false);
      setAppliedFilters({});
      setIsFiltersApplied(false);
      setShouldShowFilterBar(false);
    }
  }, [currentTabs]);

  useEffect(() => {
    const urlParams = new URLSearchParams(search);
    if (!urlParams.has("tabs")) {
      urlParams.set("tabs", "0");
      navigate(`${pathname}?${urlParams.toString()}`, { replace: true });
    }
  }, []);

  useEffect(() => {
    const hasExperienceFilter = appliedFilters.experienceLevel && appliedFilters.experienceLevel.length > 0;

    if (hasExperienceFilter && maxExperience === 12) {
      async function fetchMaxExp() {
        try {
          const response = await getMaxExperience();
          const maxExp = Math.max(response.maxExperience || 12, 12);
          setMaxExperience(maxExp);
          setExperienceLevelSelected(([min, max]) => [
            min,
            Math.min(max, maxExp),
          ]);
        } catch (error) {
          console.error("Error fetching max experience:", error);
          setMaxExperience(12);
        }
      }
      fetchMaxExp();
    }
  }, [appliedFilters.experienceLevel, maxExperience]);


  function handleTabClick(tabIndex) {

    setIsManualTabSwitch(true);
    const currentTabState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      appliedFilters,
      isFiltersApplied,
    };

    const updatedTabStates = {
      ...tabStates,
      [active]: currentTabState,
    };

    setTabStates(updatedTabStates);

    const inheritedState = inheritCompatibleFiltersWithGlobalState(
      currentTabState,
      active,
      tabIndex,
      updatedTabStates
    );

    setTabStates((prev) => ({
      ...prev,
      [tabIndex]: inheritedState,
    }));

    const urlParams = new URLSearchParams(search);
    urlParams.set("tabs", tabIndex);
    const previousTabPrefix = `tab${active}_`;
    Array.from(urlParams.keys()).forEach((key) => {
      if (key.startsWith(previousTabPrefix)) {
        urlParams.delete(key);
      }
    });

    if (inheritedState && inheritedState.isFiltersApplied) {
      const tabPrefix = `tab${tabIndex}_`;

      if (inheritedState.locationSelected?.length > 0) {
        urlParams.set(
          `${tabPrefix}location`,
          inheritedState.locationSelected.join(",")
        );
      }
      if (inheritedState.statusSelected?.length > 0) {
        urlParams.set(
          `${tabPrefix}status`,
          inheritedState.statusSelected.join(",")
        );
      }
      if (inheritedState.jobTypeSelected?.length > 0) {
        urlParams.set(
          `${tabPrefix}jobType`,
          inheritedState.jobTypeSelected.join(",")
        );
      }
      if (inheritedState.experienceLevelSelected &&
          (inheritedState.experienceLevelSelected[0] !== 1 || inheritedState.experienceLevelSelected[1] !== 12)) {
        urlParams.set(`${tabPrefix}experienceLevel`, `${inheritedState.experienceLevelSelected[0]}-${inheritedState.experienceLevelSelected[1]}`);
      }
      if (inheritedState.appliedFilters?.searchTerm && inheritedState.appliedFilters?.searchField) {
        urlParams.set(`${tabPrefix}searchTerm`, inheritedState.appliedFilters.searchTerm);
        urlParams.set(`${tabPrefix}searchField`, inheritedState.appliedFilters.searchField);
      }
    }

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });

    setActive(tabIndex);
    setShowFilter(false);
    setShowSort(false);

    unstable_batchedUpdates(() => {
      setLocationSelected(inheritedState.locationSelected);
      setStatusSelected(inheritedState.statusSelected);
      setJobTypeSelected(inheritedState.jobTypeSelected);
      setExperienceLevelSelected(inheritedState.experienceLevelSelected);
      setAppliedFilters(inheritedState.appliedFilters);
      setIsFiltersApplied(inheritedState.isFiltersApplied);
      setShouldShowFilterBar(inheritedState.shouldShowFilterBar || false);
    });

    saveCurrentTabState(tabIndex);
  }
  async function getSubmissionDetail(
    submissionType = "activeSubmission",
    page,
    limit,
    filters = {}
  ) {

    const filtersForAPI = { ...filters };
    if (appliedSorting) {
      filtersForAPI.sortBy = appliedSorting === "recent" ? "newest" : "oldest";
    }
  
    const result = await getSubmission(submissionType, page, limit, filtersForAPI);
    return {
      resData: result.results,
      totalResults: result.total,
      totalPages: result.totalPages,
      page: result.page,
      limit: result.limit,
    };
  }

  function handleFilterClick(e) {
    e?.stopPropagation?.();
    setShowFilter((prev) => !prev);
  }
  function handleSortClick(e) {
    e?.stopPropagation?.();
    setShowSort((prev) => !prev);
  }


  function handleSortingChange(newSortValue) {
    setSortValue(newSortValue);
  }
  function handleClearFilter() {
    unstable_batchedUpdates(() => {
      setLocationSelected([]);
      setStatusSelected([]);
      setJobTypeSelected([]);
      setExperienceLevelSelected([1, maxExperience]);
      setSortValue("");

      setAppliedFilters({});
      setIsFiltersApplied(false);
      setAppliedSorting("");
      setShouldShowFilterBar(false);

      setGlobalSearchState({
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false,
      });
    });

    setGlobalInheritanceState({
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
      lastUpdatedTab: null,
    });

    const defaultState = {
      locationSelected: [],
      statusSelected: [],
      jobTypeSelected: [],
      experienceLevelSelected: [1, 12],
      appliedFilters: {},
      isFiltersApplied: false,
    };

    setTabStates({
      0: { ...defaultState },
      1: { ...defaultState },
      2: { ...defaultState },
      3: { ...defaultState },
    });

    const urlParams = new URLSearchParams();

    if (currentTabs !== null) {
      urlParams.set("tabs", currentTabs);
    }

    urlParams.delete('submissionDate');

    navigate(`${pathname}?${urlParams.toString()}`, { replace: true });

    unstable_batchedUpdates(() => {
      setShowFilter(false);
      setShowSort(false);
      setShowSearch(false); 
    });
  }

  function handleApplyFilter() {

  
    const filters = {};
    const urlParams = {};
    if (locationSelected && locationSelected.length > 0) {
      filters.location = locationSelected;
      urlParams.location = locationSelected;
    }
    if (statusSelected && statusSelected.length > 0 && active !== 1) {
      filters.status = statusSelected;
      urlParams.status = statusSelected;
    }
    if (jobTypeSelected && jobTypeSelected.length > 0) {
      filters.jobType = jobTypeSelected;
      urlParams.jobType = jobTypeSelected;
    }
    if (experienceLevelSelected && experienceLevelSelected.length === 2) {
      const [min, max] = experienceLevelSelected;
      if (min !== 1 || max !== 12) {
        filters.experienceLevel = [`${min}-${max}`];
        urlParams.experienceLevel = `${min}-${max}`;
      }
    }

    const currentSorting = sortValue && sortValue !== "" ? sortValue : "";

    if (currentSorting) {
      urlParams.submissionDate = currentSorting;
    }

    if (appliedFilters.searchTerm && appliedFilters.searchField) {
      filters.searchTerm = appliedFilters.searchTerm;
      filters.searchField = appliedFilters.searchField;
      urlParams.searchTerm = appliedFilters.searchTerm;
      urlParams.searchField = appliedFilters.searchField;
    }

    setAppliedFilters(filters);
    setIsFiltersApplied(true);
    setShouldShowFilterBar(true);
    setAppliedSorting(currentSorting);
    updateURL(urlParams, active);

    const currentState = {
      locationSelected,
      statusSelected,
      jobTypeSelected,
      experienceLevelSelected,
      appliedFilters: filters, 
      isFiltersApplied: true,
    };

    setTabStates((prev) => ({
      ...prev,
      [active]: currentState,
    }));

    updateGlobalInheritanceState(currentState, active);

    setShowFilter(false);
    setShowSort(false);
  }

  function handleSearchClick() {
    setShowSearch((prev) => !prev);
  }

  function handleSearch(searchData) {
    const hasSearchTerm = searchData.searchTerm.trim() !== "";
    unstable_batchedUpdates(() => {
      setGlobalSearchState({
        searchTerm: hasSearchTerm ? searchData.searchTerm : "",
        searchField: searchData.searchField,
        isSearchApplied: hasSearchTerm,
      });
    });


    const currentUrlParams = new URLSearchParams(search);

    const urlParams = {};
    if (appliedFilters.location) urlParams.location = appliedFilters.location;
    if (appliedFilters.status) urlParams.status = appliedFilters.status;
    if (appliedFilters.jobType) urlParams.jobType = appliedFilters.jobType;
    if (appliedFilters.experienceLevel) {
      urlParams.experienceLevel = appliedFilters.experienceLevel[0];
    }
    if (appliedFilters.sortBy) urlParams.sortBy = appliedFilters.sortBy;

    if (hasSearchTerm && searchData.searchField) {
      urlParams.searchTerm = searchData.searchTerm;
      urlParams.searchField = searchData.searchField;
    } else {
      currentUrlParams.delete("searchTerm");
      currentUrlParams.delete("searchField");
      currentUrlParams.delete(`tab${active}_searchTerm`);
      currentUrlParams.delete(`tab${active}_searchField`);
    }
    if (!hasSearchTerm) {
      Object.keys(urlParams).forEach((key) => {
        currentUrlParams.set(key, urlParams[key]);
      });

      navigate(`${pathname}?${currentUrlParams.toString()}`, { replace: true });
    } else {
      updateURL(urlParams, active);
    }
  }

  return (
    <>
      <TabNav
        nav={tabs}
        active={active}
        setActive={handleTabClick}
        rightSidebar={
          <div className="flex gap-2 items-center relative">
            <div className="relative">
              <img
                src="/assets/icons/search.svg"
                alt="Search"
                className="w-8 h-8 cursor-pointer"
                onClick={handleSearchClick}
                data-search-icon="true"
              />
              <UniversalSearchDropdown
                isOpen={showSearch}
                onClose={() => setShowSearch(false)}
                onSearch={handleSearch}
                section="candidates"
                activeTab={active}
              />
            </div>
            <EnhancedThreeDot
              dropdownSize="w-56"
              iconSrc="/assets/icons/threedothorizontal.svg"
              buttonDropDown={(onClose) => (
                <div className="min-w-[200px]">
                  <div className="flex items-center justify-between px-4 pt-3 pb-2">
                    <span className="font-medium text-gray-900 text-base">
                      View Option
                    </span>
                    <button
                      className="text-gray-400 hover:text-gray-700 text-lg font-bold focus:outline-none"
                      onClick={(e) => {
                        e.stopPropagation();
                        onClose();
                      }}
                      aria-label="Close"
                      type="button"
                    >
                      ×
                    </button>
                  </div>
                  <hr className="my-1 border-gray-200" />
                  <ul className="py-0" role="none">
                    {active !== 2 && (
                      <li>
                        <div
                          className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                          role="menuitem"
                          onClick={handleFilterClick}
                        >
                          <img src="/assets/icons/filter.svg" alt="Filter" className="w-4 h-4" />
                          Filter
                        </div>
                      </li>
                    )}
                    <li>
                      <div
                        className="flex cursor-pointer items-center justify-start gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 "
                        role="menuitem"
                        onClick={handleSortClick}
                      >
                        <img src="/assets/icons/sorting.svg" alt="Sorting" className="w-4 h-4" />
                        Sorting
                      </div>
                    </li>
                  </ul>
                </div>
              )}
            />

            {active === 3 && (
              <AppButton
                label="+ Add Candidate"
                onClick={() => navigate("/candidate/addcandidate")}
                variant="primary"
              />
            )}
          </div>
        }
      />
      <div className="overflow-x-auto rounded-lg mt-2 border border-gray-200 shadow-sm">
        {(shouldShowFilters || showFilter) && (
          <FilterBar
            filters={filterOptions[active] || []}
            onClear={() => {}} 
            onApply={() => {}} 
            rightAlignActions={false} 
          />
        )}

        {(shouldShowSorting || showSort) && (
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <SubmissionDateSortPill
              selected={sortValue}
              setSelected={handleSortingChange}
              onClose={() => setShowSort(false)}
            />
          </div>
        )}

        {((shouldShowFilters || showFilter) || (shouldShowSorting || showSort)) && (
          <div className="px-4 py-2 bg-white border-b border-gray-200 flex justify-end">
            <div className="flex items-center gap-2 apply-clear-buttons">
              <span
                className="text-blue-500 cursor-pointer text-xs"
                onClick={handleClearFilter}
              >
                Clear all
              </span>
              <button
                className="px-4 py-1 rounded bg-green-500 text-white text-sm font-medium hover:bg-green-600"
                onClick={handleApplyFilter}
              >
                Apply
              </button>
            </div>
          </div>
        )}

        {active === 0 && (
          <CandidateTable
            key={`active-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Location",
              "Submission Date",
              "Email",
              "Status",
              "",
            ]}
            getData={async (page, limit) => {
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {}),
              };
              if (
                globalSearchState.isSearchApplied &&
                globalSearchState.searchTerm.trim()
              ) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "activeSubmission",
                page,
                limit,
                combinedParams
              );

              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  location:
                    item?.submission?.job?.location?.country?.split("-")[0],
                  submissionDate: new Date(
                    item?.submission?.submittedAt
                  )?.toLocaleString(),
                  email: item?.personalDetails?.emailAddress,
                  status: item?.submission?.status,
                  submissionId: item?.submission?.submissionId,
                };
              });
              const result = {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };

              return result;
            }}
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              ...JobStatusOptions,
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
            ]}
          />
        )}
        {active === 1 && (
          <CandidateTable
            key={`hired-candidates-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Title",
              "Job Id",
              "Email",
              "Location",
              "Job Type",
              "Experience",
              "",
            ]}
            getData={async (page, limit) => {
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {}),
              };
              if (
                globalSearchState.isSearchApplied &&
                globalSearchState.searchTerm.trim()
              ) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "hired",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",
                  jobType: item?.submission?.job?.jobType,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 2 && (
          <CandidateTable
            key={`rejected-candidates-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={["Name", "Job Title", "Job Id", "Email", "Reason", ""]}
            getData={async (page, limit) => {
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {}),
              };
              if (
                globalSearchState.isSearchApplied &&
                globalSearchState.searchTerm.trim()
              ) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "rejected",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  email: item?.personalDetails?.emailAddress,
                  reason: item?.submission?.job?.notes
                    ? item?.submission?.job?.notes
                    : "-",
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
          />
        )}
        {active === 3 && (
          <CandidateTable
            key={`all-submissions-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            columns={[
              "Name",
              "Job Id",
              "Job Title",
              "Location",
              "Job Type",
              "Mobile Number",
              "Experience",
              "Email",
              "Status",
              " ",
            ]}
            getData={async (page, limit) => {
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {}),
              };
              if (
                globalSearchState.isSearchApplied &&
                globalSearchState.searchTerm.trim()
              ) {
                combinedParams.searchTerm = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getSubmissionDetail(
                "any",
                page,
                limit,
                combinedParams
              );
              const resData = data?.resData?.map((item) => {
                return {
                  id: item?.candidateID,
                  name:
                    item?.personalDetails?.firstName +
                    " " +
                    item?.personalDetails?.lastName,

                  jobId: item?.submission?.job?.jobId
                    ? item?.submission?.job?.jobId
                    : "-",
                  jobTitle: item?.submission?.job?.jobTitle
                    ? item?.submission?.job?.jobTitle
                    : "-",
                  location: item?.submission?.job?.location?.country
                    ? item?.submission?.job?.location?.country?.split("-")[0]
                    : "-",

                  jobType: item?.submission?.job?.jobType
                    ? item?.submission?.job?.jobType
                    : "-",

                  "Mobile Number": `${
                    item?.personalDetails?.phoneCountryCode
                      ? "+" + item?.personalDetails?.phoneCountryCode
                      : ""
                  } ${
                    item?.personalDetails?.phoneNumber
                      ? item?.personalDetails?.phoneNumber
                      : ""
                  }`,

                  experience: item?.submission?.job?.experience?.min
                    ? item?.submission?.job?.experience?.min +
                      " - " +
                      item?.submission?.job?.experience?.max +
                      " " +
                      item?.submission?.job?.experience?.unit
                    : "-",

                  email: item?.personalDetails?.emailAddress,
                  status: item?.submission?.status || "Talent Pool", 
                  submissionId: item?.submission?.submissionId,
                };
              });
              return {
                resData,
                totalResults: data?.totalResults,
                totalPages: data?.totalPages,
                page: data?.page,
                limit: data?.limit,
              };
            }}
            dropdownkey="status"
            isDropDownDisable={true}
            dropdownItems={[
              {
                value: "Talent Pool",
                label: "Talent Pool",
                color: "bg-[#CEFAFE] text-[#00B8DB]",
              },
              ...JobStatusOptions,
            ]}
          />
        )}
      </div>
    </>
  );
};

export default Candidates;
