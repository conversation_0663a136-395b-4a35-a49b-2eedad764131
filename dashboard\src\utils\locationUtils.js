// Utility to get all country names from countries.json
// and filter by search string (case-insensitive, substring match)
import countries from '../data/countries.json';

/**
 * Returns a flat array of all country names.
 */
export function getAllCountryNames() {
  return countries.map(c => c.name);
}

/**
 * Returns a filtered array of country names matching the search string.
 * @param {string} search
 */
export function filterCountryNames(search) {
  if (!search) return getAllCountryNames();
  return countries
    .map(c => c.name)
    .filter(name => name.toLowerCase().includes(search.toLowerCase()));
}
