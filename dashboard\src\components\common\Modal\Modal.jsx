import React from "react";

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  css = "relative p-4 w-xl max-w-xl max-h-full ",
}) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex justify-center items-center w-full h-[calc(100%)] bg-[rgba(0,0,0,0.2)]"
      aria-hidden="true"
    >
      <div className={css}>
        <div className="relative bg-white rounded-lg shadow-sm  ">
          {/* Header */}
          <div className="flex items-center justify-between p-4 md:p-5  border-gray-200 rounded-t ">
            <h3 className="text-lg font-semibold text-gray-900 ">{title}</h3>
            <button
              onClick={onClose}
              type="button"
              className="text-gray-400 cursor-pointer bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center "
            >
              <svg
                className="w-3 h-3"
                fill="none"
                viewBox="0 0 14 14"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 1l6 6m0 0l6 6M7 7l6-6M7 7L1 13"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>

          {/* Body */}
          <div className="space-y-4 text-gray-700  max-h-[75vh] overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
