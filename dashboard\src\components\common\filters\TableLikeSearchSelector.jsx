import React, { useState } from 'react';
 
const TableLikeSearchSelector = ({
  searchInputs,
  handleInputChange,
  currentSearchFields
}) => {
  const [isOpen, setIsOpen] = useState(false);
 
  const selectedField = currentSearchFields.find(
    field => field.value === searchInputs.searchField
  );
 
  return (
    <div className="mb-3">
   
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-28 px-3 py-2 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 hover:bg-gray-50 flex items-center justify-between cursor-pointer"
        >
          <span>{selectedField?.label || 'All Fields'}</span>
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
 
        {isOpen && (
          <div className="absolute w-80 top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50">
            {/* Table-like List */}
             <div className="grid grid-cols-2 gap-y-2 p-4 text-sm text-gray-700">
                {currentSearchFields.map((field) => (
                  <div
                    key={field.value}
                    onClick={() => {
                      handleInputChange('searchField', field.value);
                      setIsOpen(false);
                    }}
                    className={`cursor-pointer hover:text-blue-600 ${
                      searchInputs.searchField === field.value
                        ? 'text-blue-600 font-medium'
                        : ''
                    }`}
                  >
                    {field.label}
                  </div>
                ))}
              </div>
          </div>
        )}
 
        {isOpen && (
          <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
        )}
      </div>
    </div>
  );
};
 
export default TableLikeSearchSelector;