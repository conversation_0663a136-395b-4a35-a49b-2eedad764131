import React, { Suspense } from "react";

const LazyApexChart = React.lazy(() => import("./LazyApexChart"));

const PolarAreaChart = ({
  series = [],
  labelNames = [],
  colors = ["#4C78FF", "#FF82AC", "#16DBCC", "#FFBB38"],
}) => {
  const options = {
    chart: {
      width: 220,
      type: "polarArea",
    },
    colors: colors,
    labels: labelNames,
    fill: {
      opacity: 1,
    },
    stroke: {
      width: 1,
      colors: colors,
    },
    yaxis: {
      show: false,
    },
    legend: {
      show: false,
    },
    plotOptions: {
      polarArea: {
        rings: {
          strokeWidth: 0,
        },
        spokes: {
          strokeWidth: 0,
        },
      },
    },
    theme: {
      monochrome: {
        enabled: false,
        shadeTo: "light",
        shadeIntensity: 0.6,
      },
    },
  };

  return (
    <div className="flex items-center">
      <Suspense fallback={<div>Loading chart...</div>}>
        <LazyApexChart
          options={options}
          series={series}
          type="polarArea"
          height={180}
          width={180}
        />
      </Suspense>

      <div className="grid grid-cols-2 gap-y-6 gap-x-2">
        {labelNames.map((label, i) => (
          <div key={i} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: colors[i] }}
            ></div>
            <div>
              <p className="text-[20px] font-bold leading-none">{series[i]}</p>
              <p className="text-sm text-gray-500 leading-none">{label}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PolarAreaChart;
