import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  createBookmark,
  getRecruiterJobs,
  removeBookmark,
  selectJobToWorkOn,
  unmapJobs,
  workOnRequestStatusUpdate,
} from "../../services/operations/jobAPI";
import TabNav from "../../components/common/TabNav/TabNav";
import JobsList from "../../components/core/recruiter/Job/JobsList";
import FiltersPanel from "../../components/common/filter/FiltersPanel";
import SortingPanel from "../../components/common/filter/SortingPanel";
import FilterSortBar from "../../components/common/filter/FilterSortBar";

import { useURLState } from "../../components/common/filter/useURLState";
import UniversalSearchDropdown from "../../components/core/recruiter/shared/UniversalSearchDropdown";

const SORT_OPTIONS = {
  RECENT: "Recent",
  OLDEST: "Oldest"
};

const Jobs = () => {
  const { search, pathname } = useLocation();
  const queryParams = new URLSearchParams(search);
  const currentTabs = queryParams.get("tabs");
  const navigate = useNavigate();
  const { parseURLParams, updateURLParams } = useURLState();
  const [isInitialized, setIsInitialized] = useState(false);

  // Helper function to parse URL parameters immediately for initial state
  const parseInitialURLState = () => {
    try {
      const urlState = parseURLParams();

      const parsedState = {
        appliedFilters: {},
        isFiltersApplied: false,
        appliedSorting: "",
        globalSearchState: {
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false,
        }
      };

      if (urlState.filters && Object.keys(urlState.filters).length > 0) {
        parsedState.appliedFilters = urlState.filters;
        parsedState.isFiltersApplied = true;
      }

      if (urlState.sorting && urlState.sorting.postedDate) {
        parsedState.appliedSorting = urlState.sorting.postedDate === "recent" ? "Recent" :
                                    urlState.sorting.postedDate === "oldest" ? "Oldest" : "";
      }

      if (urlState.search && urlState.search.searchTerm) {
        parsedState.globalSearchState = {
          searchTerm: urlState.search.searchTerm,
          searchField: urlState.search.searchField || "all",
          isSearchApplied: true,
        };
      }

      return parsedState;
    } catch (error) {
      console.error("Error parsing initial URL state:", error);
      return {
        appliedFilters: {},
        isFiltersApplied: false,
        appliedSorting: "",
        globalSearchState: {
          searchTerm: "",
          searchField: "all",
          isSearchApplied: false,
        }
      };
    }
  };

  const [active, setActive] = useState(() => {
    const tabFromURL = queryParams.get("tabs");
    return tabFromURL ? Number(tabFromURL) : 0;
  });

  const tabs = [
    { name: <span>Working On</span>, css: "" },
    { name: <span>Mapped</span>, css: "" },
    { name: <span>Work on request</span>, css: "" },
    { name: <span>All Jobs</span>, css: "" },
    { name: <span>Saved</span>, css: "" },
  ];


  const [showSearchDropdown, setShowSearchDropdown] = useState(false);

  const initialURLState = parseInitialURLState();
  const [showFilters, setShowFilters] = useState(false);
  const [showSorting, setShowSorting] = useState(false);

  const [appliedFilters, setAppliedFilters] = useState(() => {
    try {
      return initialURLState?.appliedFilters || {};
    } catch (error) {
      console.error("Error initializing appliedFilters:", error);
      return {};
    }
  });

  const [isFiltersApplied, setIsFiltersApplied] = useState(() => {
    try {
      return initialURLState?.isFiltersApplied || false;
    } catch (error) {
      console.error("Error initializing isFiltersApplied:", error);
      return false;
    }
  });

  const [appliedSorting, setAppliedSorting] = useState(() => {
    try {
      return initialURLState?.appliedSorting || "";
    } catch (error) {
      console.error("Error initializing appliedSorting:", error);
      return "";
    }
  });

  // New state for selected filters (which pills are visible)
  const [selectedFilters, setSelectedFilters] = useState([]);

  // Handler for when user selects a filter from dropdown
  const handleFilterSelect = (filterKey) => {
    setSelectedFilters(prev => [...prev, filterKey]);
  };

  // Handler for when user removes a filter pill
  const handleFilterRemove = (filterKey) => {
    setSelectedFilters(prev => prev.filter(key => key !== filterKey));
    // Also clear the filter values
    handleFilterChange(filterKey, []);
  };

  const [globalSearchState, setGlobalSearchState] = useState(() => {
    try {
      return initialURLState?.globalSearchState || {
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false
      };
    } catch (error) {
      console.error("Error initializing globalSearchState:", error);
      return {
        searchTerm: "",
        searchField: "all",
        isSearchApplied: false
      };
    }
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [currentLimit, setCurrentLimit] = useState(10);

  const tabFilterConfig = {
    0: {
      // Working On
      filters: [
        "specialization",
        "jobType",
        "jobPosted",
        "submissionDate",
        "submissionRange",
        "salaryRange",
        "commission",
        "numberOfPosition",
        "numberOfRecruiter",
        "jobTitle",
        "country",
        "state"
      ],
      sortOptions: ["postedDate"],
    },
    1: {
      // Mapped
      filters: [
        "specialization",
        "jobType",
        "jobPosted",
        "submissionDate",
        "submissionRange",
        "salaryRange",
        "commission",
        "numberOfPosition",
        "numberOfRecruiter",
        "jobTitle",
        "country",
        "state"
      ],
      sortOptions: ["postedDate"],
    },
    2: {
      // Work on request
      filters: [
        "specialization",
        "jobType",
        "jobPosted",
        "submissionDate",
        "submissionRange",
        "salaryRange",
        "commission",
        "numberOfPosition",
        "numberOfRecruiter",
        "jobTitle",
        "country",
        "state"
      ],
      sortOptions: ["postedDate"],
    },
    3: {
      // All Jobs
      filters: [
        "specialization",
        "jobType",
        "jobPosted",
        "submissionDate",
        "submissionRange",
        "salaryRange",
        "commission",
        "numberOfPosition",
        "numberOfRecruiter",
        "jobTitle",
        "country",
        "state"
      ],
      sortOptions: ["postedDate"],
    },
    4: {
      // Saved
      filters: [
        "specialization",
        "jobType",
        "jobPosted",
        "submissionDate",
        "submissionRange",
        "salaryRange",
        "commission",
        "numberOfPosition",
        "numberOfRecruiter",
        "jobTitle",
        "country",
        "state"
      ],
      sortOptions: ["postedDate"],
    },
  };


  const currentTabConfig = tabFilterConfig[active] || tabFilterConfig[0];
  const availableFilters = currentTabConfig.filters;
  const availableSortOptions = currentTabConfig.sortOptions;


  const initializeTabState = () => {
    const filtersState = {};
    const sortingState = {};
    const urlState = parseURLParams();

    Object.keys(tabFilterConfig).forEach((tabIndex) => {
      filtersState[tabIndex] = {};
      sortingState[tabIndex] = "";
    });

    if (
      urlState.filters &&
      Object.keys(urlState.filters).some((key) =>
        Array.isArray(urlState.filters[key])
          ? urlState.filters[key].length > 0
          : urlState.filters[key]
      )
    ) {
      filtersState[urlState.activeTab] = {
        ...filtersState[urlState.activeTab],
        ...urlState.filters,
      };
    }

    if (urlState.sorting.postedDate) {
      sortingState[urlState.activeTab] =
        urlState.sorting.postedDate === "recent"
          ? "Recent"
          : urlState.sorting.postedDate === "oldest"
          ? "Oldest"
          : "";
    }

    return { filtersState, sortingState };
  };

  const { filtersState, sortingState } = initializeTabState();
  const [filters, setFilters] = useState(filtersState);
  const [sorting, setSorting] = useState(sortingState);


  useEffect(() => {
    try {
      const urlState = parseURLParams();

      setActive(urlState.activeTab || 0);

      if (urlState.filters && Object.keys(urlState.filters).length > 0) {
        setFilters((prev) => ({
          ...prev,
          [urlState.activeTab || 0]: urlState.filters,
        }));
      }

      if (urlState.sorting && Object.keys(urlState.sorting).length > 0) {
        setSorting((prev) => ({
          ...prev,
          [urlState.activeTab || 0]: urlState.sorting.postedDate === "recent" ? "Recent" :
                                     urlState.sorting.postedDate === "oldest" ? "Oldest" : "",
        }));
      }

      setShowFilters(urlState.showFilters || false);
      setShowSorting(urlState.showSorting || false);

      setIsInitialized(true);
    } catch (error) {
      console.error('Error parsing URL parameters:', error);
      setIsInitialized(true);
    }
  }, [parseURLParams]);


  useEffect(() => {
    if (!isInitialized) {
      return;
    }

    try {
      const hasActualFilters = isFiltersApplied &&
        Object.keys(appliedFilters).length > 0 &&
        Object.values(appliedFilters).some(filterArray =>
          Array.isArray(filterArray) ? filterArray.length > 0 : filterArray
        );

      const hasActualSorting = appliedSorting &&
        appliedSorting.trim() !== "" &&
        (appliedSorting === "Recent" || appliedSorting === "Oldest");

      const shouldShowFilters = hasActualFilters;
      const shouldShowSorting = hasActualSorting;

      let sortingForURL = {};
      if (appliedSorting) {
        if (appliedSorting === "Recent") {
          sortingForURL.postedDate = "recent";
        } else if (appliedSorting === "Oldest") {
          sortingForURL.postedDate = "oldest";
        }
      }

     
      updateURLParams({
        activeTab: active,
        filters: isFiltersApplied ? appliedFilters : {},
        sorting: sortingForURL,
        search: globalSearchState.isSearchApplied && globalSearchState.searchTerm ? globalSearchState : {},
        showFilters: shouldShowFilters,
        showSorting: shouldShowSorting,
      });

  
      setShowFilters(shouldShowFilters);
      setShowSorting(shouldShowSorting);
    } catch (error) {
      console.error("Error updating URL params:", error);
    }
  }, [active, isInitialized, updateURLParams, isFiltersApplied, appliedFilters, appliedSorting, globalSearchState]);



  useEffect(() => {
    navigate(`${pathname}?${currentTabs ? `tabs=${currentTabs}` : "tabs=0"}`, {
      replace: true,
    });
   
  }, []);

  function updateTabs(tabIndex) {
   
    setActive(tabIndex);
    navigate(`${pathname}?tabs=${tabIndex}`, { replace: true });
  }

  async function getJobsDetail(jobType, page, limit, combinedParams = {}) {

    const filters = {};
    if (combinedParams.specialization) filters.specialization = combinedParams.specialization;
    if (combinedParams.location) filters.location = combinedParams.location;
    if (combinedParams.jobType) filters.jobType = combinedParams.jobType;
    if (combinedParams.experienceLevel) filters.experienceLevel = combinedParams.experienceLevel;


    const sortingParams = {};
    if (combinedParams.postedDate) {
      sortingParams.postedDate = combinedParams.postedDate; 
    }

    const searchObj = {};
    if (combinedParams.search) {
      searchObj.searchTerm = combinedParams.search;
      searchObj.searchField = combinedParams.searchField || 'all';
    }

    const res = await getRecruiterJobs(jobType, page, limit, filters, sortingParams, searchObj);
    return res;
  }

  
  async function getJobs(jobType = "workingon", page = 1, limit = 10, appliedFilters = {}, appliedSorting = "", appliedSearch = {}) {
   
    let sortingParams = {};

    if (appliedSorting) {
      switch (appliedSorting) {
        case "Recent":
          sortingParams = { postedDate: "recent" };
          break;
        case "Oldest":
          sortingParams = { postedDate: "oldest" };
          break;
        default:
          sortingParams = {};
      }
    }

    const res = await getRecruiterJobs(jobType, page, limit, appliedFilters, sortingParams, appliedSearch);
    return res;
  }

  // add to bookmark
  async function setToBookMark(jobId, jobType = "workingon") {
    await createBookmark(jobId, jobType);
  }

  // remove from bookmark
  async function removeFromBookMark(jobId, jobType = "workingon") {
    await removeBookmark(jobId, jobType);
  }

  // un-mapped jobs
  async function unMappedJobs(jobId, jobType) {
    await unmapJobs(jobId);
    return await getJobs(jobType, 1, 10);
  }

  // job to workon
  async function selectJobToWork(jobId, workOnType, jobType) {
    await selectJobToWorkOn(jobId, workOnType);
    return await getJobs(jobType, 1, 10);
  }

  // work on request status update
  async function workOnRequestUpdate(jobId, status, jobType) {
    await workOnRequestStatusUpdate(jobId, status);
    return await getJobs(jobType, 1, 10);
  }



  const handleSearchClick = () => {
    setShowSearchDropdown(!showSearchDropdown);
  };

  const handleSearchClose = () => {
    setShowSearchDropdown(false);
  };

  const handleSearch = (searchInputs) => {
    const hasSearchTerm = searchInputs.searchTerm.trim() !== "";

 
    setGlobalSearchState({
      searchTerm: hasSearchTerm ? searchInputs.searchTerm : "",
      searchField: searchInputs.searchField,
      isSearchApplied: hasSearchTerm
    });
  };


  const handleFilterChange = (filterKey, selectedValues) => {
    setFilters((prev) => ({
      ...prev,
      [active]: {
        ...prev[active],
        [filterKey]: selectedValues,
      },
    }));
  };

  const processFilters = (filters) => {
    const processed = {};
    const standardFilterKeys = ['specialization', 'location', 'jobType'];

    standardFilterKeys.forEach(key => {
      if (filters[key] && filters[key].length > 0) {
        processed[key] = filters[key];
      }
    });

    if (filters.experienceLevel && filters.experienceLevel.length > 0) {
      processed.experienceLevel = filters.experienceLevel.map(range => {
        if (Array.isArray(range)) {
          return `${range[0]}-${range[1]}`;
        }
        return range;
      });
    }

    return processed;
  };

  const handleApplyFilters = (overrideSorting = null, overrideFilters = null) => {
    const currentFilters = overrideFilters !== null ? overrideFilters : (filters[active] || {});
    const currentSorting = overrideSorting !== null ? overrideSorting : (sorting[active] || "");

    const processedFilters = processFilters(currentFilters);

    setAppliedFilters(processedFilters);
    setIsFiltersApplied(Object.keys(processedFilters).length > 0);
    setAppliedSorting(currentSorting);


    setShowSearchDropdown(false);
    setShowFilters(false);
    setShowSorting(false);
  };



  const handleSortingChange = (sortValue) => {
    const validSortValue = Object.values(SORT_OPTIONS).includes(sortValue)
      ? sortValue
      : SORT_OPTIONS.RECENT;
    setSorting((prev) => ({
      ...prev,
      [active]: validSortValue,
    }));
    setCurrentPage(1);
  };

  // Handle Clear All - clear both filters and sorting from data
  const handleClearAll = () => {
    // Clear UI state
    setSelectedFilters([]);

    // Clear filter values
    const clearedFilters = {};
    availableFilters.forEach(filterKey => {
      clearedFilters[filterKey] = [];
    });

    // Update filters state
    setFilters((prev) => ({
      ...prev,
      [active]: clearedFilters,
    }));

    // Clear sorting
    setSorting((prev) => ({
      ...prev,
      [active]: "",
    }));

    // Clear applied state
    setAppliedFilters({});
    setIsFiltersApplied(false);
    setAppliedSorting("");

    // Clear search state
    setGlobalSearchState({
      searchTerm: "",
      searchField: "all",
      isSearchApplied: false
    });

    // Hide panels
    setShowFilters(false);
    setShowSorting(false);
  };



  return (
    <>
      <TabNav
        nav={tabs}
        setActive={updateTabs}
        active={active}
        rightSidebar={
          <div className="flex gap-2 items-center">
            <div className="relative">
              <img
                src="/assets/icons/search.svg"
                alt="Search"
                className="w-6 h-8 cursor-pointer"
                onClick={handleSearchClick}
              />

              <UniversalSearchDropdown
                isOpen={showSearchDropdown}
                onClose={handleSearchClose}
                onSearch={handleSearch}
                section="jobs"
                activeTab={active}
              />
            </div>

          </div>
        }
      />

      {/* Filter and Sort Bar */}
      <FilterSortBar
        showSorting={showSorting}
        setShowSorting={setShowSorting}
        appliedSorting={appliedSorting}
        availableFilters={availableFilters}
        selectedFilters={selectedFilters}
        onFilterSelect={handleFilterSelect}
        onFilterRemove={handleFilterRemove}
        appliedFilters={appliedFilters}
        currentFilters={filters[active] || {}}
        onFilterChange={handleFilterChange}
        onApplyFilters={handleApplyFilters}
        onClearAll={handleClearAll}
        setShowFilters={setShowFilters}
        pageType="jobs"
      />

      {/* Filter and Sorting Panels - Hidden since we're using FilterSortBar */}
      {/* <FiltersPanel
        showFilters={showFilters}
        setShowFilters={setShowFilters}
        filters={isFiltersApplied ? appliedFilters : filters[active]}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        availableFilters={availableFilters}
        pageType="jobs"
      /> */}

      <SortingPanel
        showSorting={showSorting}
        setShowSorting={setShowSorting}
        sorting={appliedSorting || sorting[active]}
        setSorting={handleSortingChange}
        handleFilterChange={handleFilterChange}
        handleApplyFilters={handleApplyFilters}
        activeTab={active}
        availableSortOptions={availableSortOptions}
      />

      <div className="pt-3">
        {/* <JobCard /> */}
        {active == 0 && (
          <JobsList
            key={`working-on-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            getJobs={async (page, limit) => {
             
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

           
              if (appliedSorting) {
                combinedParams.postedDate = appliedSorting === "Recent" ? "recent" : "oldest";
              }

              
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.search = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }



              const data = await getJobsDetail("workingon", page, limit, combinedParams);
              return data;
            }}
            deleteBookMark={removeFromBookMark}
            unMappedJobs={async (jobID) => {
              return await unMappedJobs(jobID, "workingon");
            }}
            jobType="workingon"
            setBookMark={setToBookMark}
          />
        )}
        {active == 1 && (
          <JobsList
            key={`mapped-jobs-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            getJobs={async (page, limit) => {
             
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

           
              if (appliedSorting) {
                combinedParams.postedDate = appliedSorting === "Recent" ? "recent" : "oldest";
              }

            
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.search = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getJobsDetail("mappedjob", page, limit, combinedParams);
              return data;
            }}
            selectJobToWork={async (jobId, workOnType) => {
              return await selectJobToWork(jobId, workOnType, "mappedjob");
            }}
            deleteBookMark={removeFromBookMark}
            jobType="mappedjob"
            setBookMark={setToBookMark}
          />
        )}
        {active == 2 && (
          <JobsList
            key={`work-on-request-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            getJobs={async (page, limit) => {
             
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

            
              if (appliedSorting) {
                combinedParams.postedDate = appliedSorting === "Recent" ? "recent" : "oldest";
              }

            
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.search = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getJobsDetail("workonrequest", page, limit, combinedParams);
              return data;
            }}
            workOnRequestUpdate={async (jobId, status) => {
              return await workOnRequestUpdate(jobId, status, "workonrequest");
            }}
            deleteBookMark={removeFromBookMark}
            setBookMark={setToBookMark}
            jobType="workonrequest"
          />
        )}
        {active == 3 && (
          <JobsList
            key={`all-jobs-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            getJobs={async (page, limit) => {
          
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

             
              if (appliedSorting) {
                combinedParams.postedDate = appliedSorting === "Recent" ? "recent" : "oldest";
              }

            
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.search = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getJobsDetail("alljobs", page, limit, combinedParams);
              return data;
            }}
            selectJobToWork={async (jobId, workOnType) => {
              return await selectJobToWork(jobId, workOnType, "alljobs");
            }}
            deleteBookMark={removeFromBookMark}
            jobType="alljobs"
            setBookMark={setToBookMark}
          />
        )}
        {active == 4 && (
          <JobsList
            key={`saved-jobs-${JSON.stringify(appliedFilters)}-${isFiltersApplied}-${JSON.stringify(globalSearchState)}-${appliedSorting}`}
            getJobs={async (page, limit) => {
             
              const combinedParams = {
                ...(isFiltersApplied ? appliedFilters : {})
              };

            
              if (appliedSorting) {
                combinedParams.postedDate = appliedSorting === "Recent" ? "recent" : "oldest";
              }

             
              if (globalSearchState.isSearchApplied && globalSearchState.searchTerm.trim()) {
                combinedParams.search = globalSearchState.searchTerm;
                combinedParams.searchField = globalSearchState.searchField;
              }

              const data = await getJobsDetail("savejobs", page, limit, combinedParams);
              return data;
            }}
            selectJobToWork={async (jobId, workOnType) => {
              return await selectJobToWork(jobId, workOnType, "savejobs");
            }}
            workOnRequestUpdate={async (jobId, status) => {
              return await workOnRequestUpdate(jobId, status, "savejobs");
            }}
            unMappedJobs={async (jobID) => {
              return await unMappedJobs(jobID, "savejobs");
            }}
            deleteBookMark={removeFromBookMark}
            setBookMark={setToBookMark}
          />
        )}
      </div>
    </>
  );
};

export default Jobs;
