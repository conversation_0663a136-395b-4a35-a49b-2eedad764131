import React, { useEffect, useRef } from "react";

const ViewOptionsDropdown = ({
  showDropdown,
  setShowDropdown,
  setShowFilters,
  setShowSorting,
  showFilters,
  showSorting,
}) => {
  const dropdownRef = useRef(null);

  const toggleFilters = () => {
    setShowFilters(!showFilters);
    setShowSorting(false);
    setShowDropdown(false); // Close dropdown when filter is clicked
  };

  const toggleSorting = () => {
    setShowSorting(!showSorting);
    setShowFilters(false);
    setShowDropdown(false); // Close dropdown when sorting is clicked
  };

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showDropdown, setShowDropdown]);

  return (
    <div className="relative" ref={dropdownRef}>
      <img
        src={"/assets/icons/threedothorizontal.svg"}
        alt="Options"
        onClick={() => setShowDropdown(!showDropdown)}
        className="w-6 h-8 cursor-pointer"
      />

      {/* Dropdown Box */}
      {showDropdown && (
        <div className="absolute right-0 mt-2 w-52 bg-white shadow-lg rounded-lg z-50 border">
          <div className="flex justify-between items-center border-b px-4 py-3">
            <span className="font-medium text-[#252B37] text-[15px]">
              View Option
            </span>
            <span
              className="cursor-pointer text-gray-400 hover:text-gray-600"
              onClick={() => setShowDropdown(false)}
            >
              ✕
            </span>
          </div>
          <ul className="py-2">
            <li
              className="flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-gray-50 text-[15px] text-[#252B37]"
              onClick={toggleFilters}
            >
              <img
                src={"/assets/icons/filter.svg"}
                alt="Filter"
                className="w-4 h-4"
              />
              Filter
            </li>
            <li
              className="flex items-center gap-3 px-4 py-2 cursor-pointer hover:bg-gray-50 text-[15px] text-[#252B37]"
              onClick={toggleSorting}
            >
              <img
                src={"/assets/icons/sorting.svg"}
                alt="Sorting"
                className="w-4 h-4"
              />
              Sorting
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default ViewOptionsDropdown;
