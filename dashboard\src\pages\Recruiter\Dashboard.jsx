import React, { useEffect, useState } from "react";
import StatsCard from "../../components/common/Dashboard/StatsCard";
import ContainerCard from "../../components/common/Dashboard/ContainerCard";
import RadialBarChart from "../../components/common/Charts/RadialBarChart";
import PolarAreaChart from "../../components/common/Charts/PolarAreaChart";
import ListCard from "../../components/common/Dashboard/ListCard";
import OverAllStats from "../../components/common/Dashboard/OverAllStats";
import { getRecruiterDasboard } from "../../services/operations/dashboardAPI";
import { getRelativeTime } from "../../utils/RelativeTimeFormatter";
import { useNavigate } from "react-router-dom";

const Dashboard = () => {
  const navigate = useNavigate();
  const [data, setData] = useState(null);
  async function callAPI() {
    const res = await getRecruiterDasboard();
    setData(res.data);
  }

  useEffect(() => {
    callAPI();
  }, []);

  return (
    <>
      <div className="flex gap-3">
        <StatsCard
          onClick={(e) => {
            navigate("/jobs?tabs=0");
          }}
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          lable="Jobs Working On"
          value={data?.workingOn || 0}
        />
        <StatsCard
          onClick={(e) => {
            navigate("/jobs?tabs=1");
          }}
          imageBackground="bg-[#D1E9FF]"
          image="/assets/icons/file_color_blue.svg"
          lable="Matched Jobs"
          value={data?.matchJob || 0}
        />
        <StatsCard
          onClick={(e) => {
            navigate("/jobs?tabs=2");
          }}
          imageBackground="bg-[#FEE4E2]"
          image="/assets/icons/file_color_red.svg"
          lable="Work-On Requests"
          value={data?.workonrequest || 0}
        />
        <StatsCard
          onClick={(e) => {
            navigate("/candidate?tabs=0");
          }}
          imageBackground="bg-[#D1FADF]"
          image="/assets/icons/file_color_green.svg"
          lable="Submitted Candidates"
          value={data?.candidateSubmitted || 0}
        />
        <StatsCard
          onClick={(e) => {
            navigate("/candidate?tabs=0");
          }}
          imageBackground="bg-[#FEEFC7]"
          image="/assets/icons/file_color_yellow.svg"
          lable="Interviewed Candidates"
          value={data?.candidateSelected || 0}
        />
      </div>
      <div className="mt-2 flex gap-3">
        <ContainerCard
          onClick={(e) => {
            navigate("/jobs?tabs=3");
          }}
          onChange={(e) => {}}
          value="This Month"
          isDropDown={false}
          label="Jobs"
          width="flex-grow-[1.5] basis-2/6"
          children={
            <>
              <PolarAreaChart
                series={[
                  // data?.alljob || 0,
                  data?.workonrequest || 0,
                  data?.workingOn || 0,
                  data?.matchJob || 0,
                ]}
                labelNames={[
                  // "All Jobs",
                  "Work on Request",
                  "Working On",
                  "Matched Jobs",
                ]}
              />
            </>
          }
        />
        <ContainerCard
          width="flex-grow basis-1/4"
          label="Working On"
          children={
            <>
              {!data && (
                <div className="text-sm px-20 py-12 text-center text-gray-800">
                  No Record found
                </div>
              )}
              {data?.recentworkingon?.map((item) => {
                return (
                  <ListCard
                    onClick={(e) => {
                      navigate(
                        `/jobs/jobdetails?tab=job-details&jobId=${item?.jobInfo?.jobId}`
                      );
                    }}
                    time={getRelativeTime(item?.assignedAt)}
                    value={item?.jobInfo?.jobTitle}
                    varient="update"
                  />
                );
              })}
            </>
          }
        />
        <ContainerCard
          width="flex-grow basis-1/4"
          label="Job Updates"
          childrenHeight={" h-52 overflow-auto"}
          children={
            <>
              {data?.jobUpdate?.length < 1 && (
                <div className="text-sm px-20  py-12 text-center text-gray-800">
                  No Record found
                </div>
              )}
              {data?.jobUpdate?.map((item) => {
                return (
                  <ListCard
                    onClick={() => {
                      navigate(
                        `/jobs/jobdetails?tab=updates&jobId=${item?.jobID}`
                      );
                    }}
                    time={getRelativeTime(item?.createdAt)}
                    value={
                      <>
                        <div className="text-[12px]">
                          {item?.title} - ({item?.jobID})
                        </div>
                        <div className="text-[12px]">
                          {item?.update
                            ? Object.keys(item?.update)
                                ?.filter(
                                  (filterItem) =>
                                    !["updatedAt"].includes(filterItem)
                                )
                                ?.map(
                                  (mapItem) =>
                                    mapItem?.slice(0, 1).toUpperCase() +
                                    mapItem?.slice(1).toLowerCase()
                                )?.length > 1
                              ? Object.keys(item?.update)
                                  ?.filter(
                                    (filterItem) =>
                                      !["updatedAt"].includes(filterItem)
                                  )
                                  ?.map(
                                    (mapItem) =>
                                      mapItem?.slice(0, 1).toUpperCase() +
                                      mapItem?.slice(1).toLowerCase()
                                  )
                                  .slice(0, 2) + "..."
                              : Object.keys(item?.update)
                                  ?.filter(
                                    (filterItem) =>
                                      !["updatedAt"].includes(filterItem)
                                  )
                                  ?.map(
                                    (mapItem) =>
                                      mapItem?.slice(0, 1).toUpperCase() +
                                      mapItem?.slice(1).toLowerCase()
                                  )
                            : ""}
                        </div>
                      </>
                    }
                    varient="job"
                  />
                );
              })}
            </>
          }
        />
      </div>

      <div className="mt-2 flex gap-3">
        <ContainerCard
          onChange={(e) => {}}
          value="This Month"
          isDropDown={false}
          label="Overall Stats"
          width="flex-grow-[1.5] basis-4/6"
          children={
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-y-4">
                {[
                  {
                    value: data?.candidateSubmitted || 0,
                    label: "Candidate Submitted",
                    // percentChange: 18,
                  },
                  {
                    value: data?.candidateSelected || 0,
                    label: "Candidate Selected",
                    // percentChange: 25,
                    // isPostive: false,
                  },
                  {
                    value: data ? data?.offerReleased || 0 : 0,
                    label: "Offer Released",
                    //  percentChange: 25
                  },
                  {
                    value: data ? data?.offerAccepted || 0 : 0,
                    label: "Offer Accepted",
                  },
                  {
                    value: data ? data?.offerRejected || 0 : 0,
                    label: "Offer Rejected",
                  },
                  {
                    value: data ? data?.backout || 0 : 0,
                    label: "Quit During Guarantee Period",
                  },
                ].map((stat, index) => (
                  <OverAllStats key={index} {...stat} />
                ))}
              </div>
            </>
          }
        />
        <ContainerCard
          onChange={(e) => {}}
          value="This Month"
          isDropDown={false}
          label="Candidates"
          width="flex-grow basis-3/6"
          children={
            <>
              <RadialBarChart
                series={[
                  data?.candidateSubmitted || 0,
                  0,
                  data?.candidateSelected || 0,
                  data?.offerAccepted || 0,
                  data ? data?.offerRejected || 0 : 0,
                ]}
                countTotal={
                  data?.totalCandidiate +
                  data?.candidateSelected +
                  data?.offerAccepted +
                  data?.offerRejected
                }
                labelNames={[
                  "Submission",
                  "Hold",
                  "Interviewed",
                  "Hired",
                  "Decline",
                ]}
              />
            </>
          }
        />
      </div>
    </>
  );
};

export default Dashboard;
