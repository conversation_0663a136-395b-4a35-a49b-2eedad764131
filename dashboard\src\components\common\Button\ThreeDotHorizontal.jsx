import React, { useEffect, useRef, useState } from "react";

const ThreeDotHorizontal = ({ buttonDropDown = "", dropdownSize = "w-56" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div ref={dropdownRef} className="relative">
      <div className="flex items-center gap-2">
        <img
          aria-expanded={isOpen}
          onClick={() => setIsOpen(!isOpen)}
          src={"/assets/icons/threedothorizontal.svg"}
          className="w-8 h-8 cursor-pointer object-contain block flex-none"
          alt="More options"
        />
      </div>
      {isOpen && (
        <div
          className={`absolute right-0 z-50 my-1 me-3 ${dropdownSize} text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-md`}
        >
          {typeof buttonDropDown === "function"
            ? buttonDropDown(() => setIsOpen(false))
            : buttonDropDown}
        </div>
      )}
    </div>
  );
};

export default ThreeDotHorizontal;
