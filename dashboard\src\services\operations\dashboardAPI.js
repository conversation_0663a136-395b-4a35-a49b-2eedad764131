import { apiConnector } from "../apiConnector";

import {
  accountManagerEndpoints,
  dashboardEndPoint,
  jobEndpoints,
} from "../apis";

const {
  GET_DASHBOARD_RECRUITER_API,
  GET_DASHBOARD_HEAD_ACCOUNT_MANAGER_API,
  GET_DASHBOARD_SUBMISSION_STATS_API,
} = dashboardEndPoint;

const { GET_ALL_SUBMISSIONS_API } = accountManagerEndpoints;

const { GET_ALL_JOBS_API } = jobEndpoints;

export const getRecruiterDasboard = async () => {
  const response = await apiConnector("GET", GET_DASHBOARD_RECRUITER_API);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const getHeadAccountDasboard = async () => {
  const response = await apiConnector(
    "GET",
    GET_DASHBOARD_HEAD_ACCOUNT_MANAGER_API
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response.data;
};

export const getAccountManagerSubmissionStats = async (managerId = null) => {
  try {
    // Build the URL with query parameter if managerId is provided
    let url = GET_DASHBOARD_SUBMISSION_STATS_API;

    if (managerId) {
      // Add manager ID as query parameter
      const separator = url.includes("?") ? "&" : "?";
      url += `${separator}managerid=${managerId}`;
    }
    const response = await apiConnector("GET", url);
    if (!response?.data?.success) {
      throw new Error(
        response?.data?.message || "Failed to fetch submission stats"
      );
    }
    return response.data;
  } catch (error) {
    console.error(" Error in getAccountManagerSubmissionStats:", error);
    throw error;
  }
  return response.data;
};

export const getAllJobDashboard = async () => {
  try {
    const response = await apiConnector("GET", GET_ALL_JOBS_API, {}, {});

    if (!response?.data?.success) throw new Error(response?.data?.message);
    return response.data;
  } catch (error) {
    console.error("[jobAPI] Error fetching all jobs:", error);
    return {
      success: false,
      message: error.message,
      results: [],
      total: 0,
      totalPages: 1,
      page: 1,
    };
  }
};

export const getAllSubmissionDashboard = async () => {
  try {
    // Make the API call
    const response = await apiConnector("GET", `${GET_ALL_SUBMISSIONS_API}`);

    return response?.data;
  } catch (error) {
    console.error("Error fetching submissions:", error);
    throw error;
  }
};
