const express = require("express");
const {
  // registerPayout,
  createPayout,
  getPayout,
  updatePayoutDetails,
} = require("../controllers/payout");
const { auth, authorize } = require("../middlewares/auth");
const router = express.Router();

// router.post("/create", auth, authorize("accountManager"), registerPayout);
router.post(
  "/create-payout",
  auth,
  authorize("accountManager", "headAccountManager"),
  createPayout
);

router.put(
  "/update-details",
  auth,
  authorize("accountManager", "headAccountManager"),
  updatePayoutDetails
);

router.get(
  "/getpayout",
  auth,
  authorize("recruiter", "accountManager", "headAccountManager"),
  getPayout
);

module.exports = router;
