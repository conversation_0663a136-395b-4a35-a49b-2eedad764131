import React from "react";
import <PERSON>Field from "../Input/SelectionField";

const ContainerCard = ({
  width = "w-full",
  onClick,
  label = "",
  isDropDown = false,
  placeholder = "Filter Data",
  value = "This Month",
  options = [
    { label: "This Week", value: "This Week" },
    { label: "This Month", value: "This Month" },
    { label: "This Year", value: "This Year" },
  ],
  onChange,
  children,
  childrenHeight,
}) => {
  return (
    <>
      <div
        onClick={onClick}
        className={`bg-white ${
          onClick && "cursor-pointer"
        }  rounded-xl shadow-sm p-3  border border-white  hover:border-[#3E9900] ${width}`}
      >
        <div className="flex items-center justify-between border-b pb-0.5 border-[#F5F5F5]">
          <div className="flex items-center gap-1 ">
            <div className="flex items-center">
              <img
                src={"/assets/icons/dot-3.svg"}
                className="w-7 h-7 cursor-pointer object-contain block flex-none"
              />
            </div>
            <div className="text-gray-700 font-medium text-sm">{label}</div>
          </div>

          {isDropDown && (
            <div>
              <SelectField
                css="border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-transparent text-sm text-gray-700"
                placeholder={placeholder}
                options={options}
                value={value}
                onChange={onChange}
              />
            </div>
          )}
        </div>
        <div className={`space-y-3 ${childrenHeight}`}>{children}</div>
      </div>
    </>
  );
};

export default ContainerCard;
