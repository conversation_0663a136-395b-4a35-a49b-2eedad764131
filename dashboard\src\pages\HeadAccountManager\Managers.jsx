import { useState } from "react";
import EmptyState from "../../components/common/EmptyState";

const Managers = () => {
  const [managers, setManagers] = useState([]);
  // API integration ready - endpoint: /headmanager/manager/getallmanager

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Managers</h1>
        <p className="text-gray-600 mt-1">Manage and view all account managers</p>
      </div>

      {/* Data validation - show content if data exists, otherwise show EmptyState */}
      {managers.length > 0 ? (
        <div className="bg-white rounded-lg shadow">
          {/* Table or content will go here when API is connected */}
          <div className="p-4">
            {managers.map((manager, index) => (
              <div key={index} className="border-b py-2">
                <p className="font-medium">{manager.name}</p>
                <p className="text-sm text-gray-600">{manager.email}</p>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <EmptyState
          title="No Managers Found"
          description="There are no account managers to display at the moment. Managers will appear here once they are added to the system."
        />
      )}
    </div>
  );
};

export default Managers;
