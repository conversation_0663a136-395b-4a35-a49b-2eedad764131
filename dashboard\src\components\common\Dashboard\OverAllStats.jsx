import { ArrowDown, ArrowUp } from "lucide-react";

const OverAllStats = ({ value, label, percentChange, isPostive = true }) => {
  return (
    <div className="p-3 min-w-[140px]">
      <div className="flex items-center justify-between gap-2">
        <p className="text-[20px] font-semibold text-gray-800">{value}</p>
        {percentChange !== undefined && (
          <>
            {isPostive ? (
              <div className="flex items-center gap-1 text-sm text-green-600 font-medium">
                <ArrowUp className="w-4 h-4" />
                {percentChange}%
              </div>
            ) : (
              <div className="flex items-center gap-1 text-sm text-red-600 font-medium">
                <ArrowDown className="w-4 h-4" />
                {percentChange}%
              </div>
            )}
          </>
        )}
      </div>
      <p className="text-sm text-gray-500">{label}</p>
    </div>
  );
};

export default OverAllStats;
