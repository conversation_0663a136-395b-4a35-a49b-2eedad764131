import Image from "next/image";
import logo from "@/public/assets/logo.png";
import Link from "next/link";

const Navbar = () => {
  return (
    <div className="w-full px-5 md:px-16 py-4 flex justify-between items-center">
      <div className="w-[10rem] flex items-center md:w-[12.875rem] h-[3.25rem]">
        <Link href="/">
          <Image src={logo} alt="logo" />
        </Link>
      </div>
      <div className="flex items-center gap-10">
        <a href="https://recruiter.hirring.com/signup">
          <button className=" bg-[#020401] text-white px-4 py-2 rounded-md cursor-pointer">
            Sign Up
          </button>
        </a>
      </div>
    </div>
  );
};

export default Navbar;
