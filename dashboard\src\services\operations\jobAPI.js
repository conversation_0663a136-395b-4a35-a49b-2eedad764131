import { apiConnector } from "../apiConnector";
import { jobEndpoints } from "../apis";

const {
  CREATE_JOBS_API,
  GET_ALL_JOBS_API,
  BULK_UPLOAD_JOBS_API,
  GET_JOBS_BY_ID_API,
  UPDATE_JOB_HEAD_ACCOUNT_API,
  PUBLISH_JOB_HEAD_ACCOUNT_API,
  GET_UNASSIGNED_JOBS_API,
  GET_HIGH_PERIORITY_JOBS,
  BULK_ASSIGN_JOB_TO_MANAGER,
  GET_ACTIVE_JOBS,
  GET_CLOSE_JOBS,
  GET_UN_ENGAGED_JOBS,
  UPDATE_JOB_STATUS,
  GET_ASSIGN_RECRUITER_API,
  WORK_ON_REQUEST_API,
  REMOVE_WORK_ON_REQUEST_API,

  // AM JOBS API
  PUBLISH_JOB_API,
  GET_ALL_RECRUITERS_WORKING_ON_JOB,
  GET_JOBS_BY_ACCOUNT_MANAGER,

  // recruiter
  GET_WORK_UPON_JOBS,
  GET_MATCHED_JOBS,
  GET_WORK_ON_REQUEST_JOBS,
  GET_ALL_JOBS,
  GET_SAVE_JOBS,
  ADD_BOOKMARK_JOBS,
  REMOVE_BOOKMARK_JOBS,
  WORK_ON_REQUEST_STATUS_UPDATE_API,
  SELECT_JOB_TO_WORK_ON_API,
  UPMAP_JOB_API,
} = jobEndpoints;

export const createJOB = async (data) => {
  const response = await apiConnector("POST", CREATE_JOBS_API, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getJobsByAccountManager = async () => {
  const response = await apiConnector("GET", GET_JOBS_BY_ACCOUNT_MANAGER);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const bulkJobUpload = async (data) => {
  const response = await apiConnector("POST", BULK_UPLOAD_JOBS_API, data, {
    "Content-Type": "multipart/form-data",
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getAllJobs = async (page = 1, limit = 10, filters = {}) => {
  try {
    const queryParts = [`page=${page}`, `limit=${limit}`];

    if (filters.locations) queryParts.push(`locations=${filters.locations}`);
    if (filters.jobTypes) queryParts.push(`jobTypes=${filters.jobTypes}`);
    if (filters.jobStatus) queryParts.push(`jobStatus=${filters.jobStatus}`);
    if (filters.visibility) queryParts.push(`visibility=${filters.visibility}`);
    if (filters.sortBy) queryParts.push(`sortBy=${filters.sortBy}`);
    if (filters.sortOrder) queryParts.push(`sortOrder=${filters.sortOrder}`);
    if (filters.search) queryParts.push(`search=${filters.search}`);
    if (filters.searchField)
      queryParts.push(`searchField=${filters.searchField}`);
    if (filters.domain) queryParts.push(`domain=${filters.domain}`);

    const fullURL = `${GET_ALL_JOBS_API}?${queryParts.join("&")}`;
    const response = await apiConnector("GET", fullURL);

    if (!response?.data?.success) throw new Error(response?.data?.message);
    return response.data;
  } catch (error) {
    console.error("[jobAPI] Error fetching all jobs:", error);
    return {
      success: false,
      message: error.message,
      results: [],
      total: 0,
      totalPages: 1,
      page: 1,
    };
  }
  return response?.data;
};

export const getAllUnassignedJobs = async (
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  // Build query parameters
  const queryParams = new URLSearchParams();

  // Add pagination
  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters - convert arrays to comma-separated strings
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  if (sorting.postedDate) {
    queryParams.append("postedDate", sorting.postedDate);
  }

  // Add search parameters
  if (search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());
    if (search.searchField && search.searchField !== "all") {
      queryParams.append("searchField", search.searchField);
    }
  }
  const response = await apiConnector(
    "GET",
    `${GET_UNASSIGNED_JOBS_API}?${queryParams.toString()}`,
    {},
    {}
  );

  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }

  return response?.data;
};

export const getJobByID = async (jobID) => {
  const response = await apiConnector("GET", `${GET_JOBS_BY_ID_API}/${jobID}`);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const updateJob = async (data, userinfo) => {
  const response = await apiConnector(
    "PATCH",
    userinfo?.role == "accountManager"
      ? PUBLISH_JOB_API
      : UPDATE_JOB_HEAD_ACCOUNT_API,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const publishJobByHeadAccount = async (data) => {
  const response = await apiConnector(
    "PATCH",
    PUBLISH_JOB_HEAD_ACCOUNT_API,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const bulkAssignJobToManager = async (data) => {
  const response = await apiConnector(
    "PATCH",
    BULK_ASSIGN_JOB_TO_MANAGER,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const gethighperiorityJobs = async (
  page = 1,
  limit = 10,
  filters = {}
) => {
  try {
    const queryParts = [`page=${page}`, `limit=${limit}`];

    if (filters.locations) queryParts.push(`locations=${filters.locations}`);
    if (filters.jobTypes) queryParts.push(`jobTypes=${filters.jobTypes}`);
    if (filters.sortOrder) queryParts.push(`sortOrder=${filters.sortOrder}`);
    if (filters.search) queryParts.push(`search=${filters.search}`);
    if (filters.searchField)
      queryParts.push(`searchField=${filters.searchField}`);
    if (filters.domain) queryParts.push(`domain=${filters.domain}`);
    if (filters.status) queryParts.push(`status=${filters.status}`);

    const fullURL = `${GET_HIGH_PERIORITY_JOBS}?${queryParts.join("&")}`;
    const response = await apiConnector("GET", fullURL);

    if (!response?.data?.success) throw new Error(response?.data?.message);
    return response.data;
  } catch (error) {
    console.error("[jobAPI] Error fetching high priority jobs:", error);
    return {
      success: false,
      message: error.message,
      results: [],
      total: 0,
      totalPages: 1,
      page: 1,
    };
  }
  return response?.data;
};

export const getActiveJobs = async (
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  // Build query parameters
  const queryParams = new URLSearchParams();

  // Add pagination
  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters - convert arrays to comma-separated strings
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  // Add sorting parameters
  if (sorting.postedDate) {
    queryParams.append("postedDate", sorting.postedDate);
  }
  if (sorting.sortBy) {
    queryParams.append("sortBy", sorting.sortBy);
  }
  if (sorting.sortOrder) {
    queryParams.append("sortOrder", sorting.sortOrder);
  }

  // Add search parameters
  if (search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());
    if (search.searchField && search.searchField !== "all") {
      queryParams.append("searchField", search.searchField);
    }
  }

  try {
    const response = await apiConnector(
      "GET",
      `${GET_ACTIVE_JOBS}?${queryParams.toString()}`,
      {},
      {}
    );

    if (!response?.data?.success) {
      throw new Error(response?.data?.message || "Failed to fetch active jobs");
    }

    return response?.data;
  } catch (error) {
    console.error("Error fetching active jobs:", error);
    throw error;
  }
};
export const getCloseJobs = async (page, limit, filters = {}) => {
  try {
    const queryParts = [`page=${page}`, `limit=${limit}`];

    if (filters.locations) queryParts.push(`locations=${filters.locations}`);
    if (filters.jobTypes) queryParts.push(`jobTypes=${filters.jobTypes}`);
    if (filters.sortOrder) queryParts.push(`sortOrder=${filters.sortOrder}`);
    if (filters.search) queryParts.push(`search=${filters.search}`);
    if (filters.searchField)
      queryParts.push(`searchField=${filters.searchField}`);
    if (filters.domain) queryParts.push(`domain=${filters.domain}`);
    // CHANGE THIS LINE: Use jobStatus instead of status
    if (filters.jobStatus) queryParts.push(`jobStatus=${filters.jobStatus}`);

    const fullURL = `${GET_CLOSE_JOBS}?${queryParts.join("&")}`;
    const response = await apiConnector("GET", fullURL);

    if (!response?.data?.success) throw new Error(response?.data?.message);
    return response.data;
  } catch (error) {
    console.error("[jobAPI] Error fetching close jobs:", error);
    return {
      success: false,
      message: error.message,
      results: [],
      total: 0,
      totalPages: 1,
      page: 1,
    };
  }
};
export const getUnEngagedJobs = async (
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  const queryParams = new URLSearchParams();

  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  // Add search parameters
  if (search && search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());

    if (search.searchField && search.searchField !== "all") {
      console.log("🔍 API: Adding search field:", search.searchField);
      queryParams.append("searchField", search.searchField);
    }
  }

  // Add sorting parameters
  if (sorting.postedDate) {
    queryParams.append("postedDate", sorting.postedDate);
  }
  if (sorting.sortBy) {
    queryParams.append("sortBy", sorting.sortBy);
  }
  if (sorting.sortOrder) {
    queryParams.append("sortOrder", sorting.sortOrder);
  }

  const response = await apiConnector(
    "GET",
    `${GET_UN_ENGAGED_JOBS}?${queryParams.toString()}`,
    {},
    {}
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const updateJobStatus = async (data) => {
  const response = await apiConnector("PATCH", UPDATE_JOB_STATUS, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getAssignRecruiter = async (jobID) => {
  const response = await apiConnector(
    "GET",
    `${GET_ASSIGN_RECRUITER_API}/${jobID}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const workOnRequest = async (data) => {
  const response = await apiConnector("POST", WORK_ON_REQUEST_API, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const removeWorkOnRequest = async (data) => {
  const response = await apiConnector(
    "DELETE",
    REMOVE_WORK_ON_REQUEST_API,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

// ============================= AM JOBS ==================================

export const getAllRecruitersWorkingOnJob = async (jobId) => {
  const response = await apiConnector(
    "GET",
    `${GET_ALL_RECRUITERS_WORKING_ON_JOB}`,
    {},
    {},
    { jobId }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

// ============== recruiter jobs -=====================

export const getRecruiterJobs = async (
  jobType,
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  // Build query parameters
  const queryParams = new URLSearchParams();

  // Add pagination
  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters - convert arrays to comma-separated strings
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  // Add sorting parameters
  if (sorting.postedDate) {
    queryParams.append("postedDate", sorting.postedDate);
  }
  if (sorting.sortBy) {
    queryParams.append("sortBy", sorting.sortBy);
  }
  if (sorting.sortOrder) {
    queryParams.append("sortOrder", sorting.sortOrder);
  }

  // Add search parameters
  if (search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());
    if (search.searchField && search.searchField !== "all") {
      queryParams.append("searchField", search.searchField);
    }
  }

  const baseURL =
    jobType == "workingon"
      ? GET_WORK_UPON_JOBS
      : jobType == "mappedjob"
      ? GET_MATCHED_JOBS
      : jobType == "workonrequest"
      ? GET_WORK_ON_REQUEST_JOBS
      : jobType == "alljobs"
      ? GET_ALL_JOBS
      : jobType == "savejobs"
      ? GET_SAVE_JOBS
      : GET_WORK_UPON_JOBS;

  const response = await apiConnector(
    "GET",
    `${baseURL}?${queryParams.toString()}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const createBookmark = async (jobId) => {
  const response = await apiConnector("POST", ADD_BOOKMARK_JOBS, { jobId });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const removeBookmark = async (jobId) => {
  const response = await apiConnector("DELETE", REMOVE_BOOKMARK_JOBS, {
    jobId,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const workOnRequestStatusUpdate = async (jobId, status) => {
  const response = await apiConnector(
    "POST",
    WORK_ON_REQUEST_STATUS_UPDATE_API,
    {
      jobId,
      status,
    }
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const selectJobToWorkOn = async (jobId, workOnType) => {
  const response = await apiConnector("POST", SELECT_JOB_TO_WORK_ON_API, {
    jobId,
    workOnType,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const unmapJobs = async (jobId) => {
  const response = await apiConnector("POST", UPMAP_JOB_API, {
    jobId,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
