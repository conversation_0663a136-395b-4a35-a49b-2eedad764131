import React, { useEffect, useState } from "react";
import Job<PERSON><PERSON> from "./JobCard";
import PaginationFooter from "../../../common/Table/TableFooter";

const JobsList = ({
  getJobs,
  setBookMark,
  deleteBookMark,
  jobType,
  selectJobToWork,
  unMappedJobs,
  workOnRequestUpdate,
}) => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  async function getAllData() {
    setLoading(true);
    try {
      const data = await getJobs(currentPage, pageSize);
      setTotalPages(data.totalPages);
      setTotalResults(data.total || data.totalworkonJobs || 0);
      setJobs(data.results || []); // Ensure it's always an array
    } catch (error) {
      console.error("Error fetching jobs:", error);
      setJobs([]);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    getAllData();
  }, [currentPage, pageSize]); // Only dependencies that should trigger new API calls

  return (
    <>
      <div className="flex flex-col gap-3">
        {loading && (
          <div className="text-center py-8 text-gray-500">Loading...</div>
        )}
        {!loading && jobs?.length < 1 && (
          <div className="flex flex-col items-center justify-center py-8">
            <img
              src={"/assets/images/No Record Found.svg"}
              alt="no record found"
              className="mb-4"
            />
            <p className="text-gray-600">No Jobs Found</p>
          </div>
        )}
        {!loading &&
          jobs &&
          jobs.map((item, key) => (
            <JobCard
              jobType={jobType ? jobType : item.jobType}
              item={item}
              key={key}
              setJobs={setJobs}
              unMappedJobs={unMappedJobs}
              setBookMark={setBookMark}
              workOnRequestUpdate={workOnRequestUpdate}
              selectJobToWork={selectJobToWork}
              deleteBookMark={deleteBookMark}
            />
          ))}
      </div>
      <PaginationFooter
        totalResults={totalResults}
        pageSize={pageSize}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        setPageSize={setPageSize}
        totalPages={totalPages}
      />
    </>
  );
};

export default JobsList;
