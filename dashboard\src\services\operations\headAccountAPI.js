import { apiConnector } from "../apiConnector";
import { headManager, accountManagerEndpoints } from "../apis";

const {
  GET_ALL_MANAGER_WORK_ON_REQUEST,
  GET_ALL_SUBMISSIONS,
  GET_ALL_ACCOUNT_MANAGER,
  CREATE_ACCOUNT_MANAGER,
  GET_ACCOUNT_MANAGER_BY_ID,
  UPDATE_ACCOUNT_MANAGER,
} = headManager;

const { GET_ALL_ACCOUNT_MANAGER_SUBMISSIONS } = accountManagerEndpoints;

export const getallManagerWorkHistory = async () => {
  const response = await apiConnector("GET", GET_ALL_MANAGER_WORK_ON_REQUEST);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const updateAccountManager = async (data, managerID) => {
  const response = await apiConnector(
    "POST",
    `${UPDATE_ACCOUNT_MANAGER}/${managerID}`,
    data
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
export const getAccountByID = async (jobID) => {
  const response = await apiConnector(
    "GET",
    `${GET_ACCOUNT_MANAGER_BY_ID}/${jobID}`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const getAllSubmissions = async (
  page = 1,
  limit = 10,
  userType,
  filters = {},
  sorting = {},
  search = {}
) => {
  const queryParams = new URLSearchParams();

  // Add basic pagination parameters
  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  // Add search parameters
  if (search && search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());

    if (search.searchField && search.searchField !== "all") {
      queryParams.append("searchField", search.searchField);
    }
  }

  // Add sorting parameters - UPDATED to handle both submissionDate and postedDate
  if (sorting.submissionDate) {
    // ✅ ADDED: Check for submissionDate first
    queryParams.append("submissionDate", sorting.submissionDate);
  } else if (sorting.postedDate) {
    // ✅ UPDATED: Keep as fallback for backward compatibility
    queryParams.append("postedDate", sorting.postedDate);
  }

  if (sorting.sortBy) {
    queryParams.append("sortBy", sorting.sortBy);
  }
  if (sorting.sortOrder) {
    queryParams.append("sortOrder", sorting.sortOrder);
  }

  // Determine the API endpoint based on userType
  const apiEndpoint =
    userType === "accountManager"
      ? GET_ALL_ACCOUNT_MANAGER_SUBMISSIONS
      : GET_ALL_SUBMISSIONS;

  const response = await apiConnector(
    "GET",
    `${apiEndpoint}?${queryParams.toString()}`,
    {},
    {}
  );

  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }

  return response?.data;
};

export const getAllManager = async (
  page = 1,
  limit = 10,
  filters = {},
  sorting = {},
  search = {}
) => {
  const queryParams = new URLSearchParams();

  queryParams.append("page", page);
  queryParams.append("limit", limit);

  // Add filters
  Object.keys(filters).forEach((key) => {
    const filterValue = filters[key];
    if (filterValue && Array.isArray(filterValue) && filterValue.length > 0) {
      queryParams.append(key, filterValue.join(","));
    } else if (
      filterValue &&
      typeof filterValue === "string" &&
      filterValue.trim()
    ) {
      queryParams.append(key, filterValue);
    }
  });

  // Add search parameters
  if (search && search.searchTerm && search.searchTerm.trim()) {
    queryParams.append("search", search.searchTerm.trim());

    if (search.searchField && search.searchField !== "all") {
      queryParams.append("searchField", search.searchField);
    }
  }

  // Add sorting parameters
  if (sorting.postedDate) {
    queryParams.append("postedDate", sorting.postedDate);
  }
  if (sorting.sortBy) {
    queryParams.append("sortBy", sorting.sortBy);
  }
  if (sorting.sortOrder) {
    queryParams.append("sortOrder", sorting.sortOrder);
  }

  const response = await apiConnector(
    "GET",
    `${GET_ALL_ACCOUNT_MANAGER}?${queryParams.toString()}`,
    {},
    {},
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const createAccountManager = async (data) => {
  const response = await apiConnector("POST", CREATE_ACCOUNT_MANAGER, data);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
