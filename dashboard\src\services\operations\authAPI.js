import { apiConnector } from "../apiConnector";
import { authEndpoints } from "../apis";

const {
  SIGN_UP_API,
  SIGN_IN_API,
  SIGN_IN_USER_INFO_API,
  GENRATE_ACCESS_TOKEN_API,
  VERIFY_EMAIL_API,
  VERIFY_FORGOT_PASSWORD_API,
  RESET_PASSWORD_API,
} = authEndpoints;

export const register = async (data) => {
  const response = await apiConnector("POST", SIGN_UP_API, data, {}, {}, true);
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const resetPassword = async (data) => {
  const response = await apiConnector(
    "POST",
    RESET_PASSWORD_API,
    data,
    {},
    {},
    true
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const forgotPassword = async (email) => {
  const response = await apiConnector("POST", VERIFY_FORGOT_PASSWORD_API, {
    email,
  });
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const verifyEmail = async (token) => {
  const response = await apiConnector(
    "GET",
    `${VERIFY_EMAIL_API}?token=${token}`,
    null,
    {}
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const verifyPasswordResetToken = async (token) => {
  const response = await apiConnector(
    "GET",
    `${VERIFY_EMAIL_API}?token=${token}&type=passwordReset`
  );
  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const login = async (data) => {
  const response = await apiConnector("POST", SIGN_IN_API, data, {}, {}, true);

  if (!(response.status === 200)) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const genrateAccessToken = async (data) => {
  const response = await apiConnector(
    "POST",
    GENRATE_ACCESS_TOKEN_API,
    data,
    {},
    {},
    true
  );

  if (!(response.status === 200)) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};

export const loginUserInfo = async () => {
  const response = await apiConnector("GET", SIGN_IN_USER_INFO_API);

  if (!response?.data?.success) {
    throw new Error(response?.data?.message);
  }
  return response?.data;
};
