import heroBg from "@/public/assets/images/homepage/hero-bg.webp";

const Hero = () => {
  return (
    <section
      className="flex flex-col items-center justify-center h-[calc(100vh-6rem)] gap-12  w-full"
      style={{
        backgroundImage: `url(${heroBg.src})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
      }}
    >
      <h1
        className="text-center  font-semibold text-6xl lg:text-7xl xl:text-8xl bg-clip-text"
        style={{
          backgroundImage: "linear-gradient(-62deg, #09090B 0%, #296600 100%)",
          WebkitTextFillColor: "transparent",
          WebkitBackgroundClip: "text",
          MozTextFillColor: "transparent",
          MozBackgroundClip: "text",
          backgroundSize: "100%",
        }}
      >
        Empowering Freelance <br /> Recruiters, Globally
      </h1>
      <p className="text-center text-xl text-[#71717A] w-[85%] lg:w-[75%] mx-auto  ">
        HIRRING.COM is the future of recruitment, giving freelance recruiters
        the tools, resources, and support to build consistent, hassle-free
        income streams.
      </p>
      <a href="https://recruiter.hirring.com/user/signup">
        <button className="bg-[#65FF00] text-uiBlack px-4 py-2 rounded-md  font-medium cursor-pointer">
          Start Earning
        </button>
      </a>
    </section>
  );
};

export default Hero;
