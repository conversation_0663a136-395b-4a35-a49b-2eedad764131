import React from "react";

const ColorSelectionInput = ({
  value,
  onChange,
  options = [],
  width,
  disabled = false,
}) => {
  const selected = options.find((opt) => opt.value === value) || {};

  return (
    <div
      className={`relative inline-flex items-center py-1 rounded-full text-sm font-medium ${
        selected.color || "text-gray-600 bg-gray-100"
      }`}
    >
      <span className="w-2 h-2 rounded-full bg-current ms-3"></span>
      <select
        disabled={disabled}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={`bg-transparent outline-none ${
          !disabled && "cursor-pointer"
        } text-current appearance-none px-3 ${width}`}
      >
        {options.map((opt) => (
          <option key={opt.value} className={`${opt.color}`} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </select>
      {!disabled && (
        <svg
          className="absolute right-2 pointer-events-none w-4 h-4 text-current"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      )}
    </div>
  );
};

export default ColorSelectionInput;
