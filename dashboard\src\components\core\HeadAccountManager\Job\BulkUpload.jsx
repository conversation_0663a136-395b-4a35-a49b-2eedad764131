import React, { useState } from "react";
import Modal from "../../../common/Modal/Modal";
import AppButton from "../../../common/Button/AppButton";
import { bulkJobUpload } from "../../../../services/operations/jobAPI";
import { toast } from "react-toastify";

const BulkUpload = ({ setIsModalOpen, isModalOpen, getAllJob }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [loading, setLoading] = useState(false);

  const formatFileSize = (sizeInBytes) => {
    if (sizeInBytes >= 1024 * 1024) {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
    } else if (sizeInBytes >= 1024) {
      return `${(sizeInBytes / 1024).toFixed(1)} KB`;
    } else {
      return `${sizeInBytes} bytes`;
    }
  };

  const onFileSelect = (file) => {
    setSelectedFile(file);
  };

  const [dragActive, setDragActive] = useState(false);

  const handleDrop = (e) => {
    e.preventDefault();
    setDragActive(false);

    const file = e.dataTransfer.files[0];
    if (file && (file.name.endsWith(".csv") || file.name.endsWith(".xlsx"))) {
      onFileSelect(file);
    }
  };

  const handleBrowse = (e) => {
    const file = e.target.files[0];
    if (file && (file.name.endsWith(".csv") || file.name.endsWith(".xlsx"))) {
      onFileSelect(file);
    }
    e.target.value = "";
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  async function handleBulkUpload() {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("csvFile", selectedFile);
      const response = await bulkJobUpload(formData);
      if (response.success) {
        setLoading(false);
        setIsModalOpen(false);
        getAllJob();
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message ||
          "File upload failed. Please try uploading another file."
      );
      console.log(
        error?.response?.data?.message ||
          "File upload failed. Please try uploading another file."
      );
      setLoading(false);
    }
  }

  return (
    <>
      <Modal
        title={"Media Upload"}
        isOpen={isModalOpen}
        onClose={() => {
          handleRemoveFile();
          setIsModalOpen(false);
        }}
      >
        <div className="px-8 pt-1 pb-10">
          <div
            className={`border-2 border-dashed rounded-md p-6 transition-all ${
              dragActive ? "border-green-500 bg-green-50" : "border-gray-300"
            }`}
            onDragOver={(e) => {
              e.preventDefault();
              setDragActive(true);
            }}
            onDragLeave={() => setDragActive(false)}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center gap-2 text-sm text-gray-500">
              <img
                src={"/assets/icons/upload.svg"}
                alt="Bulk Upload"
                className="w-6 h-6 text-blue-600"
              />
              <p>Drag your file to start uploading</p>
              <span className="text-xs text-gray-400">OR</span>
              <label className="cursor-pointer inline-block px-4 py-2 mt-1 text-green-700 border border-green-600 rounded hover:bg-green-100">
                Browse File
                <input
                  type="file"
                  accept=".csv,.xlsx"
                  className="hidden"
                  onChange={handleBrowse}
                />
              </label>
            </div>
          </div>

          <p className="text-xs text-gray-400 mt-2">
            Only support .csv and .xlsx files
          </p>

          {selectedFile && (
            <div className="my-2 border rounded-md flex items-center justify-between p-4 border-[#F1F4F9]">
              <div className="flex items-center gap-3">
                {/* Replace this src with an Excel icon SVG if needed */}
                <img
                  src="/assets/icons/excel_report.svg"
                  alt="Excel Icon"
                  className="w-6 h-6"
                />
                <div>
                  <p className="text-sm font-medium">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
              </div>
              <button
                onClick={handleRemoveFile}
                className="text-gray-400 hover:text-red-500 text-2xl cursor-pointer"
              >
                &times;
              </button>
            </div>
          )}

          <div className="flex justify-end items-center gap-3">
            <AppButton
              label={"Cancel"}
              variant="secondary"
              onClick={() => {
                handleRemoveFile();
                setIsModalOpen(false);
              }}
            />
            <AppButton
              disable={loading || !selectedFile ? true : false}
              label={loading ? "loading..." : "Continue"}
              onClick={() => {
                handleBulkUpload();
              }}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BulkUpload;
