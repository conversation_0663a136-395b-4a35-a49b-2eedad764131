import React from "react";

const TabNav = ({ nav = [],setActive,active=0 ,rightSidebar}) => {
  return (
    <>
      <div className="text-sm font-medium text-center text-[#64748B] border-b border-gray-200 flex items-center justify-between ">
        <ul className="flex flex-wrap -mb-px">
          {nav?.map((item, key) => {
            return (
              <li className="me-1 cursor-pointer" key={key} onClick={(e)=>{
                e.preventDefault();
                setActive(key)
              }}>
                <div
                  className={
                    active == key
                      ? `inline-block p-4 text-[#1E2A3B] border-b-2 border-[#296600] rounded-t-lg active ${item?.css}`
                      : `inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 ${item?.css}`
                  }
                >
                  {item?.name}
                </div>
              </li>
            );
          })}
        </ul>
        <div>
           {rightSidebar} 
        </div>
       
      </div>
    </>
  );
};

export default TabNav;
