const jwt = require("jsonwebtoken");
const BlackListedToken = require("../models/BlackListedToken");
const { RefreshToken } = require("../models/RefreshToken");

// Generate access token
exports.generateAccessToken = (data) => {
  return jwt.sign(
    { ...data },
    process.env.JWT_SECRET,
    { expiresIn: "15m" } // Short-lived token
  );
};

// Generate refresh token
exports.generateRefreshToken = async (userId, tokenVersion) => {
  const isRefreshToken = await RefreshToken.findOne({ userId: userId });
  if (isRefreshToken) {
    await RefreshToken.findOneAndDelete({ userId: userId });
  }

  const refreshToken = jwt.sign(
    { userId, tokenVersion },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: "7d" }
  );
  const refreshTokenInstance = new RefreshToken({
    token: refreshToken,
    userId: userId,
  });
  await refreshTokenInstance.save();
  return refreshToken;
};

// Verify access token
exports.verifyAccessToken = (token) => {
  try {
    const data = jwt.verify(token, process.env.JWT_SECRET);
    return { success: true, data: data };
  } catch (error) {
    return { success: false };
  }
};

// Verify refresh token
exports.verifyRefreshToken = (token) => {
  try {
    const data = jwt.verify(token, process.env.JWT_REFRESH_SECRET);
    return { success: true, data: data };
  } catch (error) {
    return { success: false };
  }
};

// Blacklist a token
exports.blacklistToken = async (token) => {
  await new BlacklistedToken({ token }).save();
};

// Check if token is blacklisted
exports.isTokenBlacklisted = async (token) => {
  const blacklistedToken = await BlackListedToken.findOne({ token });
  return !!blacklistedToken;
};
