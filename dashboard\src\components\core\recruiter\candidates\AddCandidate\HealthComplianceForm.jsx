import React, { useEffect, useRef } from "react";
import { useFormikContext } from "formik";
import SelectField from "../../../../common/Input/SelectionField";
import CalendarDropdownField from "../../../../common/Input/CalendarDropdownField";

const yesNoOptions = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
];

const covidOptions = [
  { label: "Fully Vaccinated", value: "fully" },
  { label: "Not Vaccinated", value: "none" },
  { label: "Exempt", value: "exempt" },
];

const HealthComplianceForm = ({ onValidationChange, setSubmitRef }) => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isValid,
    dirty,
    submitForm,
    setFieldValue,
  } = useFormikContext();

  const prevStatus = useRef(values.healthAndCompliance.covid19Status);

  // Notify parent for button enable/disable
  useEffect(() => {
    if (typeof onValidationChange === "function") {
      onValidationChange({ isValid, dirty });
    }
  }, [isValid, dirty, onValidationChange]);

  // Expose submitForm to parent
  useEffect(() => {
    if (typeof setSubmitRef === "function") {
      setSubmitRef(submitForm);
    }
  }, [setSubmitRef, submitForm]);

  // Reset date fields if parent select is changed to "no"/"none"/"exempt"
  useEffect(() => {
    if (
      prevStatus.current === "fully" &&
      values.healthAndCompliance.covid19Status !== "fully"
    ) {
      setFieldValue("healthAndCompliance.dateOfLastCovid19Dose", "");
    }
    prevStatus.current = values.healthAndCompliance.covid19Status;
    // eslint-disable-next-line
  }, [values.healthAndCompliance.covid19Status]);

  const showCovidFields = values.healthAndCompliance.covid19Status === "fully";

  return (
    <form
      className="grid grid-cols-1 md:grid-cols-2 gap-4"
      onSubmit={handleSubmit}
    >
      <SelectField
        label="COVID Vaccination Status"
        name="healthAndCompliance.covid19Status"
        value={values.healthAndCompliance.covid19Status}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={covidOptions}
        errorMessage={
          touched.healthAndCompliance?.covid19Status &&
          errors.healthAndCompliance?.covid19Status
        }
      />
      {showCovidFields && (
        <CalendarDropdownField
          label="Date of Dose"
          name="healthAndCompliance.dateOfLastCovid19Dose"
          // required={values.healthAndCompliance.covid19Status === "fully"}
          // disabled={values.healthAndCompliance.covid19Status !== "fully"}
          maxDate={new Date()}
          // errorMessage={
          //   touched.healthAndCompliance?.dateOfLastCovid19Dose &&
          //   errors.healthAndCompliance?.dateOfLastCovid19Dose
          // }
        />
      )}
      <SelectField
        label="Booster Received"
        name="healthAndCompliance.boosterReceived"
        value={values.healthAndCompliance.boosterReceived}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={yesNoOptions}
        errorMessage={
          touched.healthAndCompliance?.boosterReceived &&
          errors.healthAndCompliance?.boosterReceived
        }
      />
      {showCovidFields && (
        <SelectField
          label="Proof of Vaccination Available"
          name="healthAndCompliance.proofOfVaccinationAvailable"
          value={values.healthAndCompliance.proofOfVaccinationAvailable}
          onChange={handleChange}
          onBlur={handleBlur}
          required
          placeholder="Select"
          options={yesNoOptions}
          errorMessage={
            touched.healthAndCompliance?.proofOfVaccinationAvailable &&
            errors.healthAndCompliance?.proofOfVaccinationAvailable
          }
        />
      )}
      <SelectField
        label="Flu Vaccination"
        name="healthAndCompliance.fluVaccination"
        value={values.healthAndCompliance.fluVaccination}
        onChange={handleChange}
        onBlur={handleBlur}
        required
        placeholder="Select"
        options={yesNoOptions}
        errorMessage={
          touched.healthAndCompliance?.fluVaccination &&
          errors.healthAndCompliance?.fluVaccination
        }
      />
    </form>
  );
};

export default HealthComplianceForm;
